package com.mmt.hotels.clientgateway.transformer.response;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.gommt.hotels.orchestrator.model.request.common.GeoLocationDetails;
import com.gommt.hotels.orchestrator.model.response.listing.MediaDetails;
import com.gommt.hotels.orchestrator.model.response.listing.VideoDetails;
import com.gommt.hotels.orchestrator.model.response.ugc.ListingReviewDetails;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.ibm.icu.text.NumberFormat;
import com.mmt.hotels.clientgateway.businessobjects.*;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.constants.ControllerConstants;
import com.mmt.hotels.clientgateway.enums.*;
import com.mmt.hotels.clientgateway.helpers.PolyglotHelper;
import com.mmt.hotels.clientgateway.pms.CommonConfig;
import com.mmt.hotels.clientgateway.pms.MyPartnerConfig;
import com.mmt.hotels.clientgateway.request.DeviceDetails;
import com.mmt.hotels.clientgateway.request.FilterRange;
import com.mmt.hotels.clientgateway.request.MobLandingRequest;
import com.mmt.hotels.clientgateway.request.SearchCriteria;
import com.mmt.hotels.clientgateway.request.payment.AddOnNode;
import com.mmt.hotels.clientgateway.request.payment.InsuranceAddOnData;
import com.mmt.hotels.clientgateway.request.payment.TmInsuranceAddOn;
import com.mmt.hotels.clientgateway.response.CancellationTimeline;
import com.mmt.hotels.clientgateway.response.FCBenefit;
import com.mmt.hotels.clientgateway.response.Policy;
import com.mmt.hotels.clientgateway.response.PricingDetails;
import com.mmt.hotels.clientgateway.response.TotalPricing;
import com.mmt.hotels.clientgateway.response.*;
import com.mmt.hotels.clientgateway.response.availrooms.EMIAbridgeResponse;
import com.mmt.hotels.clientgateway.response.availrooms.HotelTag;
import com.mmt.hotels.clientgateway.response.availrooms.*;
import com.mmt.hotels.clientgateway.response.corporate.*;
import com.mmt.hotels.clientgateway.response.dayuse.rooms.DayUsePriceDetail;
import com.mmt.hotels.clientgateway.response.dayuse.rooms.DayUseRoomsResponse;
import com.mmt.hotels.clientgateway.response.dayuse.rooms.DayUseSlotPlan;
import com.mmt.hotels.clientgateway.response.filter.FilterObject;
import com.mmt.hotels.clientgateway.response.listingmap.MetaInfo;
import com.mmt.hotels.clientgateway.response.listingmap.Poi;
import com.mmt.hotels.clientgateway.response.moblanding.*;
import com.mmt.hotels.clientgateway.response.rooms.RatePlan;
import com.mmt.hotels.clientgateway.response.rooms.RoomHighlight;
import com.mmt.hotels.clientgateway.response.rooms.TagInfo;
import com.mmt.hotels.clientgateway.response.searchHotels.GeoLocation;
import com.mmt.hotels.clientgateway.response.searchHotels.Hotel;
import com.mmt.hotels.clientgateway.response.staticdetail.MediaInfo;
import com.mmt.hotels.clientgateway.response.thankyou.AmountDetail;
import com.mmt.hotels.clientgateway.response.thankyou.SelectedSpecialRequests;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.transformer.factory.FilterFactory;
import com.mmt.hotels.clientgateway.transformer.factory.SearchHotelsFactory;
import com.mmt.hotels.clientgateway.util.Currency;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.configurations.prime.GoTribeBenefitImageUrls;
import com.mmt.hotels.filter.Filter;
import com.mmt.hotels.filter.FilterGroup;
import com.mmt.hotels.model.clm.ClmPersuasion;
import com.mmt.hotels.model.enums.BNPLVariant;
import com.mmt.hotels.model.enums.InclusionCategory;
import com.mmt.hotels.model.gocash.GoCash;
import com.mmt.hotels.model.persuasion.peitho.request.hotelDetails.BlackBenefits;
import com.mmt.hotels.model.request.flyfish.OTA;
import com.mmt.hotels.model.response.HotelBenefitInfo;
import com.mmt.hotels.model.response.addon.InsuranceData;
import com.mmt.hotels.model.response.addon.InsuranceDetails;
import com.mmt.hotels.model.response.addon.TmInsuranceAddOns;
import com.mmt.hotels.model.response.addon.WidgetData;
import com.mmt.hotels.model.response.adtech.AdTechPlacementContext;
import com.mmt.hotels.model.response.altaccodata.AltAccoResponse;
import com.mmt.hotels.model.response.altaccodata.CardPayloadResponse;
import com.mmt.hotels.model.response.corporate.AutobookRequestorConfigBO;
import com.mmt.hotels.model.response.corporate.CorpTags;
import com.mmt.hotels.model.response.emi.Emi;
import com.mmt.hotels.model.response.flyfish.ConceptSummaryDTO;
import com.mmt.hotels.model.response.flyfish.SubConceptDTO;
import com.mmt.hotels.model.response.flyfish.TravellerRatingSummaryDTO;
import com.mmt.hotels.model.response.listpersonalization.GenericCardPayloadData;
import com.mmt.hotels.model.response.listpersonalization.PriceBucket;
import com.mmt.hotels.model.response.listpersonalization.SpokeCity;
import com.mmt.hotels.model.response.listpersonalization.ValueStaysData;
import com.mmt.hotels.model.response.persuasion.HotelTagType;
import com.mmt.hotels.model.response.persuasion.SelectiveHotelPersuasions;
import com.mmt.hotels.model.response.pricing.CancelPenalty;
import com.mmt.hotels.model.response.pricing.*;
import com.mmt.hotels.model.response.pricing.Inclusion;
import com.mmt.hotels.model.response.pricing.jsonviews.LongStayBenefits;
import com.mmt.hotels.model.response.prime.DoubleBlackValidateResponse;
import com.mmt.hotels.model.response.searchwrapper.*;
import com.mmt.hotels.model.response.staticdata.BbLatLong;
import com.mmt.hotels.model.response.staticdata.HotelResult;
import com.mmt.hotels.model.response.staticdata.VideoInfo;
import com.mmt.hotels.model.response.staticdata.poiinfo.POIInfo;
import com.mmt.hotels.model.response.txn.BookingMessageCard;
import com.mmt.hotels.model.response.txn.LoyaltyMessageResponse;
import com.mmt.hotels.model.response.txn.UserCard;
import com.mmt.hotels.pojo.response.CampaignPojo;
import com.mmt.hotels.util.PromotionalOfferType;
import com.mmt.model.AttributesFacility;
import com.mmt.model.Facility;
import com.mmt.model.FacilityGroup;
import com.mmt.model.SubAttributeFacility;
import com.mmt.model.*;
import com.mmt.propertymanager.config.PropertyManager;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.lang.reflect.Type;
import java.text.MessageFormat;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.mmt.hotels.clientgateway.constants.Constants.MyBiz_Assured;
import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.*;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.UPGRADE_RATE_PLAN_ORIGINAL_PRICE_GI;
import static com.mmt.hotels.clientgateway.enums.ExperimentKeys.HCARDV2;
import static java.util.Comparator.comparingInt;

@Component
public class CommonResponseTransformer {

	private List<String> safetyCategories;

	private List<String> hotelCategoryTypesPriority;

	private List<String> altAccoCategoryTypesPriority;

	private Map<String, Integer> mediaLimitMap;

	@Value("${listing.hotel.media.limit}")
	String listingHotelMediaLimit;

	@Value("${allowed.Amount.Delta}")
	int allowedAmountDelta;

	@Value("${hotel.category.highest.priority}")
	private String highestPriorityCategory;

	@Value("${hotel.category.selection.algo.version}")
	private int categorySelectionAlgoVersion;

	@Value("${property.rules.max.count}")
	private int propertyRulesMaxCount;

	@Value("${hotel.categories.max.count.allowed}")
	private int maxCategoriesAllowed;

	@Value("#{'${intl.nr.supplier.exclusion}'.split(',')}")
	private List<String> intlNrSupplierExclusionList;

	private Map<String, String> categoryTextToCategoryTypeMap;

	private Map<String, String> categoryTypeToCategoryTextMap;

	@Autowired
	private PoliciesResponseTransformer policyResponseTransformer;

	private static Gson gson = new Gson();

	@Value("#{'${corp.segments}'.split(',')}")
	private Set<String> corpSegments;

	@Value("#{'${detail.persuasion.card.ids.gi}'.split(',')}")
	private List<String> detailPersuasionCardIds;

	@Value("#{'${moblanding.card.ids.gi}'.split(',')}")
	private List<String> mobLandingCardIds;

	@Autowired
	private PropertyManager propManager;

	@Autowired
	private Utility utility;

	@Autowired
	private ObjectMapperUtil objectMapperUtil;


	@Autowired
	private PolyglotService polyglotService;

	@Autowired
	SearchHotelsFactory searchHotelsFactory;

	@Autowired
	PolyglotHelper polyglotHelper;

	@Autowired
	private FilterFactory filterFactory;

	@Value("${value.stay.icon}")
	private String iconUrl;
	@Value("${value.stay.icon.gcc}")
	private String iconUrlGcc;
	@Value("${value.stay.background}")
	private String valueStayBackground;


	private Map<String, Map<String, PersuasionResponse>> persuasionResponseMap = null;
	private String mySafetyDataPolyglot;
	private Map<String, HotelCategoryDataWeb> hotelCategoryDataWebMap = null;
	private String mobgenJsonBO;
	private Map<String, Map<String, MobgenJsonBO>> mobgenJsonBOMap = null;
	private String mobgenStringsBO;
	private Map<String, MobgenStringsBO> mobgenStringsBOMap = null;
	private Map<String, HotelCategoryDataWeb> hotelCategoryDataWebMapNew = null;
	private Map<String, Map<String, HotelCategoryData>> hotelCategoryDataMap = null;
	private Map<String, Map<String, HotelCategoryData>> hotelCategoryDataMapModified = null;
	private Map<String, HotelCategoryDataWeb> hotelCategoryDataWebMapModified = null;

	private static final String TYPE_FCZPN = "FCZPN";

	private static final Logger logger = LoggerFactory.getLogger(CommonResponseTransformer.class);

	private LinkedHashMap<String, Double> discountParameters;
	private Map<String,String> mealPlanMapPolyglot;

	@PostConstruct
	public void init() {
		CommonConfig commonConfig = propManager.getProperty("commonConfig", CommonConfig.class);

		hotelCategoryTypesPriority = commonConfig.hotelCategoryPriority();
		altAccoCategoryTypesPriority = commonConfig.altAccoCategoryPriority();
		categoryTextToCategoryTypeMap = commonConfig.categoryKeyToTextMap();
		safetyCategories = commonConfig.safetyCategories();
		categoryTypeToCategoryTextMap = MapUtils.invertMap(categoryTextToCategoryTypeMap);
		mediaLimitMap = gson.fromJson(listingHotelMediaLimit, new TypeToken<Map<String, Integer>>() {
		}.getType());
		mySafetyDataPolyglot = commonConfig.mySafetyData();
		mobgenJsonBO = commonConfig.mobgenJsonBO();
		mobgenStringsBO = commonConfig.mobgenStringsBO();
		hotelCategoryDataWebMapNew = commonConfig.hotelCategoryDataWebMapNew();
		hotelCategoryDataMap = commonConfig.hotelCategoryDataMap();

		mealPlanMapPolyglot = commonConfig.mealPlanMapPolyglot();
		commonConfig.addPropertyChangeListener("mealPlanMapPolyglot", evt -> mealPlanMapPolyglot = commonConfig.mealPlanMapPolyglot());

		MyPartnerConfig myPartnerProperty = propManager.getProperty("myPartnerConfig", MyPartnerConfig.class);
		discountParameters = (LinkedHashMap<String, Double>) myPartnerProperty.discountParameters();


	}

	public List<String> getHotelCategories(Set<String> hotelCategories, boolean isAltAcco) {

		if (CollectionUtils.isEmpty(hotelCategories)) {
			return null;
		}

		if (CollectionUtils.isNotEmpty(safetyCategories)) {
			Set<String> result = safetyCategories.stream()
					.distinct()
					.filter(hotelCategories::contains)
					.collect(Collectors.toSet());

			//return if any of the safety data is present and only send one category
			if (CollectionUtils.isNotEmpty(result)) {
				return Arrays.asList(result.iterator().next());
			}
		}

		List<String> applicableCategoryTexts = hotelCategories.stream().filter(categoryText -> categoryTextToCategoryTypeMap.containsKey(categoryText)).collect(Collectors.toList());
		if (CollectionUtils.isEmpty(applicableCategoryTexts))
			applicableCategoryTexts = new ArrayList<>();

		List<String> applicableCategoryTypes = applicableCategoryTexts.stream()
				.map(categoryText -> categoryTextToCategoryTypeMap.get(categoryText))
				.collect(Collectors.toList());
		if (isAltAcco) {
			applicableCategoryTypes.retainAll(altAccoCategoryTypesPriority);
			applicableCategoryTypes.sort(Comparator.comparing(categoryType -> altAccoCategoryTypesPriority.indexOf(categoryType)));
		} else {
			applicableCategoryTypes.retainAll(hotelCategoryTypesPriority);
			applicableCategoryTypes.sort(Comparator.comparing(categoryType -> hotelCategoryTypesPriority.indexOf(categoryType)));
		}

		LinkedHashSet<String> finalCategories = applicableCategoryTypes.stream()
				.map(categoryType -> categoryTypeToCategoryTextMap.get(categoryType))
				.collect(Collectors.toCollection(LinkedHashSet::new));

		if (finalCategories.size() < 2) {
			finalCategories.addAll(hotelCategories);
		}
		List<String> categories = new ArrayList<>(finalCategories);
		return categories.subList(0, Math.min(maxCategoriesAllowed, categories.size()));
	}

	public PropertyRules getImportantInfoSection(RequestInputBO inputBo) {
		PropertyRules propertyRules = new PropertyRules();
		propertyRules.setPropertyType(inputBo.getPropertyType());
		propertyRules.setDescription(polyglotService.getTranslatedData(ConstantsTranslation.PROPERTY_RULES_DISCLAIMER_TEXT));
		propertyRules.setRules(buildPropertyRules(inputBo));
		if (CollectionUtils.isEmpty(propertyRules.getRules())) {
			propertyRules.setRules(null);
			propertyRules.setNoRulesAvailable(true);
		}
		return propertyRules;
	}

	public List<Rules> buildPropertyRules(RequestInputBO inputBo) {
		List<Rules> rulesList = new ArrayList<>();

		// 1. notices
		if (CollectionUtils.isNotEmpty(inputBo.getNotices())) {
			for (Notices notice : inputBo.getNotices()) {
				if (StringUtils.isBlank(notice.getDescription()))
					continue;
				Rules propertyRule = new Rules();
				propertyRule.setIconType(IconType.DEFAULT.name());
				propertyRule.setTitle(notice.getDescription());
				rulesList.add(propertyRule);
				if (rulesList.size() == propertyRulesMaxCount)
					return rulesList;
			}
		}

		// 2. checkIn restriction & confirmation policy when available
		if (inputBo.getCheckinPolicy() != null && StringUtils.isNotBlank(inputBo.getCheckinPolicy().getDescription())) {
			Rules propertyRule = new Rules();
			propertyRule.setIconType(IconType.DEFAULT.name());
			propertyRule.setTitle(inputBo.getCheckinPolicy().getDescription());
			rulesList.add(propertyRule);
			if (rulesList.size() == propertyRulesMaxCount)
				return rulesList;
		}
		if (inputBo.getConfirmationPolicy() != null && StringUtils.isNotBlank(inputBo.getConfirmationPolicy().getDescription())) {
			Rules propertyRule = new Rules();
			propertyRule.setIconType(IconType.DEFAULT.name());
			propertyRule.setTitle(inputBo.getConfirmationPolicy().getDescription());

			rulesList.add(propertyRule);
			if (rulesList.size() == propertyRulesMaxCount)
				return rulesList;
		}

		// 3. Pah_with_cc + FC/NR text
		if (inputBo.isPahWithCC()) {
			Rules propertyRule = new Rules();
			propertyRule.setIconType(IconType.DEFAULT.name());
			if (BookedCancellationPolicyType.NR.name().equalsIgnoreCase(inputBo.getCancellationPolicyType())) {
				propertyRule.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.INTL_PAH_NON_REFUNDABLE_TEXT));
			} else if(SUPPLIER_INGO.equalsIgnoreCase(inputBo.getSupplierCode())){
				propertyRule.setTitle(MessageFormat.format(polyglotService.getTranslatedData(ConstantsTranslation.INTL_PAH_FREE_CANCELLATION_TEXT), inputBo.getCancellationDate()));
			}else{
				propertyRule.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.INTL_PAH_FC_NON_INGO_POLICY_TEXT));
			}
			rulesList.add(propertyRule);
			if (rulesList.size() == propertyRulesMaxCount)
				return rulesList;
		}

		// 4. must read policies
		if (CollectionUtils.isNotEmpty(inputBo.getMustReadRules())) {
			for (String mustRead : inputBo.getMustReadRules()) {
				Rules propertyRule = new Rules();
				propertyRule.setIconType(IconType.DEFAULT.name());
				propertyRule.setTitle(mustRead);
				rulesList.add(propertyRule);
				if (rulesList.size() == propertyRulesMaxCount)
					return rulesList;
			}
		}

		// 5. Intl NR text for suppliers other than ingo, expedia
		if (!Constants.DOM_COUNTRY.equalsIgnoreCase(inputBo.getCountryCode()) &&
				BookedCancellationPolicyType.NR.name().equalsIgnoreCase(inputBo.getCancellationPolicyType())) {
			boolean excluded = isSupplierExcluded(inputBo.getSupplierCode());
			if (!excluded) {
				Rules propertyRule = new Rules();
				propertyRule.setIconType(IconType.DEFAULT.name());
				propertyRule.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.INTL_NR_SUPPLIER_SPECIFIC_TEXT));
				rulesList.add(propertyRule);
				if (rulesList.size() == propertyRulesMaxCount)
					return rulesList;
			}
		}

		// 6. house rules
		if (inputBo.getHouseRules() != null) {
			List<Policy> houseRulePolices = policyResponseTransformer.buildHouseRules(inputBo.getHouseRules());
			if (CollectionUtils.isNotEmpty(houseRulePolices)) {
				for (Policy houseRulePolicy : houseRulePolices) {
					if (CollectionUtils.isNotEmpty(houseRulePolicy.getRules())) {
						for (String policy : houseRulePolicy.getRules()) {
							// must read & house rules may have some common rules, so ignore duplicates
							if (!checkIfRuleDuplicate(rulesList, policy)) {
								Rules propertyRule = new Rules();
								propertyRule.setIconType(IconType.DEFAULT.name());
								propertyRule.setTitle(policy);
								rulesList.add(propertyRule);
								if (rulesList.size() == propertyRulesMaxCount)
									return rulesList;
							}
						}
					}
				}
			}
		}

		return rulesList;
	}

	public boolean checkIfRuleDuplicate(List<Rules> rulesList, String currentRule) {
		Optional<Rules> duplicateRule = rulesList.stream().filter(rule -> currentRule.equalsIgnoreCase(rule.getTitle())).findAny();
		return duplicateRule.isPresent();
	}

	public boolean isSupplierExcluded(String supplierCode) {
		if (StringUtils.isNotBlank(supplierCode)) {
			for (String supplier : intlNrSupplierExclusionList) {
				if (supplierCode.startsWith(supplier))
					return true;
			}
		}
		return false;
	}


	public TotalPricing getTotalPricing(DisplayPriceBreakDown displayPriceBrkDwn, String countryCode, String payMode, boolean isCorp, String segmentId, Map<String, String> expData, boolean groupBookingFunnel,
										boolean cbrAvailable, LoyaltyMessageResponse loyaltyData, ClmPersuasion clmData, AmountDetail paidAmount, Integer charityAmount, double bnplConvFees,double insuranceAmount) {
		TotalPricing totalPricing = new TotalPricing();
		String controller = MDC.get(MDCHelper.MDCKeys.CONTROLLER.getStringValue());
		if (displayPriceBrkDwn != null) {
			totalPricing.setDetails(getPricingDetails(displayPriceBrkDwn, countryCode, payMode, isCorp, segmentId, expData, groupBookingFunnel,cbrAvailable, loyaltyData, clmData, paidAmount, charityAmount, bnplConvFees,insuranceAmount));
			totalPricing.setPinCodeMandatory(displayPriceBrkDwn.isPinCodeMandatory());
			totalPricing.setOffersAppliedText(getOffersAppliedText(totalPricing.getDetails(),displayPriceBrkDwn, expData, countryCode));
			if (utility.isDetailPageAPI(controller)) {
				totalPricing.setOffersInclusionsList(getOffersInclusionList(displayPriceBrkDwn, expData, loyaltyData, clmData, countryCode));
			}
			totalPricing.setCoupons(getCouponDetails(displayPriceBrkDwn));
			if (CollectionUtils.isEmpty(totalPricing.getCoupons())) {
				totalPricing.setNoCouponText(polyglotService.getTranslatedData(ConstantsTranslation.NO_COUPON_AVAILABLE_TEXT));
			}
			totalPricing.setEmiBankDetails(getEmiDetails(displayPriceBrkDwn));
			totalPricing.setPricingKey(displayPriceBrkDwn.getPricingKey());

		}
		if(B2C.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.IDCONTEXT.getStringValue()))){
			totalPricing.setCouponSubtext(polyglotService.getSafeTranslatedData(GIFT_CARD_TEXT_GI));
		}
		return totalPricing;
	}

	public List<PricingDetails> getPricingDetails(DisplayPriceBreakDown displayPriceBrkDwn, String countryCode, String payMode, boolean isCorp, String segmentId, Map<String, String> expData,
												  boolean groupBookingFunnel, boolean cbrAvailable, LoyaltyMessageResponse loyaltyData, ClmPersuasion clmData, AmountDetail paidAmount, Integer charityAmount, double bnplConvFees,double insuranceAmount) {
		if (displayPriceBrkDwn != null) {
			List<PricingDetails> pricingDetails = new ArrayList<>();
			buildInsuranceBreakup(pricingDetails, displayPriceBrkDwn);
			buildBaseFare(pricingDetails, displayPriceBrkDwn);
			buildBaseFareWithTax(pricingDetails, displayPriceBrkDwn, expData, groupBookingFunnel);
			buildTotalDiscounts(pricingDetails, displayPriceBrkDwn, isCorp, segmentId, expData);
			buildPriceAfterDiscount(pricingDetails, displayPriceBrkDwn);
			buildWallet(pricingDetails, displayPriceBrkDwn);
			buildTaxesAndServiceFee(pricingDetails, displayPriceBrkDwn, countryCode,expData,cbrAvailable, bnplConvFees);
			if (!Constants.DOM_COUNTRY.equalsIgnoreCase(countryCode)) {
				buildTCSAmount(pricingDetails, paidAmount, displayPriceBrkDwn, expData, countryCode);
			}
			if (Utility.isPahOnlyPaymode(payMode))
				buildAmountYouPayingNow(pricingDetails, displayPriceBrkDwn, payMode);

			buildTotalAmount(pricingDetails, displayPriceBrkDwn, payMode, expData, paidAmount, charityAmount, bnplConvFees, countryCode,insuranceAmount);
			if (Utility.gocashVariant(expData, StringUtils.equalsIgnoreCase(countryCode, DOM_COUNTRY))) {
				buildPayNow(pricingDetails, displayPriceBrkDwn);
				buildAfterCashbackPrice(pricingDetails, displayPriceBrkDwn, loyaltyData, clmData);
			}
			return pricingDetails;
		}
		return null;
	}

	/**
	 * This method update TCS amount in Price breakup for thank-you page
	 * @param pricingDetails
	 * @param paidAmount
	 * @param displayPriceBrkDwn
	 * @param expData
	 */
	private void buildTCSAmount(List<PricingDetails> pricingDetails, AmountDetail paidAmount,
								DisplayPriceBreakDown displayPriceBrkDwn, Map<String, String> expData,
								String countryCode) {
		// This check is added when TCS collection for IH has been moved to LOBs from payments.
		// Before this, payments team was collecting TCS on payments-checkout page and TCS amount on orch end is
		// calculated by subtracting displayPrice from actual amount paid by user.
		if (displayPriceBrkDwn.getTcsAmount() > 0.0d) {
			displayPriceBrkDwn.setDisplayPrice(displayPriceBrkDwn.getDisplayPrice() + displayPriceBrkDwn.getTcsAmount());
			PricingDetails tcsAmount = new PricingDetails();
			tcsAmount.setAmount(displayPriceBrkDwn.getTcsAmount());
			tcsAmount.setKey(Constants.TCS_AMOUNT_KEY);
			tcsAmount.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.TCS_AMOUNT_LABEL));
			tcsAmount.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
			pricingDetails.add(tcsAmount);
		} else {
			double totalDisplayAmount = displayPriceBrkDwn.getDisplayPrice();
			String controller = MDC.get(MDCHelper.MDCKeys.CONTROLLER.getStringValue());
			if (Utility.gocashVariant3(expData, StringUtils.equalsIgnoreCase(countryCode, DOM_COUNTRY)) && checkGoCashAmount(displayPriceBrkDwn) && !utility.isReviewPageAPI(controller)) {
				totalDisplayAmount = totalDisplayAmount - displayPriceBrkDwn.getCouponInfo().getHybridDiscounts().get(Constants.GOCASH_KEY);
			}
			if (paidAmount != null && (totalDisplayAmount + allowedAmountDelta) < paidAmount.getAmount()) {
				PricingDetails tcsAmount = new PricingDetails();
				tcsAmount.setAmount(paidAmount.getAmount() - totalDisplayAmount);
				tcsAmount.setLabel(Constants.TCS_AMOUNT_LABEL);
				tcsAmount.setKey(Constants.TCS_AMOUNT_KEY);
				tcsAmount.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
				pricingDetails.add(tcsAmount);
			}
		}
	}

	private void buildAmountYouPayingNow(List<PricingDetails> pricingDetails, DisplayPriceBreakDown displayPriceBrkDwn, String payMode) {
		PricingDetails totalAmount = new PricingDetails();
		totalAmount.setAmount(0.0d); // pah case
		totalAmount.setKey(Constants.AMOUNT_YOU_PAYING_NOW_KEY);
		totalAmount.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.AMOUNT_YOU_PAYING_NOW_LABEL));
		if (!Utility.isPahWithCCPaymode(payMode))
			totalAmount.setSubLine(polyglotService.getTranslatedData(ConstantsTranslation.NO_CARD_REQUIRED_SUBLINE));
		totalAmount.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
		pricingDetails.add(totalAmount);
	}

	public List<Coupon> getCouponDetails(DisplayPriceBreakDown displayPriceBrkDwn) {
		if (displayPriceBrkDwn.getCouponInfo() != null) {
			List<Coupon> coupons = new ArrayList<>();
			Coupon coupon = new Coupon();
			coupon.setCode(displayPriceBrkDwn.getCouponInfo().getCouponCode());
			coupon.setCouponAmount(displayPriceBrkDwn.getCouponInfo().getDiscountAmount() != null ? displayPriceBrkDwn.getCouponInfo().getDiscountAmount() : 0.0);
			if(coupon.getCouponAmount() <= 0.0d && displayPriceBrkDwn.getCouponInfo() != null && displayPriceBrkDwn.getCouponInfo().getHybridDiscounts()!= null &&
					displayPriceBrkDwn.getCouponInfo().getHybridDiscounts() != null && displayPriceBrkDwn.getCouponInfo().getHybridDiscounts().containsKey("Instant")) {
				coupon.setCouponAmount(displayPriceBrkDwn.getCouponInfo().getHybridDiscounts().get("Instant"));
			}
			coupon.setDescription(displayPriceBrkDwn.getCouponInfo().getDescription());
			coupon.setAutoApplicable(displayPriceBrkDwn.getCouponInfo().isAutoApplicable());
			coupon.setBnplAllowed(displayPriceBrkDwn.getCouponInfo().getBnplAllowed() != null ? displayPriceBrkDwn.getCouponInfo().getBnplAllowed() : false);
			coupon.setTncUrl(displayPriceBrkDwn.getCouponInfo().getTncUrl());
			coupon.setDisabled(displayPriceBrkDwn.getCouponInfo().isDisabled());
			coupon.setSubDescription(displayPriceBrkDwn.getCouponInfo().getSubDescription());
			coupon.setPromoIconLink(displayPriceBrkDwn.getCouponInfo().getPromoIconLink());
			coupon.setSuccessMessage(displayPriceBrkDwn.getCouponInfo().getSuccessMessage());
			coupon.setBankOffer(displayPriceBrkDwn.getCouponInfo().isBankOffer());
			coupon.setCtaBankText(displayPriceBrkDwn.getCouponInfo().getCtaBankText());
			coupon.setCtaBankUrl(displayPriceBrkDwn.getCouponInfo().getCtaBankUrl());
			coupon.setGiftCardAllowed(displayPriceBrkDwn.getCouponInfo().getGiftCardAllowed());
			coupons.add(coupon);

			return coupons;
		}
		return null;
	}

	public EMIDetail getEmiDetails(DisplayPriceBreakDown displayPriceBrkDwn) {
		if (displayPriceBrkDwn.getEmiDetails() != null) {
			EMIDetail emiDetails = new EMIDetail();
			Emi emi = displayPriceBrkDwn.getEmiDetails();
			emiDetails.setBankId(emi.getBankId());
			emiDetails.setBankName(emi.getBankName());
			emiDetails.setAmount(Double.valueOf(emi.getEmiAmount()));
			emiDetails.setInterestRate(Double.valueOf(emi.getInterestRate()));
			emiDetails.setPayOption(emi.getPayOption());
			emiDetails.setTenure(emi.getTenure());
			emiDetails.setTotalCost(emi.getTotalCost());
			emiDetails.setTotalInterest(emi.getTotalInterest());
			emiDetails.setType(emi.getEmiType());
			emiDetails.setTaxIncluded(emi.isTaxIncluded());
			emiDetails.setTermType(emi.getTermType());
			emiDetails.setInterestType(emi.getInterestType());
			return emiDetails;
		}
		return null;
	}

	private void buildBaseFare(List<PricingDetails> pricingDetails, DisplayPriceBreakDown displayPriceBrkDwn) {
		if (displayPriceBrkDwn.getBasePrice() > 0.0d) {
			PricingDetails baseFare = new PricingDetails();
			baseFare.setAmount(displayPriceBrkDwn.getBasePrice());
			baseFare.setKey(Constants.BASE_FARE_KEY);
			baseFare.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.BASE_FARE_LABEL));
			baseFare.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
			pricingDetails.add(baseFare);
		}
	}

	private void buildBaseFareWithTax(List<PricingDetails> pricingDetails, DisplayPriceBreakDown displayPriceBrkDwn, Map<String, String> expData, boolean groupBookingFunnel) {
		if (displayPriceBrkDwn.getNonDiscountedPrice() > 0.0d && utility.isExpPdoPrnt(expData) && groupBookingFunnel) {
			PricingDetails baseFareWithTax = new PricingDetails();
			baseFareWithTax.setAmount(displayPriceBrkDwn.getNonDiscountedPrice()+(displayPriceBrkDwn.isTaxIncluded()?0:displayPriceBrkDwn.getTotalTax()));
			baseFareWithTax.setKey(Constants.BASE_FARE_WITH_TAX_KEY);
			baseFareWithTax.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.BASE_FARE_WITH_TAX_LABEL));
			baseFareWithTax.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
			pricingDetails.add(baseFareWithTax);
		}
	}

	public void buildInsuranceBreakup(List<PricingDetails> priceDetails, DisplayPriceBreakDown displayPriceBreakDown) {
		if (MapUtils.isNotEmpty(displayPriceBreakDown.getInsuranceBreakupMap())) {
			PricingDetails insurance = new PricingDetails();
			insurance.setKey(Constants.TOTAL_INSURANCE);
			insurance.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.INSURANCE_LABEL));
			insurance.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
			List<PricingDetails> breakup = new ArrayList<>();
			double totalAmount = 0.0d;
			for (Map.Entry<String, InsuranceDetails> entry : displayPriceBreakDown.getInsuranceBreakupMap().entrySet()) {
				PricingDetails i = new PricingDetails();
				i.setAmount(entry.getValue().getAmount());
				totalAmount += i.getAmount();
				i.setLabel(entry.getValue().getDisplayLabel());
				i.setKey(Constants.INSURANCE_AMOUNT);
				breakup.add(i);
			}
			insurance.setAmount(totalAmount);
			insurance.setBreakup(breakup);
			priceDetails.add(insurance);
		}
	}
//Mohit Fare breakup
	private void buildTotalAmount(List<PricingDetails> pricingDetails, DisplayPriceBreakDown displayPriceBrkDwn,
								  String payMode, Map<String, String> expData, AmountDetail paidAmount, Integer charityAmount,
								  double bnplConvFees, String countryCode, double insuranceAmount) {
		PricingDetails totalAmount = new PricingDetails();
		double totalDisplayAmount = displayPriceBrkDwn.getDisplayPrice();
		String controller = MDC.get(MDCHelper.MDCKeys.CONTROLLER.getStringValue());
		if (Utility.gocashVariant3(expData, StringUtils.equalsIgnoreCase(countryCode, DOM_COUNTRY)) && checkGoCashAmount(displayPriceBrkDwn) && !utility.isReviewPageAPI(controller)){
			totalDisplayAmount = totalDisplayAmount - displayPriceBrkDwn.getCouponInfo().getHybridDiscounts().get(Constants.GOCASH_KEY);
		}
		// This condition should be removed once we have completely shifted to 1st one i.e. TCS call out on review page.
		if (paidAmount != null && paidAmount.getAmount() > (totalDisplayAmount + allowedAmountDelta)) {
			totalDisplayAmount = paidAmount.getAmount();
		}

		if((paidAmount == null || paidAmount.getAmount()==0) && charityAmount!=null) {
			totalDisplayAmount += charityAmount;
		}
		if (!utility.isExperimentTrue(expData,Constants.INSURANCE_AMOUNT_DISABLED_DATA) && insuranceAmount > 0.0) {
			totalDisplayAmount += insuranceAmount;
		}
		totalAmount.setAmount(totalDisplayAmount);
		if (bnplConvFees > 0.0) {
			totalAmount.setAmount(totalAmount.getAmount() + bnplConvFees);
		}

		totalAmount.setKey(Constants.TOTAL_AMOUNT_KEY);
		totalAmount.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
		if (Utility.isPahOnlyPaymode(payMode)) {
			totalAmount.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.AMOUNT_YOU_PAYING_AT_HOTEL_LABEL));
		} else if (utility.isReviewPageAPI(controller) && Utility.gocashVariant(expData, StringUtils.equalsIgnoreCase(countryCode, DOM_COUNTRY)) && checkGoCashAmount(displayPriceBrkDwn)) {
			totalAmount.setLabel(Constants.PAY_NOW_LABEL);
		} else {
			totalAmount.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.TOTAL_AMOUNT_LABEL));
		}
		totalAmount.setSubTitle("Includes taxes and fees"); // This text is needed by GI
		pricingDetails.add(totalAmount);
	}

	public void updateTotalAmountInHotelierCurrency(List<PricingDetails> pricingDetails, String payMode, String userCurrency, String supplierCurrency, double convFactor) {

		String region = MDC.get(MDCHelper.MDCKeys.REGION.getStringValue());
		if (Utility.isPahOnlyPaymode(payMode) && !userCurrency.equalsIgnoreCase(supplierCurrency) && !Constants.AE.equalsIgnoreCase(region)) {
			for (PricingDetails priceDetail : pricingDetails) {
				if (Constants.TOTAL_AMOUNT_KEY.equalsIgnoreCase(priceDetail.getKey())) {
					priceDetail.setHotelierCurrencyCode(supplierCurrency);
					priceDetail.setHotelierCurrencyAmount(Utility.round(priceDetail.getAmount() * convFactor, 2));
					break;
				}
			}
		}
	}

	private void buildTaxesAndServiceFee(List<PricingDetails> pricingDetails, DisplayPriceBreakDown displayPriceBrkDwn, String countryCode, Map<String,
			String> expData,boolean cbrAvailable, double bnplConvFees) {
		double totalTaxesAndSrvcFee = 0.0d;
		List<PricingDetails> taxesBreakup = new ArrayList<>();
		if (displayPriceBrkDwn.getHotelTax() > 0.0d) {
			double hotelTaxAmount = displayPriceBrkDwn.getHotelTax();
			if (displayPriceBrkDwn.getHotelServiceCharge() > 0.0d) {
				hotelTaxAmount -= displayPriceBrkDwn.getHotelServiceCharge();
				PricingDetails serviceCharge = new PricingDetails();
				serviceCharge.setAmount(displayPriceBrkDwn.getHotelServiceCharge());
				serviceCharge.setKey(Constants.SERVICE_CHARGE_KEY);
				serviceCharge.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.SERVICE_CHARGE_LABEL));
				serviceCharge.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
				taxesBreakup.add(serviceCharge);
				totalTaxesAndSrvcFee += serviceCharge.getAmount();
			}
			PricingDetails hotelTax = new PricingDetails();
			hotelTax.setAmount(hotelTaxAmount);
			hotelTax.setKey(Constants.HOTEL_TAX_KEY);
			hotelTax.setLabel(Constants.DOM_COUNTRY.equalsIgnoreCase(countryCode) ? polyglotService.getTranslatedData(ConstantsTranslation.GST_LABEL) : polyglotService.getTranslatedData(ConstantsTranslation.HOTEL_TAX_LABEL));
			hotelTax.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
			taxesBreakup.add(hotelTax);
			totalTaxesAndSrvcFee += hotelTax.getAmount();
		}
		if (displayPriceBrkDwn.getMmtServiceCharge() > 0.0d) {
			PricingDetails serviceFee = new PricingDetails();
			serviceFee.setAmount(displayPriceBrkDwn.getMmtServiceCharge());
			if (bnplConvFees > 0.0) {
				serviceFee.setAmount(serviceFee.getAmount() + bnplConvFees);
			}
			serviceFee.setKey(Constants.SERVICE_FEES_KEY);
			serviceFee.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.SERVICE_FEES_LABEL));
			serviceFee.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
			taxesBreakup.add(serviceFee);
			totalTaxesAndSrvcFee += serviceFee.getAmount();
		}
		if (displayPriceBrkDwn.getAffiliateFee() > 0.0d) {
			PricingDetails affiliateFee = new PricingDetails();
			affiliateFee.setAmount(displayPriceBrkDwn.getAffiliateFee());
			affiliateFee.setKey(Constants.AFFILIATE_FEES_KEY);
			affiliateFee.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.AFFILIATE_FEES_LABEL));
			affiliateFee.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
			taxesBreakup.add(affiliateFee);
			totalTaxesAndSrvcFee += affiliateFee.getAmount();
		}
		if (totalTaxesAndSrvcFee > 0.0d) {
			PricingDetails taxesAndSrvcFee = new PricingDetails();
			taxesAndSrvcFee.setAmount(totalTaxesAndSrvcFee);
			taxesAndSrvcFee.setKey(Constants.TAXES_KEY);
			//Excluded Charges text to be shown above taxes for GCC on review page
			if (Utility.isGCC() && utility.isExperimentOn(expData, GEC)) {
				taxesAndSrvcFee.setSubLine(polyglotService.getTranslatedData(ConstantsTranslation.TAXES_EXCLUDED_TEXT_REVIEW_PAGE));
			}
			String controller = MDC.get(MDCHelper.MDCKeys.CONTROLLER.getStringValue());
			if ((ControllerConstants.LISTING_SEARCH_HOTELS.equalsIgnoreCase(controller) || ControllerConstants.DETAIL_SEARCH_ROOMS.equalsIgnoreCase(controller))
					&& (displayPriceBrkDwn.getHotelTax() > 0.0d || displayPriceBrkDwn.getMmtServiceCharge() > 0.0d)) {
				taxesAndSrvcFee.setLabel(polyglotService.getTranslatedData("TAXES_LISTING_DETAIL_LABEL"));
			} else if (displayPriceBrkDwn.getHotelTax() > 0.0d && displayPriceBrkDwn.getMmtServiceCharge() > 0.0d) {
				taxesAndSrvcFee.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.TAXES_LABEL)); // if taxes and service fee both there
			} else if (displayPriceBrkDwn.getHotelTax() > 0.0d) {
				taxesAndSrvcFee.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.HOTEL_TAX_LABEL)); // if only hotel taxes
			} else {
				taxesAndSrvcFee.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.SERVICE_FEES_LABEL)); // only service fees
			}
			taxesAndSrvcFee.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
			if(!cbrAvailable) {
				// not to send the break up of taxes to client if cheapest buy rate is true for the rateplan selected
				taxesAndSrvcFee.setBreakup(taxesBreakup);
			}
			pricingDetails.add(taxesAndSrvcFee);
		}
	}

//    private void buildAffiliateFee(List<PricingDetails> pricingDetails, DisplayPriceBreakDown displayPriceBrkDwn) {
//    	if(displayPriceBrkDwn.getAffiliateFee() > 0.0d) {
//    		PricingDetails affiliateFee = new PricingDetails();
//    		affiliateFee.setAmount(displayPriceBrkDwn.getAffiliateFee());
//    		affiliateFee.setKey(Constants.AFFILIATE_FEES_KEY);
//    		affiliateFee.setLabel(Constants.AFFILIATE_FEES_LABEL);
//    		affiliateFee.setType(Constants.PRICE_TYPE_SUM);
//    		pricingDetails.add(affiliateFee);
//    	}
//    }

	protected void buildWallet(List<PricingDetails> pricingDetails, DisplayPriceBreakDown displayPriceBrkDwn) {
		if (displayPriceBrkDwn.getWallet() > 0.0d) {
			List<PricingDetails> walletPricingBreakUp = null;
			if (displayPriceBrkDwn.getGoCashDetails() != null && displayPriceBrkDwn.getGoCashDetails().getGoCashApplicable() != null
					&& displayPriceBrkDwn.getGoCashDetails().isGoCashApplied()) {
				walletPricingBreakUp = new ArrayList<>();
				GoCash goCashApplicable = displayPriceBrkDwn.getGoCashDetails().getGoCashApplicable();
//				GoCash is Deprecated
//				if (goCashApplicable.getGoCash() != null && goCashApplicable.getGoCash() > 0.0d) {
//					PricingDetails wallet = new PricingDetails();
//					wallet.setAmount(goCashApplicable.getGoCash());
//					wallet.setKey(Constants.WALLET_KEY);
//					wallet.setLabel("goCash");
//					wallet.setType(polyglotService.getTranslatedData(PRICE_TYPE_SUM));
//					walletPricingBreakUp.add(wallet);
//				}

				if (goCashApplicable.getGoCashPlus() != null && goCashApplicable.getGoCashPlus() > 0.0d) {
					PricingDetails wallet = new PricingDetails();
					wallet.setAmount(goCashApplicable.getGoCashPlus());
					wallet.setKey(Constants.WALLET_KEY);
					wallet.setLabel("GoCash plus");
					wallet.setType(polyglotService.getTranslatedData(PRICE_TYPE_SUM));
					walletPricingBreakUp.add(wallet);
				}

				if (goCashApplicable.getNonPromotionalGoCash() != null && goCashApplicable.getNonPromotionalGoCash() > 0.0d) {
					PricingDetails wallet = new PricingDetails();
					wallet.setAmount(goCashApplicable.getNonPromotionalGoCash());
					wallet.setKey(Constants.WALLET_KEY);
					wallet.setLabel("NonPromotional GoCash");
					wallet.setType(polyglotService.getTranslatedData(PRICE_TYPE_SUM));
					walletPricingBreakUp.add(wallet);
				}
			}

			PricingDetails wallet = new PricingDetails();
			wallet.setAmount(displayPriceBrkDwn.getWallet());
			wallet.setKey(Constants.WALLET_KEY);
			wallet.setLabel("goCash");
			wallet.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_DIFF));
			wallet.setBreakup(walletPricingBreakUp);
			pricingDetails.add(wallet);
		}
	}

	private void buildPriceAfterDiscount(List<PricingDetails> pricingDetails, DisplayPriceBreakDown displayPriceBrkDwn) {
		String controller = MDC.get(MDCHelper.MDCKeys.CONTROLLER.getStringValue());
		double totalDiscountAmount = displayPriceBrkDwn.getMmtDiscount() + displayPriceBrkDwn.getBlackDiscount() + displayPriceBrkDwn.getCdfDiscount();
		if (!utility.isReviewPageAPI(controller)) {
			totalDiscountAmount += displayPriceBrkDwn.getWallet();
		}
		if (totalDiscountAmount > 0.0d) {
			double priceAfterDiscountAmount = displayPriceBrkDwn.getBasePrice() - totalDiscountAmount;
			PricingDetails priceAfterDiscount = new PricingDetails();
			priceAfterDiscount.setAmount(priceAfterDiscountAmount);
			priceAfterDiscount.setKey(Constants.PRICE_AFTER_DISCOUNT_KEY);
			priceAfterDiscount.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_AFTER_DISCOUNT_LABEL));
			priceAfterDiscount.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
			pricingDetails.add(priceAfterDiscount);
		}
	}
	private boolean checkGoCashAmount(DisplayPriceBreakDown displayPriceBrkDwn){
		return (displayPriceBrkDwn != null && displayPriceBrkDwn.getCouponInfo() != null && displayPriceBrkDwn.getCouponInfo().getHybridDiscounts() != null
				&& displayPriceBrkDwn.getCouponInfo().getHybridDiscounts().containsKey(Constants.GOCASH_KEY) &&
				displayPriceBrkDwn.getCouponInfo().getHybridDiscounts().get(Constants.GOCASH_KEY) > 0.0d);
	}

	private void buildPayNow(List<PricingDetails> pricingDetails, DisplayPriceBreakDown displayPriceBrkDwn) {
		String controller = MDC.get(MDCHelper.MDCKeys.CONTROLLER.getStringValue());
		double totalDisplayAmount = displayPriceBrkDwn.getDisplayPrice();
		//THIS HAS TO SHOW ONLY IN DETAILS PAGE
		if (!utility.isReviewPageAPI(controller)) {
			if (totalDisplayAmount > 0.0d && checkGoCashAmount(displayPriceBrkDwn)) {
				double payNowAmount = totalDisplayAmount;
				PricingDetails payNow = new PricingDetails();
				payNow.setAmount(payNowAmount);
				payNow.setKey(Constants.PAY_NOW_KEY);
				payNow.setLabel(Constants.PAY_NOW_LABEL);
				payNow.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
				payNow.setSubTitle(Constants.TAX_CASHBACK_LABEL);
				pricingDetails.add(payNow);
			}
		}
	}
	private void buildAfterCashbackPrice(List<PricingDetails> pricingDetails, DisplayPriceBreakDown displayPriceBrkDwn, LoyaltyMessageResponse loyaltyData, com.mmt.hotels.model.clm.ClmPersuasion clmData) {
		String controller = MDC.get(MDCHelper.MDCKeys.CONTROLLER.getStringValue());
		double totalDiscountAmount = 0.0d;
		if (checkGoCashAmount(displayPriceBrkDwn)) {
			totalDiscountAmount = totalDiscountAmount + displayPriceBrkDwn.getCouponInfo().getHybridDiscounts().get(Constants.GOCASH_KEY);
		}
		//Loyalty and clm cashback
		if (utility.isReviewPageAPI(controller)) {
			if (loyaltyData != null && loyaltyData.getWalletEarn() > 0.0d) {
				totalDiscountAmount += loyaltyData.getWalletEarn();
			}
			if (clmData != null && Double.valueOf(clmData.getBenefitAmount()) > 0.0d) {
				totalDiscountAmount += Double.valueOf(clmData.getBenefitAmount());
			}
		}
		if (totalDiscountAmount > 0.0d && checkGoCashAmount(displayPriceBrkDwn)) {
			double priceAfterCashbackAmount = displayPriceBrkDwn.getDisplayPrice() - totalDiscountAmount;
			PricingDetails afterCashbackPrice = new PricingDetails();
			afterCashbackPrice.setAmount(priceAfterCashbackAmount);
			afterCashbackPrice.setKey(Constants.AFTER_CASHBACK_PRICE_KEY);
			afterCashbackPrice.setLabel(Constants.AFTER_CASHBACK_PRICE_LABEL);
			afterCashbackPrice.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
			afterCashbackPrice.setSubTitle("Get ₹" + (int)totalDiscountAmount + " instant goCash");
			afterCashbackPrice.setSubTitleIcon(Constants.GOCASH_ICON);
			pricingDetails.add(afterCashbackPrice);
		}
	}

	public String getOffersAppliedText(List<PricingDetails> pricingDetails, DisplayPriceBreakDown displayPriceBrkDwn, Map<String, String> expData, String countryCode) {
		if (Utility.gocashVariant3(expData, StringUtils.equalsIgnoreCase(countryCode, DOM_COUNTRY)) && checkGoCashAmount(displayPriceBrkDwn) && (expData.containsKey("recomFlow") && expData.get("recomFlow").equals("false"))) {
			return Constants.OFFERS_CASHBACK_APPLIED_TEXT;
		}
		else {
			for (PricingDetails priceDetail : pricingDetails) {
				if (Constants.TOTAL_DISCOUNT_KEY.equalsIgnoreCase(priceDetail.getKey()) && priceDetail.getBreakup() != null && priceDetail.getBreakup().size() > 0) {
					return priceDetail.getBreakup().size() + OFFERS_APPLIED_TEXT;
				}
			}
		}
		return EMPTY_STRING;
	}

	public List<BookedInclusion> getOffersInclusionList(DisplayPriceBreakDown displayPriceBrkDwn,
										 Map<String, String> expData,  LoyaltyMessageResponse loyaltyData, com.mmt.hotels.model.clm.ClmPersuasion clmData, String countryCode) {
		List<BookedInclusion> offersInclusionsList = new ArrayList<>();
		String controller = MDC.get(MDCHelper.MDCKeys.CONTROLLER.getStringValue());
		double totalDiscountAmount = 0.0d;
		if (checkGoCashAmount(displayPriceBrkDwn)) {
			totalDiscountAmount = totalDiscountAmount + displayPriceBrkDwn.getCouponInfo().getHybridDiscounts().get(Constants.GOCASH_KEY);
		}
		//Loyalty and clm cashback
		if (utility.isReviewPageAPI(controller) && totalDiscountAmount > 0.0d) {
			if (loyaltyData != null && loyaltyData.getWalletEarn() > 0.0d) {
				totalDiscountAmount += loyaltyData.getWalletEarn();
			}
			if (clmData != null && Double.valueOf(clmData.getBenefitAmount()) > 0.0d) {
				totalDiscountAmount += Double.valueOf(clmData.getBenefitAmount());
			}
		}
		if (Utility.gocashVariant(expData, StringUtils.equalsIgnoreCase(countryCode, DOM_COUNTRY)) && totalDiscountAmount > 0.0d) {
			String goCashText = "";
			int goCashAmount = (int)totalDiscountAmount;
			if (Utility.gocashVariant2(expData, StringUtils.equalsIgnoreCase(countryCode, DOM_COUNTRY)) || (Utility.gocashVariant3(expData, StringUtils.equalsIgnoreCase(countryCode, DOM_COUNTRY)) && utility.isReviewPageAPI(controller))) {
				goCashText = String.format(EARN_GOCASH_INCLUSION, goCashAmount);
			} else if (Utility.gocashVariant3(expData, StringUtils.equalsIgnoreCase(countryCode, DOM_COUNTRY))) {
				goCashText = String.format(GOCASH_CAHSBACK_INCLUSION, goCashAmount);
			}
			BookedInclusion walletInclusion = new BookedInclusion();
			walletInclusion.setText(goCashText);
			walletInclusion.setCode(goCashText);
			walletInclusion.setIconType(IconType.DEFAULT);
			walletInclusion.setIUrl(GOCASH_ICON);
			walletInclusion.setCategory(GOCASH);
			offersInclusionsList.add(0, walletInclusion);
		}
		return offersInclusionsList;
	}

	private void buildTotalDiscounts(List<PricingDetails> pricingDetails, DisplayPriceBreakDown displayPriceBrkDwn, boolean isCorp, String segmentId, Map<String, String> expData) {
		double totalDiscountAmount = 0.0d;
		double totalOfferBasedDiscount = 0.0d;
		String controller = MDC.get(MDCHelper.MDCKeys.CONTROLLER.getStringValue());

		List<PricingDetails> priceBreakup = new ArrayList<>();

		/* Build OfferDetailMap from Pricer breakup if available */
		if (utility.isReviewPageAPI(controller) && utility.shouldDisplayOfferDiscountBreakup(expData)) {
			if (MapUtils.isNotEmpty(displayPriceBrkDwn.getOfferDiscountBreakup())) {
				List<Entry<PromotionalOfferType, Double>> list = displayPriceBrkDwn.getOfferDiscountBreakup().entrySet()
						.stream()
						.filter(entry -> entry.getValue() != null && entry.getValue() > 0.0d)
						.collect(Collectors.toList());
				if (CollectionUtils.isNotEmpty(list)) {
					for (Entry<PromotionalOfferType, Double> entry : list) {
						if (entry.getKey() != PromotionalOfferType.MIXED) {
							PricingDetails pd = new PricingDetails();
							pd.setAmount(entry.getValue());
							pd.setKey(entry.getKey().getName());
							pd.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
							pd.setLabel(polyglotService.getTranslatedData(entry.getKey().getName().concat("_DISC_LABEL")));
							totalOfferBasedDiscount = totalOfferBasedDiscount + entry.getValue();
							priceBreakup.add(pd);
						}
					}
				}
				totalDiscountAmount = totalDiscountAmount + totalOfferBasedDiscount;
			}
		}
		/* Build OfferDetailMap from Pricer breakup if available */

		if (displayPriceBrkDwn.getMmtDiscount() - totalOfferBasedDiscount > 0.0d) {
			PricingDetails mmtDiscount = new PricingDetails();
			mmtDiscount.setAmount(displayPriceBrkDwn.getMmtDiscount() - totalOfferBasedDiscount);
			mmtDiscount.setKey(Constants.MMT_DISCOUNT_KEY);
			mmtDiscount.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.HOTELIER_DISCOUNT_LABEL));
			mmtDiscount.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
			if (isCorp && corpSegments.contains(segmentId)) {
				mmtDiscount.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.HOTELIER_DISCOUNT_LABEL_CORP));
			}
			priceBreakup.add(mmtDiscount);
			totalDiscountAmount += mmtDiscount.getAmount();
		}

		if (displayPriceBrkDwn.getBlackDiscount() > 0.0d) {
			PricingDetails blackDiscount = new PricingDetails();
			blackDiscount.setAmount(displayPriceBrkDwn.getBlackDiscount());
			blackDiscount.setKey(Constants.BLACK_DISCOUNT_KEY);
			blackDiscount.setLabel((displayPriceBrkDwn.getGoTribeDiscountLabel() != null ? displayPriceBrkDwn.getGoTribeDiscountLabel() : "goTribe") + " Discount");
			blackDiscount.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
			priceBreakup.add(blackDiscount);
			totalDiscountAmount += blackDiscount.getAmount();
		}

		if (displayPriceBrkDwn.getCdfDiscount() > 0.0d) {
			PricingDetails cdfDiscount = new PricingDetails();
			cdfDiscount.setAmount(displayPriceBrkDwn.getCdfDiscount());
			cdfDiscount.setKey(Constants.CDF_DISCOUNT_KEY);
			cdfDiscount.setLabel(displayPriceBrkDwn.getCouponInfo() != null ? displayPriceBrkDwn.getCouponInfo().getCouponCode() : polyglotService.getTranslatedData(ConstantsTranslation.CDF_DISCOUNT_LABEL));
			cdfDiscount.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
			if (displayPriceBrkDwn.getMmtServiceCharge() > 0.0d && displayPriceBrkDwn.getCdfDiscount() > displayPriceBrkDwn.getMmtServiceCharge()) {
				List<PricingDetails> cdfCouponBreakup = new ArrayList<>();
				PricingDetails serviceFeeReversal = new PricingDetails();
				serviceFeeReversal.setKey(Constants.SERVICE_FEES_REVERSAL_KEY);
				serviceFeeReversal.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.SERVICE_FEES_REVERSAL_LABLE));
				serviceFeeReversal.setAmount(displayPriceBrkDwn.getMmtServiceCharge());
				serviceFeeReversal.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
				cdfCouponBreakup.add(serviceFeeReversal);
				PricingDetails effectiveCouponApplied = new PricingDetails();
				effectiveCouponApplied.setKey(Constants.EFFECTIVE_COUPON_APPLIED_KEY);
				effectiveCouponApplied.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.EFFECTIVE_COUPON_APPLIED_LABLE));
				effectiveCouponApplied.setAmount(displayPriceBrkDwn.getCdfDiscount() - displayPriceBrkDwn.getMmtServiceCharge());
				effectiveCouponApplied.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
				cdfCouponBreakup.add(effectiveCouponApplied);
				cdfDiscount.setBreakup(cdfCouponBreakup);
			}
			priceBreakup.add(cdfDiscount);
			totalDiscountAmount += cdfDiscount.getAmount();
		}
		if (!utility.isReviewPageAPI(controller)) {
			if (displayPriceBrkDwn.getWallet() > 0.0d) {
				PricingDetails wallet = new PricingDetails();
				wallet.setAmount(displayPriceBrkDwn.getWallet());
				wallet.setKey(Constants.WALLET_KEY);
				wallet.setLabel("goCash");
				wallet.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM));
				priceBreakup.add(wallet);
				totalDiscountAmount += wallet.getAmount();
			}
		}

		if (totalDiscountAmount > 0.0d) {
			PricingDetails totalDiscount = new PricingDetails();
			totalDiscount.setAmount(totalDiscountAmount);
			totalDiscount.setKey(Constants.TOTAL_DISCOUNT_KEY);
			totalDiscount.setLabel(polyglotService.getTranslatedData(ConstantsTranslation.TOTAL_DISCOUNT_LABEL));
			totalDiscount.setType(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_DIFF));
			totalDiscount.setBreakup(priceBreakup);
			pricingDetails.add(totalDiscount);
		}
	}

	public CancellationTimeline buildCancellationTimeline(com.mmt.hotels.model.response.pricing.CancellationTimeline cancellationTL) {
		if (cancellationTL != null) {
			CancellationTimeline cancellationTimeline = new CancellationTimeline();
			cancellationTimeline.setBookingDate(cancellationTL.getBookingDate());
			cancellationTimeline.setCancellationDate(cancellationTL.getCancellationDate());
			cancellationTimeline.setCancellationDateTime(cancellationTL.getCancellationDateTime());
			cancellationTimeline.setCardChargeDate(cancellationTL.getCardChargeDate());
			cancellationTimeline.setCardChargeDateTime(cancellationTL.getCardChargeDateTime());
			cancellationTimeline.setDateFormat(cancellationTL.getDateFormat());
			cancellationTimeline.setCardChargeText(cancellationTL.getCardChargeText());
			cancellationTimeline.setBookingAmountText(cancellationTL.getBookingAmountText());
			cancellationTimeline.setCheckInDate(cancellationTL.getCheckInDate());
			cancellationTimeline.setCheckInDateTime(cancellationTL.getCheckInDateTime());
			cancellationTimeline.setFreeCancellationText(cancellationTL.getFreeCancellationText());
			cancellationTimeline.setSubTitle(cancellationTL.getSubTitle());
			cancellationTimeline.setTitle(cancellationTL.getTitle());
			cancellationTimeline
					.setFreeCancellationBenefits(buildFreeCancellationBenefits(cancellationTL.getFreeCancellationBenefits()));
			return cancellationTimeline;
		}
		return null;
	}

	private List<FCBenefit> buildFreeCancellationBenefits(List<com.mmt.hotels.model.response.pricing.FCBenefit> freeCancellationBenefits) {
		if (CollectionUtils.isEmpty(freeCancellationBenefits)) {
			return null;
		}
		List<FCBenefit> benefitsForCG = new ArrayList<>();
		for (com.mmt.hotels.model.response.pricing.FCBenefit benefitHES : freeCancellationBenefits) {
			FCBenefit benefitCG = new FCBenefit();
			benefitCG.setText(benefitHES.getText());
			if (benefitHES.getType().equalsIgnoreCase(TYPE_FCZPN)) {
				benefitCG.setIconType(IconType.DOUBLETICK);
			} else {
				benefitCG.setIconType(IconType.SINGLETICK);
			}
			benefitsForCG.add(benefitCG);
		}
		return benefitsForCG;
	}

	public EMIAbridgeResponse getEmiAbridgeDetails(com.mmt.model.EMIAbridgeResponse emiRsp) {
		if (emiRsp != null) {
			EMIAbridgeResponse emiDetails = new EMIAbridgeResponse();
			emiDetails.setAmount(emiRsp.getAmount());
			emiDetails.setCoefficient(emiRsp.getCoefficient());
			emiDetails.setEmiAvailable(emiRsp.isEmiAvailable());
			emiDetails.setMessage(emiRsp.getMessage());
			emiDetails.setMinEligibleAmount(emiRsp.getMinEligibleAmount());
			return emiDetails;
		}
		return null;
	}

	public BNPLDetails buildBNPLDetails(boolean bnplApplicable, String bnplPersuasionMsg, String bnplPolicyText, String bnplNewVariantText,
										String bnplNewVariantSubText, boolean hotelOriginalBNPL, boolean showBnplCard, BNPLVariant bnplVariant,
										BNPLDisabledReason bnplDisabledReason, String countryCode, double bnplConvFees) {
		BNPLDetails bnplDetails = null;
		if (checkBnplApplicability(bnplApplicable, hotelOriginalBNPL, showBnplCard)) {
			 bnplDetails = new BNPLDetails();
			bnplDetails.setBnplApplicable(bnplApplicable);
			bnplDetails.setBnplPersuasionMsg(bnplPersuasionMsg);
			bnplDetails.setBnplPolicyText(bnplPolicyText);
			if (!Utility.isGCC())
				bnplDetails.setBnplVariant(bnplVariant!=null ? bnplVariant.name() : null);
			setBnplNewVariantDetails(bnplNewVariantSubText, bnplDetails);
			String bnplText = BNPLVariant.BNPL_AT_0.equals(bnplVariant)?polyglotService.getTranslatedData(BNPL_ZERO_VARIANT_TEXT_GI):BNPLVariant.BNPL_AT_1.equals(bnplVariant)?polyglotService.getTranslatedData(BNPL_NEW_VARIANT_TEXT_GI):bnplNewVariantText;
			bnplDetails.setBnplNewVariantText(bnplText);
			bnplDetails.setBnplConvFees(bnplConvFees);
		} else if (showInDisabledState(bnplApplicable,hotelOriginalBNPL,bnplDisabledReason)){
			bnplDetails = new BNPLDetails();
			setBnplDisabledStateDetails(getBnplDisabledMessage(bnplDisabledReason),bnplVariant,bnplDetails,countryCode);
		}
		return bnplDetails;
	}

	/* bnplDetails node is there in response based upon showBnplCard flag, for only GCC funnel. */
	public boolean checkBnplApplicability(boolean bnplApplicable, boolean hotelOriginalBNPL, boolean showBnplCard) {
		return bnplApplicable || (Constants.AE.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.REGION.getStringValue())) && showBnplCard && hotelOriginalBNPL);
	}

	private void setBnplNewVariantDetails(String bnplNewVariantSubText, BNPLDetails bnplDetails) {
		if (StringUtils.isNotBlank(bnplNewVariantSubText)) {
			bnplDetails.setBnplNewVariantSubText(bnplNewVariantSubText);
		}
	}

	/*CHECK IF BNPL NEEDS TO FLOW, THIS FUNCTION WILL DECIDE, IF BNPL IS IN DISABLED STATE*/
	public boolean showInDisabledState(boolean bnplApplicable, boolean hotelOriginalBNPL, BNPLDisabledReason bnplDisabledReason) {
		return (!bnplApplicable && hotelOriginalBNPL && bnplDisabledReason != null) || (bnplDisabledReason != null && bnplDisabledReason.equals(BNPLDisabledReason.ACTIVE_BOOKINGS_THRESHOLD));
	}

	public List<AffiliateFeeDetail> buildAffiliateFeeDetails(List<AffiliateFeeDetails> affiliateFeeDetailsList) {
		if (CollectionUtils.isEmpty(affiliateFeeDetailsList))
			return null;
		List<AffiliateFeeDetail> affiliateFeeDetails = new ArrayList<>();
		for (AffiliateFeeDetails affiliateFeeHES : affiliateFeeDetailsList) {
			AffiliateFeeDetail affiliateFeeDetailCG = new AffiliateFeeDetail();
			BeanUtils.copyProperties(affiliateFeeHES, affiliateFeeDetailCG);
			affiliateFeeDetails.add(affiliateFeeDetailCG);
		}
		return affiliateFeeDetails;
	}

	public List<AddOnNode> getAddons(List<com.mmt.hotels.model.response.addon.AddOnNode> addOns) {
		if (CollectionUtils.isNotEmpty(addOns)) {
			List<AddOnNode> addOnList = new ArrayList<>();
			for (com.mmt.hotels.model.response.addon.AddOnNode addOn : addOns) {
				AddOnNode addOnNode = new AddOnNode();
				addOnNode.setAddOnType(addOn.getAddOnType());
				addOnNode.setId(addOn.getId());
				addOnNode.setProductId(addOn.getProductId());
				addOnNode.setRpMultiplier(addOn.getRpMultiplier());
				addOnNode.setCategory(addOn.getCategory());
				addOnNode.setBucketId(addOn.getBucketId());
				addOnNode.setEssenceList(addOn.getEssenceList());
				addOnNode.setPaymentMode(addOn.getPaymentMode());
				addOnNode.setValidFrom(addOn.getValidFrom());
				addOnNode.setInclusion(addOn.getInclusion());
				addOnNode.setExpiry(addOn.getExpiry());
				addOnNode.setAvailableUnits(addOn.getAvailableUnits());
				addOnNode.setInsuranceData(buildInsuranceAddOnData(addOn.getInsuranceData(), addOn.getInsuarnceWidgetData()));
				addOnNode.setMobiusAddOn(addOn.isMobiusAddOn());
				addOnNode.setRefundPolicy(addOn.getRefundPolicy());
				addOnNode.setImageMap(addOn.getImageMap());
				addOnNode.setPrice(addOn.getPrice());
				addOnNode.setAlternateCurrencyPrice(addOn.getAlternateCurrencyPrice());
				if (Constants.EARLY_CHECKIN_CATEGORY.equalsIgnoreCase(addOn.getCategory())) {
					addOnNode.setTitle(MessageFormat.format(polyglotService.getTranslatedData(ConstantsTranslation.EARLY_CHECKIN_TITLE), addOn.getPrice()));
				} else {
					addOnNode.setTitle(addOn.getTitle());
				}
				addOnNode.setTncUrl(addOn.getTncUrl());
				addOnNode.setTnc(getTnC(addOn.getTnc()));
				addOnNode.setDescriptions(getAddonDescriptions(addOn.getDescriptions()));
				addOnNode.setAutoSelect(addOn.isAutoSelect());
				addOnList.add(addOnNode);
			}
			return addOnList;
		}
		return null;
	}

	//Preparing BHF response node received from HES
	public List<BhfPersuasion> getBhfPersuasions(List<com.mmt.hotels.pojo.response.detail.BhfPersuasion> bhfPersuasions){
		if(CollectionUtils.isNotEmpty(bhfPersuasions)){
			List<BhfPersuasion> bhfPersuasionList = new ArrayList<>();
			for(com.mmt.hotels.pojo.response.detail.BhfPersuasion bhfPersuasion: bhfPersuasions){
				BhfPersuasion bhfPersuasionNode = new BhfPersuasion();
				bhfPersuasionNode.setName(bhfPersuasion.getName());
				bhfPersuasionNode.setTextColor(bhfPersuasion.getTextColor());
				bhfPersuasionNode.setIcon(bhfPersuasion.getIcon());
				bhfPersuasionNode.setBgColor(bhfPersuasion.getBgColor());
				bhfPersuasionNode.setText(bhfPersuasion.getText());
				bhfPersuasionNode.setHeading(bhfPersuasion.getHeading());
				bhfPersuasionNode.setAdditionalText(bhfPersuasion.getAdditionalText());
				bhfPersuasionNode.setLeftCTA(bhfPersuasion.getLeftCTA());
				bhfPersuasionNode.setRightCTA(bhfPersuasion.getRightCTA());
				bhfPersuasionNode.setAdditionalTextColor(bhfPersuasion.getAdditionalTextColor());
				bhfPersuasionList.add(bhfPersuasionNode);
			}
			return bhfPersuasionList;
		}
		return null;
	}

	private InsuranceAddOnData buildInsuranceAddOnData(InsuranceData dataFromHES, WidgetData insuranceWidgetDataHES) {
		if (dataFromHES == null || CollectionUtils.isEmpty(dataFromHES.getTmInsuranceAddOns()))
			return null;
		InsuranceAddOnData data = new InsuranceAddOnData();
		data.setVendorLogo(dataFromHES.getVendorLogo());
		data.setTopHeading(dataFromHES.getTopHeading());
		data.setPriceBreakupText(dataFromHES.getPriceBreakupText());
		data.setIsMultiSelectable(dataFromHES.getMultiSelectable());
		data.setTmInsuranceAddOns(buildInsuranceAddOns(dataFromHES.getTmInsuranceAddOns()));
		data.setWidgetData(buildWidgetData(insuranceWidgetDataHES));
		return data;
	}

	private com.mmt.hotels.clientgateway.request.payment.WidgetData buildWidgetData(WidgetData widgetData) {
		com.mmt.hotels.clientgateway.request.payment.WidgetData widgetDataCG = null;
		if (widgetData != null && widgetData.getData() != null && widgetData.getUi() != null) {
			widgetDataCG = new com.mmt.hotels.clientgateway.request.payment.WidgetData();
			widgetDataCG.setData(widgetData.getData());
			widgetDataCG.setUi(widgetData.getUi());
		}
		return widgetDataCG;
	}

	private List<TmInsuranceAddOn> buildInsuranceAddOns(List<TmInsuranceAddOns> list) {
		if (CollectionUtils.isEmpty(list))
			return null;
		List<TmInsuranceAddOn> addOnlist = new ArrayList<>();
		for (TmInsuranceAddOns insurance : list) {
			TmInsuranceAddOn addOn = new TmInsuranceAddOn();
			addOn.setId(insurance.getId());
			addOn.setDescription(insurance.getDescription());
			addOn.setCurrency(insurance.getCurrency());
			addOn.setTncText(insurance.getTncText());
			addOn.setTncLink(insurance.getTncLink());
			addOn.setHeading(insurance.getHeading());
			addOn.setSubHeading(insurance.getSubHeading());
			addOn.setShortHeading(insurance.getShortHeading());
			addOn.setShortTextDesc(insurance.getShortTextDesc());
			addOn.setPostAttachMessage(insurance.getPostAttachMessage());
			addOn.setFeatureList(insurance.getFeatureList());
			addOn.setLargeIcon(insurance.getLargeIcon());
			addOn.setName(insurance.getName());
			addOn.setIncludedUnits(insurance.getIncludedUnits());
			addOn.setPriceTagLine(insurance.getPriceTagLine());
			addOn.setVendorNo(insurance.getVendorNo());
			addOn.setVendorLogo(insurance.getVendorLogo());
			addOn.setUnitVendorSgst(insurance.getUnitVendorSgst());
			addOn.setUnitVendorCgst(insurance.getUnitVendorCgst());
			addOn.setUnitVendorIgst(insurance.getUnitVendorIgst());
			addOn.setUnitType(insurance.getUnitType());
			addOn.setUnitPrice(insurance.getUnitPrice());
			addOn.setUnitBasePrice(insurance.getUnitBasePrice());
			addOn.setType(insurance.getType());
			addOn.setTotalPrice(insurance.getTotalPrice());
			addOn.setSumInsured(insurance.getSumInsured());
			addOnlist.add(addOn);
		}
		return addOnlist;
	}

	public SelectedSpecialRequests buildSelctedSpecialRequests(SpecialRequest specialRequestAvailable, SpecialRequest specialRequest) {
		SelectedSpecialRequests selectedSpecialRequest = null;

		Map<String, SpecialRequestCategory> availableSpecialRequests = specialRequestAvailable.getCategories()
				.stream()
				.collect(Collectors.toMap(SpecialRequestCategory::getCode, Function.identity()));

		selectedSpecialRequest = new SelectedSpecialRequests();
		selectedSpecialRequest.setRequests(new ArrayList<>());
		selectedSpecialRequest.setText(polyglotService.getTranslatedData(ConstantsTranslation.SPECIAL_REQUEST_TEXT));
		selectedSpecialRequest.setSubText(polyglotService.getTranslatedData(ConstantsTranslation.SPECIAL_REQUEST_SUBTEXT));
		for (SpecialRequestCategory selectedCategory : specialRequest.getCategories()) {
			StringBuilder sb = new StringBuilder();
			if ("109".equalsIgnoreCase(selectedCategory.getCode())) {
				if (selectedCategory.getValues() != null && selectedCategory.getValues().length > 0 && StringUtils.isNotBlank(selectedCategory.getValues()[0])) {
					sb.append(selectedCategory.getValues()[0]);
					selectedSpecialRequest.getRequests().add(sb.toString());
				}
				continue;
			}
			if (!availableSpecialRequests.containsKey(selectedCategory.getCode())) {
				logger.warn("Could not find category code {} but was selected while payment checkout. Skipping this category.", selectedCategory.getCode());
				continue;
			}
			sb.append(availableSpecialRequests.get(selectedCategory.getCode()).getName());
			if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(selectedCategory.getSubCategories()) &&
					selectedCategory.getSubCategories().get(0).getValues().length > 0
					&& StringUtils.isNotBlank(selectedCategory.getSubCategories().get(0).getValues()[0])) {
				sb.append(" : ").append(selectedCategory.getSubCategories().get(0).getValues()[0]);
			}
			selectedSpecialRequest.getRequests().add(sb.toString());
		}
		return selectedSpecialRequest;
	}

	private List<AddOnDescriptions> getAddonDescriptions(List<GenericCardPayloadData> descriptions) {
		if (CollectionUtils.isNotEmpty(descriptions)) {
			List<AddOnDescriptions> addonDescriptions = new ArrayList<>();
			for (GenericCardPayloadData desc : descriptions) {
				AddOnDescriptions addonDesc = new AddOnDescriptions();
				addonDesc.setCharityDescription(desc.getCharityDescription());
				addonDesc.setTitleText(desc.getTitleText());
				addonDesc.setItemIconType(desc.getItemIconType());
				addonDesc.setIconUrl(desc.getIconUrl());
				addonDescriptions.add(addonDesc);
			}
			return addonDescriptions;
		}
		return null;
	}

	private List<String> getTnC(Map<String, List<String>> tncMap) {
		if (MapUtils.isNotEmpty(tncMap)) {
			List<String> tncList = new ArrayList<>();
			for (Map.Entry<String, List<String>> entry : tncMap.entrySet()) {
				tncList.addAll(entry.getValue());
			}
		}
		return null;
	}

	public BlackInfo buildBlackInfo(com.mmt.hotels.model.response.mmtprime.BlackInfo bInfo) {
		if (bInfo != null) {
			BlackInfo blackInfo = new BlackInfo();
			blackInfo.setTierName(bInfo.getTierName());
			blackInfo.setTierNumber(bInfo.getTierNumber());
			blackInfo.setIconUrl(bInfo.getIconUrl());
			blackInfo.setIconUrlV2(bInfo.getIconUrl());
			blackInfo.setBenefitsTitle(bInfo.getBenefitsTitle());
			blackInfo.setLineBgColor(bInfo.getLineBgColor());
			blackInfo.setMsg(bInfo.getMsg());
			blackInfo.setInclusionsList(bInfo.getInclusionsList());
			blackInfo.setCtaUrl(bInfo.getCtaLink());
			blackInfo.setCtaText(bInfo.getCta());
			blackInfo.setCurrencyIcon(bInfo.getCurrencyIcon());
			return blackInfo;
		}
		return null;
	}

	public GoTribeInfo buildGoTribeInfo(com.mmt.hotels.model.response.mmtprime.BlackInfo bInfo, String title, String subTitle, boolean overrideBenefitImageUrls) {
		if (bInfo != null && CollectionUtils.isNotEmpty(bInfo.getInclusionsList())) {
			GoTribeInfo goTribeInfo = new GoTribeInfo();
			goTribeInfo.setTierName(bInfo.getTierName());
			goTribeInfo.setTierNumber(bInfo.getTierNumber());
			goTribeInfo.setGoTribeIconUrl(bInfo.getTierHeaderUrl());
			goTribeInfo.setGoTribeIconUrlV2(bInfo.getIconUrl());
			goTribeInfo.setBenefitsTitle(bInfo.getBenefitsTitle());
			goTribeInfo.setLineBgColour(bInfo.getLineBgColor());
			goTribeInfo.setTitle(title);
			goTribeInfo.setSubTitle(subTitle);
			goTribeInfo.setBenefits(bInfo.getInclusionsList());
			if (overrideBenefitImageUrls && bInfo.getGoTribeBenefitImageUrls() != null && CollectionUtils.isNotEmpty(goTribeInfo.getBenefits())) {
				GoTribeBenefitImageUrls goTribeBenefitImageUrls = bInfo.getGoTribeBenefitImageUrls();
				goTribeInfo.getBenefits().forEach(benefit -> {
					if (StringUtils.equalsIgnoreCase(InclusionCategory.ROOM_UPGRADE.getLeafCategory(), benefit.getCategory())
							|| StringUtils.equalsIgnoreCase(InclusionCategory.ROOM_UPGRADE.getLeafCategory(), benefit.getLeafCategory())) {
						benefit.setIconUrl(goTribeBenefitImageUrls.getRoomUpgrade());
					} else if (StringUtils.equalsIgnoreCase(InclusionCategory.MEAL_UPGRADE.getLeafCategory(), benefit.getCategory())
							|| StringUtils.equalsIgnoreCase(InclusionCategory.MEAL_UPGRADE.getLeafCategory(), benefit.getLeafCategory())) {
						benefit.setIconUrl(goTribeBenefitImageUrls.getMealUpgrade());
					} else if (StringUtils.equalsIgnoreCase(InclusionCategory.DISCOUNT.getLeafCategory(), benefit.getCategory())
							|| StringUtils.equalsIgnoreCase(InclusionCategory.DISCOUNT.getLeafCategory(), benefit.getLeafCategory())) {
						benefit.setIconUrl(goTribeBenefitImageUrls.getDiscount());
					} else if (StringUtils.equalsIgnoreCase(InclusionCategory.HOTEL_CREDIT.getLeafCategory(), benefit.getCategory())
							|| StringUtils.equalsIgnoreCase(InclusionCategory.HOTEL_CREDIT.getLeafCategory(), benefit.getLeafCategory())) {
						benefit.setIconUrl(goTribeBenefitImageUrls.getHotelCredit());
					} else if (StringUtils.equalsIgnoreCase(InclusionCategory.SPA.getLeafCategory(), benefit.getCategory())
							|| StringUtils.equalsIgnoreCase(InclusionCategory.SPA.getLeafCategory(), benefit.getLeafCategory())) {
						benefit.setIconUrl(goTribeBenefitImageUrls.getSpaDiscount());
					} else if (StringUtils.equalsIgnoreCase(InclusionCategory.FOOD_AND_BEVERAGE.getLeafCategory(), benefit.getCategory())
							|| StringUtils.equalsIgnoreCase(InclusionCategory.FOOD_AND_BEVERAGE.getLeafCategory(), benefit.getLeafCategory())) {
						benefit.setIconUrl(goTribeBenefitImageUrls.getFoodAndBeverages());
					} else if (StringUtils.equalsIgnoreCase(InclusionCategory.MY_CASH.getLeafCategory(), benefit.getCategory())
							|| StringUtils.equalsIgnoreCase(InclusionCategory.MY_CASH.getLeafCategory(), benefit.getLeafCategory())) {
						benefit.setIconUrl(goTribeBenefitImageUrls.getGoCash());
					}
					benefit.setIconUrlV2(benefit.getIconUrl());
				});
			}
			if (CollectionUtils.isNotEmpty(goTribeInfo.getBenefits()))
				goTribeInfo.setBenefits(utility.reorderBlackBenefits(goTribeInfo.getBenefits()));
			return goTribeInfo;
		}
		return null;
	}

	public AdditionalMandatoryCharges buildAdditionalCharges(AdditionalChargesBO additionalChargesBO, boolean showTransfersFeeTxt) {
		if (CollectionUtils.isEmpty(additionalChargesBO.getAdditionalFees()))
			return null;
		AdditionalMandatoryCharges additionalMandatoryCharges = new AdditionalMandatoryCharges();
		additionalMandatoryCharges.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.MANDATORY_CHARGES_SECTION_TITLE));
		String propertyType = "";
		if (additionalChargesBO.getPropertyType() != null) {
			propertyType = additionalChargesBO.getPropertyType().toLowerCase();
		}
		additionalMandatoryCharges.setSubTitle(MessageFormat.format(polyglotService.getTranslatedData(ConstantsTranslation.MANDATORY_CHARGES_SECTION_DESC), propertyType));

		double sum = additionalChargesBO.getAdditionalFees().stream().mapToDouble(mc -> mc.getAmount()).sum();
		additionalMandatoryCharges.setAmount(Utility.round(sum, 0));
		additionalMandatoryCharges.setCurrency(additionalChargesBO.getUserCurrency());
		additionalMandatoryCharges.setShowReadAndAgree(checkIfMandatoryChargesTooHigh(additionalMandatoryCharges.getAmount(), additionalChargesBO.getBookingAmount()));
		additionalMandatoryCharges.setBreakup(new ArrayList<>());

		for (AdditionalFees additionalFees : additionalChargesBO.getAdditionalFees()) {
			AdditionalMandatoryChargesBreakup breakup = new AdditionalMandatoryChargesBreakup();
			breakup.setTitle(additionalFees.getLeafCategory());
			if (StringUtils.isNotBlank(additionalFees.getName())) {
				breakup.setDescription(additionalFees.getName() + ". " + additionalFees.getDescription());
			} else {
				breakup.setDescription(additionalFees.getDescription());
			}
			breakup.setAmount(Utility.round(additionalFees.getAmount(), 0));
			breakup.setCurrency(additionalFees.getCurrency());
			if (StringUtils.isNotBlank(additionalFees.getCurrency()) && !additionalFees.getCurrency().equalsIgnoreCase(additionalChargesBO.getHotelierCurrency())) {
				breakup.setSubTitle(polyglotService.getTranslatedData(ConstantsTranslation.MANDATORY_CHARGES_BREAKUP_SUBTITLE));
				String region = MDC.get(MDCHelper.MDCKeys.REGION.getStringValue());
				if (!Constants.AE.equalsIgnoreCase(region)) {
					breakup.setHotelierCurrency(additionalChargesBO.getHotelierCurrency());
					breakup.setHotelierCurrencyAmount(Utility.round(additionalFees.getAmount() * additionalChargesBO.getConversionFactor(), 2));
				}
			}
			//Added Maldives transfer charges message
			if (showTransfersFeeTxt && TRANSFERS.equalsIgnoreCase(additionalFees.getLeafCategory())) {
				if (CITY_CODE_MALDIVES.equalsIgnoreCase(additionalChargesBO.getCityCode())) {
					breakup.setChargesMsg(polyglotService.getTranslatedData(ADDITIONAL_FEE_TRANSFERS_MSG));
				}
			}
			breakup.setAmountSubText(buildMandatoryChargesAmountSubText(additionalFees));
			additionalMandatoryCharges.getBreakup().add(breakup);
		}
		return additionalMandatoryCharges;
	}

	public boolean checkIfMandatoryChargesTooHigh(double mandatoryChargesTotalAmount, double bookingAmount) {
		return mandatoryChargesTotalAmount > (bookingAmount / 2);
	}

	private String buildMandatoryChargesAmountSubText(AdditionalFees additionalFees) {
		if (null == additionalFees.getPrice()) {
			return null;
		}
		StringBuilder sb = new StringBuilder();
		AdditionalFeesPrice additionalFeesPrice = additionalFees.getPrice();
		if (additionalFeesPrice.getDefaultPrice() != null) {
			sb.append(polyglotService.getTranslatedData(ConstantsTranslation.MANDATORY_CHARGES_DEFAULT_PRICE_TEXT));
		} else if (null != additionalFeesPrice.getPerStayRoom() && additionalFees.getTotalRooms() > 0) {
			sb.append(additionalFees.getTotalRooms())
					.append(Constants.ADDITIONAL_FEE_SUBTEXT_SPACE)
					.append(getRoomsSubText(additionalFees.getTotalRooms()))
					.append(Constants.ADDITIONAL_FEE_SUBTEXT_SEPARATOR)
					.append(additionalFees.getCurrency())
					.append(Constants.ADDITIONAL_FEE_SUBTEXT_SPACE)
					.append(getRoundedAmount(additionalFeesPrice.getPerStayRoom()));
		} else if (null != additionalFeesPrice.getPerStayAdult() && additionalFees.getTotalAdults() > 0) {
			sb.append(additionalFees.getTotalAdults())
					.append(Constants.ADDITIONAL_FEE_SUBTEXT_SPACE)
					.append(getAdultsSubText(additionalFees.getTotalAdults()))
					.append(Constants.ADDITIONAL_FEE_SUBTEXT_SEPARATOR)
					.append(additionalFees.getCurrency())
					.append(Constants.ADDITIONAL_FEE_SUBTEXT_SPACE)
					.append(getRoundedAmount(additionalFeesPrice.getPerStayAdult()));
			if (null != additionalFeesPrice.getPerStayChild() && additionalFees.getTotalChild() > 0) {
				sb.append(Constants.ADDITIONAL_FEE_SUBTEXT_LINE_SEPARATOR)
						.append(additionalFees.getTotalChild())
						.append(Constants.ADDITIONAL_FEE_SUBTEXT_SPACE)
						.append(getChildSubText(additionalFees.getTotalChild()))
						.append(Constants.ADDITIONAL_FEE_SUBTEXT_SEPARATOR)
						.append(additionalFees.getCurrency())
						.append(Constants.ADDITIONAL_FEE_SUBTEXT_SPACE)
						.append(getRoundedAmount(additionalFeesPrice.getPerStayChild()));
			}
		} else if (null != additionalFeesPrice.getPerNightRoom() && additionalFees.getTotalRooms() > 0) {
			sb.append(additionalFees.getApplicableDaysCount())
					.append(Constants.ADDITIONAL_FEE_SUBTEXT_SPACE)
					.append(getNightsSubText(additionalFees.getApplicableDaysCount()))
					.append(Constants.ADDITIONAL_FEE_SUBTEXT_SEPARATOR)
					.append(additionalFees.getTotalRooms())
					.append(Constants.ADDITIONAL_FEE_SUBTEXT_SPACE)
					.append(getRoomsSubText(additionalFees.getTotalRooms()))
					.append(Constants.ADDITIONAL_FEE_SUBTEXT_SEPARATOR)
					.append(additionalFees.getCurrency())
					.append(Constants.ADDITIONAL_FEE_SUBTEXT_SPACE)
					.append(getRoundedAmount(additionalFeesPrice.getPerNightRoom()));
		} else if (null != additionalFeesPrice.getPerNightAdult() && additionalFees.getTotalAdults() > 0) {
			sb.append(additionalFees.getApplicableDaysCount())
					.append(Constants.ADDITIONAL_FEE_SUBTEXT_SPACE)
					.append(getNightsSubText(additionalFees.getApplicableDaysCount()))
					.append(Constants.ADDITIONAL_FEE_SUBTEXT_SEPARATOR)
					.append(additionalFees.getTotalAdults())
					.append(Constants.ADDITIONAL_FEE_SUBTEXT_SPACE)
					.append(getAdultsSubText(additionalFees.getTotalAdults()))
					.append(Constants.ADDITIONAL_FEE_SUBTEXT_SEPARATOR)
					.append(additionalFees.getCurrency())
					.append(Constants.ADDITIONAL_FEE_SUBTEXT_SPACE)
					.append(getRoundedAmount(additionalFeesPrice.getPerNightAdult()));
			if (null != additionalFeesPrice.getPerNightChild() && additionalFees.getTotalChild() > 0) {
				sb.append(Constants.ADDITIONAL_FEE_SUBTEXT_LINE_SEPARATOR)
						.append(additionalFees.getApplicableDaysCount())
						.append(Constants.ADDITIONAL_FEE_SUBTEXT_SPACE)
						.append(getNightsSubText(additionalFees.getApplicableDaysCount()))
						.append(Constants.ADDITIONAL_FEE_SUBTEXT_SEPARATOR)
						.append(additionalFees.getTotalChild())
						.append(Constants.ADDITIONAL_FEE_SUBTEXT_SPACE)
						.append(getChildSubText(additionalFees.getTotalChild()))
						.append(Constants.ADDITIONAL_FEE_SUBTEXT_SEPARATOR)
						.append(additionalFees.getCurrency())
						.append(Constants.ADDITIONAL_FEE_SUBTEXT_SPACE)
						.append(getRoundedAmount(additionalFeesPrice.getPerNightChild()));
			}
		}

		return sb.toString();
	}


	public int getRoundedAmount(Double amount) {
		return Utility.round(amount, 0).intValue();
	}

	private String getChildSubText(int totalChild) {
		if (totalChild > 1)
			return polyglotService.getTranslatedData(ConstantsTranslation.ADDITIONAL_FEE_SUBTEXT_CHILDREN);
		return polyglotService.getTranslatedData(ConstantsTranslation.ADDITIONAL_FEE_SUBTEXT_CHILD);
	}

	private String getAdultsSubText(int totalAdults) {
		if (totalAdults > 1)
			return polyglotService.getTranslatedData(ConstantsTranslation.ADDITIONAL_FEE_SUBTEXT_ADULTS);
		return polyglotService.getTranslatedData(ConstantsTranslation.ADDITIONAL_FEE_SUBTEXT_ADULT);
	}

	private String getRoomsSubText(int totalRooms) {
		if (totalRooms > 1)
			return polyglotService.getTranslatedData(ConstantsTranslation.ADDITIONAL_FEE_SUBTEXT_ROOMS);
		return polyglotService.getTranslatedData(ConstantsTranslation.ADDITIONAL_FEE_SUBTEXT_ROOM);
	}

	private String getNightsSubText(int applicableDays) {
		if (applicableDays > 1)
			return polyglotService.getTranslatedData(ConstantsTranslation.ADDITIONAL_FEE_SUBTEXT_NIGHTS);
		return polyglotService.getTranslatedData(ConstantsTranslation.ADDITIONAL_FEE_SUBTEXT_NIGHT);
	}

	public CorpApprovalInfo buildCorpApprovalInfo(com.mmt.hotels.model.response.corporate.CorpMetaInfo corpMetaData) {
		if (corpMetaData == null) {
			return null;
		}
		CorpApprovalInfo corpMetaInfo = new CorpApprovalInfo();
		corpMetaInfo.setApprovalRequired(corpMetaData.isApprovalRequired());
		if (corpMetaData.getValidationPayload() != null) {
			corpMetaInfo.setBlockOopBooking(corpMetaData.getValidationPayload().getBlockOopBooking());
			corpMetaInfo.setBlockSkipApproval(corpMetaData.getValidationPayload().getBlockSkipApproval());
			corpMetaInfo.setWithinPolicy(corpMetaData.getValidationPayload().isWithinPolicy());
			corpMetaInfo.setFailureReasons(corpMetaData.getValidationPayload().getFailureReasons());
			corpMetaInfo.setApprovalType(corpMetaData.getValidationPayload().getApprovalType());
		}
		if (corpMetaData.getQuickCheckout() != null) {
			// sending quickCheckout flag from corp to client
			corpMetaInfo.setWalletQuickPayAllowed(corpMetaData.getQuickCheckout());
		}
		return corpMetaInfo;
	}

	public List<ApprovingManager> buildManagers(List<com.mmt.hotels.model.response.corporate.ApprovingManager> approvingManagers) {

		if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(approvingManagers)) {
			List<ApprovingManager> managerList = new ArrayList<>();

			for (com.mmt.hotels.model.response.corporate.ApprovingManager managerHES : approvingManagers) {
				ApprovingManager managerCG = new ApprovingManager();
				managerCG.setId(managerHES.getId());
				managerCG.setName(managerHES.getName());
				managerCG.setBusinessEmailId(managerHES.getBusinessEmailId());
				managerCG.setManagerType(managerHES.getManagerType());
				managerCG.setPhoneNumber(managerHES.getPhoneNumber());
				managerList.add(managerCG);
			}

			return managerList;
		}
		return null;
	}

	public List<CorpRateTags> buildTags(List<CorpTags> tags) {

		if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(tags)) {
			List<CorpRateTags> tagsCG = new ArrayList<>();

			for (CorpTags tagCB : tags) {
				CorpRateTags tagCG = new CorpRateTags();
				tagCG.setTagDescription(tagCB.getTagDescription());
				tagCG.setTagName(tagCB.getTagName());

				tagsCG.add(tagCG);
			}
			return tagsCG;
		}

		return null;
	}

	public List<RatePlan> buildRateplanList(List<RoomType> roomTypes, BNPLDetails bnplDetails, BNPLVariant bnplVariant, DisplayPriceBreakDown displayPriceBrkDwn,
											Map<String, String> expData,  LoyaltyMessageResponse loyaltyData, com.mmt.hotels.model.clm.ClmPersuasion clmData, String countryCode) {
		List<RatePlan> ratePlanList = new ArrayList<>();
		boolean bnplApplicable = false;
		if (bnplDetails != null)
			bnplApplicable = bnplDetails.isBnplApplicable();
		if (CollectionUtils.isNotEmpty(roomTypes)) {
			String confirmationPolicyType = null;
			RatePolicy confirmationPolicy = getConfirmationPolicy(roomTypes);
			if (confirmationPolicy != null) {
				confirmationPolicyType = confirmationPolicy.getValue();
			}
			for (RoomType roomType : roomTypes) {
				String roomCode = roomType.getRoomTypeCode();
				if (MapUtils.isNotEmpty(roomType.getRatePlanList())) {
					for (Entry<String, com.mmt.hotels.model.response.pricing.RatePlan> entry : roomType.getRatePlanList().entrySet()) {
						String ratePlanCode = entry.getKey();
						com.mmt.hotels.model.response.pricing.RatePlan ratePlanHES = entry.getValue();

						RatePlan rtPlan = new RatePlan();
						rtPlan.setRoomCode(roomCode);
						rtPlan.setCode(ratePlanCode);
						rtPlan.setCancellationTimeline(buildCancellationTimeline(ratePlanHES.getCancellationTimeline()));
						rtPlan.setCancellationPolicyTimeline(buildCancellationPolicyTimeline(ratePlanHES.getCancellationTimeline()));
						rtPlan.setCancellationPolicy(utility.transformCancellationPolicy(ratePlanHES.getCancelPenaltyList(), bnplApplicable, bnplVariant, confirmationPolicyType, polyglotService.getTranslatedData(ConstantsTranslation.CANCELLATION_POLICY_NR_NON_INSTANT_TEXT), null));
						updateCancelPolicyDescription(rtPlan, ratePlanHES.getCancelPenaltyList());
						ratePlanList.add(rtPlan);
					}
				}
			}
		}
		if (CollectionUtils.isNotEmpty(ratePlanList) && ratePlanList.size() > 0) {
			ratePlanList.get(0).setOffersInclusionsList(getOffersInclusionList(displayPriceBrkDwn, expData, loyaltyData, clmData, countryCode));
		}
		return ratePlanList;

	}

	/*
	 *  This method will return amenity displayName(facility Name,text in bracket)
	 *  Case 1: empty displayType -> facility name
	 *  Case 2: displayType -> 2 -> L0(L1) – L2’,L2’‘,L2’‘’
	 *  Case 3: displayType -> 1 -> L0(L1,L1',L1'’)
	 *
	 */
	public List<SelectRoomAmenities> getAmenities(List<FacilityGroup> amenities) {
		List<SelectRoomAmenities> amenitiesList = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(amenities)) {
			for (FacilityGroup fg : amenities) {
				SelectRoomAmenities selectRoomAmenities = new SelectRoomAmenities();
				selectRoomAmenities.setName(fg.getName());
				if (CollectionUtils.isNotEmpty(fg.getFacilities())) {
					List<SelectRoomFacility> selectRoomFacilities = new ArrayList<>();
					for (Facility facility : fg.getFacilities()) {
						SelectRoomFacility selectRoomFacility = new SelectRoomFacility();
						selectRoomFacility.setName(facility.getName());
						if (StringUtils.isNotEmpty(facility.getDisplayType())) {
							if ("1".equalsIgnoreCase(facility.getDisplayType())) {
								StringBuilder stringBuilder = new StringBuilder();
								for (AttributesFacility cf : facility.getChildAttributes()) {
									stringBuilder.append(cf.getName()).append(Constants.COMMA);
								}
								selectRoomFacility.setSubText(Constants.AMENITIES_OPEN_BRACE +
										StringUtils.chop(stringBuilder.toString()) + Constants.AMENITIES_CLOSING_BRACE);
							}
							if ("2".equalsIgnoreCase(facility.getDisplayType())) {
								StringBuilder stringBuilder = new StringBuilder();
								if (CollectionUtils.isNotEmpty(facility.getChildAttributes()) && facility.getChildAttributes().get(0) != null) {
									AttributesFacility childAttribute = facility.getChildAttributes().get(0);
									stringBuilder.append(childAttribute.getName())
											.append(Constants.SPACE)
											.append(Constants.HYPEN)
											.append(Constants.SPACE);
									if (CollectionUtils.isNotEmpty(childAttribute.getSubAttributes())) {
										for (SubAttributeFacility subAttributeFacility : childAttribute.getSubAttributes()) {
											stringBuilder.append(subAttributeFacility.getName()).append(Constants.COMMA);
										}
									}
								}
								selectRoomFacility.setSubText(Constants.AMENITIES_OPEN_BRACE +
										StringUtils.chop(stringBuilder.toString()) + Constants.AMENITIES_CLOSING_BRACE);
							}
						}
						selectRoomFacilities.add(selectRoomFacility);
					}
					selectRoomAmenities.setFacilities(selectRoomFacilities);
					selectRoomAmenities.setImages(fg.getImages());
				}
				amenitiesList.add(selectRoomAmenities);
			}
		}
		return amenitiesList;
	}

	public List<String> getHighlightedAmenities(List<FacilityGroup> highlightedAmenities) {
		List<String> hltAmnties = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(highlightedAmenities)) {
			for (FacilityGroup fg : highlightedAmenities) {
				if (CollectionUtils.isNotEmpty(fg.getFacilities())) {
					for (Facility facility : fg.getFacilities()) {
						hltAmnties.add(facility.getName());
					}
				}
			}
		}
		return hltAmnties;
	}

	public com.mmt.hotels.clientgateway.response.moblanding.ListPersonalizationResponse buildListPersonalizationResponse(com.mmt.hotels.pojo.listing.personalization.ListPersonalizationResponse listPersonalizationResponse, String client, LinkedHashMap<String,String> expDataMap, String idContext, String funnelSource, CommonModifierResponse commonModifierResponse) {
		if (listPersonalizationResponse == null || MapUtils.isEmpty(listPersonalizationResponse.getCardData())) {
			return null;
		}
		com.mmt.hotels.clientgateway.response.moblanding.ListPersonalizationResponse listPersonalizationResponseCG = new com.mmt.hotels.clientgateway.response.moblanding.ListPersonalizationResponse();
		listPersonalizationResponseCG.setExperimentId(listPersonalizationResponse.getExperimentId());
		listPersonalizationResponseCG.setCardData(buildCardData(listPersonalizationResponse.getCardData(), client,expDataMap, filterFactory.getFilterConfiguration(client, idContext, funnelSource, commonModifierResponse)));
		if (CollectionUtils.isEmpty(listPersonalizationResponseCG.getCardData()))
			return null;
		listPersonalizationResponseCG.setTrackText(buildTrackText(listPersonalizationResponseCG.getCardData()));
		listPersonalizationResponseCG.setMeta(buildMetaFromCardMetaInfo(listPersonalizationResponse.getMeta()));
		return listPersonalizationResponseCG;
	}

	/**
	 * Converts CardMetaInfo from the pojo package to Meta class from the clientgateway response package
	 * @param cardMetaInfo the CardMetaInfo object from com.mmt.hotels.pojo.listing.personalization package
	 * @return Meta object for com.mmt.hotels.clientgateway.response.moblanding.ListPersonalizationResponse
	 */
	public com.mmt.hotels.clientgateway.response.moblanding.Meta buildMetaFromCardMetaInfo(com.mmt.hotels.pojo.listing.personalization.CardMetaInfo cardMetaInfo) {
		if (cardMetaInfo == null) {
			return null;
		}
		
		// Create a new Meta object
		com.mmt.hotels.clientgateway.response.moblanding.Meta meta = new com.mmt.hotels.clientgateway.response.moblanding.Meta();

		try{
			// Extract SavedCardTrackingData from CardMetaInfo
			com.mmt.hotels.pojo.listing.personalization.SavedCardTrackingData savedCardTrackingData = cardMetaInfo.getSavedCardTracking();

			if (savedCardTrackingData != null) {
				// Create new SavedCardTracking object for the clientgateway response
				com.mmt.hotels.clientgateway.response.moblanding.SavedCardTracking savedCardTracking =
						new com.mmt.hotels.clientgateway.response.moblanding.SavedCardTracking();

				// Map userSavedCardCount (int -> Integer)
				savedCardTracking.setUserSavedCardCount(savedCardTrackingData.getUserSavedCardCount());

				// Map offerApplicable (boolean -> Boolean)
				savedCardTracking.setOfferApplicable(savedCardTrackingData.isOfferApplicable());

				// Map cardInfo (List<String> -> List<String>)
				savedCardTracking.setCardInfo(savedCardTrackingData.getCardInfo());

				// Set the converted SavedCardTracking on Meta
				meta.setSavedCardTracking(savedCardTracking);
			}
		}catch (Exception e) {
			logger.warn("Error while parsing meta data from CardMetaInfo: {}", e.getMessage());
		}

		return meta;
	}

	private String buildTrackText(List<CardData> cardData) {
		if (CollectionUtils.isNotEmpty(cardData)) {
			StringBuilder trackText = new StringBuilder();
			for (CardData cardDataObject : cardData) {
				if (cardDataObject.getCardInfo() != null) {
					trackText.append(cardDataObject.getCardInfo() .getId()).append(Constants.UNDERSCORE).append(cardDataObject.getCardInfo().getSubType()).append(Constants.UNDERSCORE).append("S").append(PIPE_SYMBOL);
				}
			}
			return trackText.toString();
		}
		return "";
	}

	private List<com.mmt.hotels.clientgateway.response.moblanding.CardData> buildCardData(Map<Integer, List<com.mmt.hotels.pojo.listing.personalization.CardData>> map, String client, LinkedHashMap<String,String> expDataMap, FilterConfiguration filterConfiguration) {
		if (MapUtils.isEmpty(map)) {
			return null;
		}
		List<com.mmt.hotels.clientgateway.response.moblanding.CardData> cardDataList = new ArrayList<>();
		for (Map.Entry mapElement : map.entrySet()) {
			List<com.mmt.hotels.pojo.listing.personalization.CardData> cardDataCB = (List<com.mmt.hotels.pojo.listing.personalization.CardData>) mapElement.getValue();
			com.mmt.hotels.clientgateway.response.moblanding.CardData cardDataCG = new com.mmt.hotels.clientgateway.response.moblanding.CardData();
			if (doNotSetCardData(cardDataCB, expDataMap)) {
				continue;
			}
			cardDataCG.setSequence((Integer) mapElement.getKey());
			cardDataList.add(cardDataCG);
			cardDataCG.setCardInfo(buildCardInfo(cardDataCB, client,expDataMap, filterConfiguration));
		}
		return cardDataList;
	}

	private boolean doNotSetCardData(List<com.mmt.hotels.pojo.listing.personalization.CardData> cardDataCB, LinkedHashMap<String, String> expDataMap) {
		String cardId = "";




		if (CollectionUtils.isNotEmpty(cardDataCB) && cardDataCB.get(0) != null) {
			if (expDataMap.containsKey(MULTIFILTER_COROUSAL) && utility.isExperimentTrue(expDataMap, MULTIFILTER_COROUSAL)){
				if (cardDataCB.get(0).getTemplateId() != null && Objects.equals(cardDataCB.get(0).getTemplateId(), "multifilter_corousal")){
					return false;
				}
			}
			com.mmt.hotels.pojo.listing.personalization.CardData cardDataObjectCB = getFirstNonMultifilterCard(cardDataCB);
			if (cardDataObjectCB == null) {
				return true;
			}
			if (StringUtils.isNotEmpty(cardDataObjectCB.getCardId()) && StringUtils.isNotEmpty(cardDataObjectCB.getCardSubType())) {
				cardId = cardDataObjectCB.getCardId() + "_"+ cardDataObjectCB.getCardSubType();
			} else if (StringUtils.isNotEmpty(cardDataObjectCB.getCardId()) && StringUtils.isEmpty(cardDataObjectCB.getCardSubType())) {
				cardId = cardDataObjectCB.getCardId();
			} else if (StringUtils.isEmpty(cardDataObjectCB.getCardId()) && StringUtils.isNotEmpty(cardDataObjectCB.getCardSubType())) {
				cardId = cardDataObjectCB.getCardSubType();
			} else {
				return true;
			}
		}
		if (CardTemplateId.BUSINESSIDENTIFICATION_CARD.name().equalsIgnoreCase(cardId)) {
			if (utility.showBusinessIdentificationCard(expDataMap)) {
				return false;
			} else {
				return true;
			}
		}
		if (StringUtils.isNotEmpty(cardId) && (detailPersuasionCardIds.contains(cardId) || mobLandingCardIds.contains(cardId))) {
			return false;
		}
		return true;
	}

	/**
	 * Safely retrieves the first CardData object that does not have "multifilter_corousal" as templateId.
	 * Returns null if no such card is found or if the input list is null/empty.
	 * 
	 * @param cardDataList List of CardData objects to search through
	 * @return First CardData with non "multifilter_corousal" templateId, or null if none found
	 */
	private com.mmt.hotels.pojo.listing.personalization.CardData getFirstNonMultifilterCard(
			List<com.mmt.hotels.pojo.listing.personalization.CardData> cardDataList) {
		
		if (CollectionUtils.isEmpty(cardDataList)) {
			return null;
		}
		
		for (com.mmt.hotels.pojo.listing.personalization.CardData cardData : cardDataList) {
			if (cardData != null && 
				(cardData.getTemplateId() == null || 
				 !Objects.equals(cardData.getTemplateId(), "multifilter_corousal"))) {
				return cardData;
			}
		}
		
		return null;
	}

	private List<CardInfo> buildCardInfoArray(List<com.mmt.hotels.pojo.listing.personalization.CardData> cardDataCB, String client, LinkedHashMap<String,String> expDataMap,FilterConfiguration filterConfiguration) {
		if (CollectionUtils.isEmpty(cardDataCB)) {
			return null;
		}
		List<CardInfo> cardInfoArray = new ArrayList<>();
		for (com.mmt.hotels.pojo.listing.personalization.CardData cardDataObjectCB : cardDataCB) {

			if (cardDataObjectCB.getTemplateId() != null && cardDataObjectCB.getTemplateId().equals("multifilter_corousal")) {
				cardInfoArray.add(transformCardData(cardDataObjectCB, client, expDataMap,filterConfiguration));
			}
		}
		return cardInfoArray;
	}

	public CardInfo buildCardInfo(List<com.mmt.hotels.pojo.listing.personalization.CardData> cardDataCB, String client,LinkedHashMap<String,String> expDataMap,FilterConfiguration filterConfiguration) {
		if (CollectionUtils.isEmpty(cardDataCB)) {
			return null;
		}

		CardInfo cardDataCG = new CardInfo();
		// Add cardInfoArray to the main cardInfo


		cardDataCG.setTemplateId("MULTIFILTER_COUROUSAL_HORIZONTAL");
		cardDataCG.setCardInfoArray(buildCardInfoArray(cardDataCB, client, expDataMap,filterConfiguration));


		if ((!utility.isExperimentTrue(expDataMap, MULTIFILTER_COROUSAL) || cardDataCG.getCardInfoArray().size() <= 1 ) || (CollectionUtils.isNotEmpty(cardDataCB) && cardDataCB.get(0) != null && !Objects.equals(cardDataCB.get(0).getTemplateId(), "multifilter_corousal"))) {

				cardDataCG.setCardInfoArray(null);
				for (com.mmt.hotels.pojo.listing.personalization.CardData cardDataObjectCB : cardDataCB) {

					if (cardDataObjectCB.getTemplateId() != null && !Objects.equals(cardDataObjectCB.getTemplateId(), "multifilter_corousal")) {
						cardDataCG = transformCardData(cardDataObjectCB, client, expDataMap, filterConfiguration);
						break;
					}
				}
			}

		return cardDataCG;

	}


	private CardInfo transformCardData(com.mmt.hotels.pojo.listing.personalization.CardData cardDataObjectCB, String client, LinkedHashMap<String,String> expDataMap,FilterConfiguration filterConfiguration) {

		CardInfo cardDataCG = new CardInfo();
		cardDataCG.setHasFilter(cardDataObjectCB.getHasFilter() != null ? cardDataObjectCB.getHasFilter() : false);
		cardDataCG.setIndex(cardDataObjectCB.getIndex());
		cardDataCG.setMinItemsToShow(cardDataObjectCB.getMinItemsToShow() == 0 ? null : cardDataObjectCB.getMinItemsToShow());
		cardDataCG.setId(cardDataObjectCB.getCardId());
		cardDataCG.setSubText(cardDataObjectCB.getSubText());
		cardDataCG.setSubType(cardDataObjectCB.getCardSubType());
		cardDataCG.setTitleText(cardDataObjectCB.getTitleText());
		cardDataCG.setTitleTextColor(cardDataObjectCB.getTitleTextColor());
		cardDataCG.setCardAction(buildCardAction(cardDataObjectCB.getCardAction(), filterConfiguration));
		cardDataCG.setCouponCode(cardDataObjectCB.getCouponCode());
        cardDataCG.setFloatingSheetData(buildFloatingSheetData(cardDataObjectCB.getFloatingSheetData()));
		if(Constants.HIDDEN_GEM_CARD.equalsIgnoreCase(cardDataCG.getSubType())) {
			addPersuasionsForHiddenGemCard(cardDataObjectCB.getCardPayload());
		}
		if(CollectionUtils.isNotEmpty(cardDataObjectCB.getHotelList())){
			CardPayloadResponse cardPayloadResponse = cardDataObjectCB.getCardPayload() != null ? cardDataObjectCB.getCardPayload() : new CardPayloadResponse();
			cardPayloadResponse.setHotelList(cardDataObjectCB.getHotelList());
			cardDataObjectCB.setCardPayload(cardPayloadResponse);
			cardDataObjectCB.setHotelList(null);
		}
		cardDataCG.setCardPayload(buildCardPayload(cardDataObjectCB.getCardPayload(), client,expDataMap));
		cardDataCG.setCardCondition(buildCardCondition(cardDataObjectCB.getCardCondition()));
		cardDataCG.setIconURL(cardDataObjectCB.getIconUrl());
		cardDataCG.setHasAction(cardDataObjectCB.getHasAction());
		cardDataCG.setActionText(cardDataObjectCB.getActionText());
		cardDataCG.setBgImageURL(cardDataObjectCB.getBgImageUrl());
		cardDataCG.setBgGradient(cardDataObjectCB.getBgGradient());
		cardDataCG.setScratchText(cardDataObjectCB.getScratchText());
		cardDataCG.setTemplateId(cardDataObjectCB.getTemplateId());
		cardDataCG.setPageContext(cardDataObjectCB.getPageContext());
		cardDataCG.setTemplateType(cardDataObjectCB.getTemplateType());
		cardDataCG.setDescription(cardDataObjectCB.getDescription());
		cardDataCG.setBgLinearGradient(cardDataObjectCB.getBgLinearGradient());
		cardDataCG.setHasTooltip(cardDataObjectCB.isHasToolTip());
		cardDataCG.setVideoUrl(cardDataObjectCB.getVideoUrl());
		cardDataCG.setSubTextList(cardDataObjectCB.getSubTextList());
		cardDataCG.setCardError(buildCardError(cardDataObjectCB.getCardError()));
		cardDataCG.setHotelList(buildCardHotelList(cardDataObjectCB.getHotelList()));
		cardDataCG.setCardSheet(buildCardSheet(cardDataObjectCB.getCardSheet()));
		cardDataCG.setTextColor(cardDataObjectCB.getTextColor());
		cardDataCG.setTitleTextColor(cardDataObjectCB.getTitleTextColor());
		cardDataCG.setExtraData(cardDataObjectCB.getExtraData());
		cardDataCG.setHeading(cardDataObjectCB.getHeading());
		return cardDataCG;
	}

	private FloatingSheetData buildFloatingSheetData(com.mmt.hotels.pojo.listing.personalization.FloatingSheetData sheetData) {
		if (sheetData == null)
			return null;
		FloatingSheetData floatingSheetData = new FloatingSheetData();
		floatingSheetData.setText(sheetData.getText());
		floatingSheetData.setCurrentTimeStamp(sheetData.getCurrentTimeStamp());
		floatingSheetData.setEndTimeStamp(sheetData.getEndTimeStamp());
		floatingSheetData.setFlotingActionIcon(sheetData.getFlotingActionIcon());
		floatingSheetData.setFlotingActionName(sheetData.getFlotingActionName());
		floatingSheetData.setCtaSelectedText(sheetData.getCtaSelectedText());
		floatingSheetData.setCtaNonSelectedText(sheetData.getCtaNonSelectedText());
		floatingSheetData.setIconURL(sheetData.getIconURL());
		floatingSheetData.setHeaderImageUrl(sheetData.getHeaderImageUrl());
		floatingSheetData.setBenefits(buildBenefitsList(sheetData.getBenefits()));
		return floatingSheetData;
	}

	private CardSheet buildCardSheet(com.mmt.hotels.pojo.listing.personalization.CardSheet cardSheet) {
		if (cardSheet == null) {
			return null;
		}

		CardSheet cardSheetCG = new CardSheet();
		cardSheetCG.setTopSheet(buildCardSheetElem(cardSheet.getTopSheet()));
		cardSheetCG.setBottomSheet(buildCardSheetElem(cardSheet.getBottomSheet()));

		return cardSheetCG;
	}

	private CardSheetElem buildCardSheetElem(com.mmt.hotels.pojo.listing.personalization.CardSheetElem cardSheetElem) {
		if (cardSheetElem == null) {
			return null;
		}

		CardSheetElem cardSheetElemCG = new CardSheetElem();
		cardSheetElemCG.setText(cardSheetElem.getText());
		cardSheetElemCG.setSubText(cardSheetElem.getSubText());
		cardSheetElemCG.setIconUrl(cardSheetElem.getIconUrl());
		cardSheetElemCG.setPurpose(cardSheetElem.getPurpose());
		cardSheetElemCG.setStyle(buildCardStyle(cardSheetElem.getStyle()));
		cardSheetElemCG.setSuccessInfo(buildSuccessInfo(cardSheetElem.getSuccessInfo()));

		return cardSheetElemCG;

	}

	private CardSheetElem.SuccessInfo buildSuccessInfo(com.mmt.hotels.pojo.listing.personalization.CardSheetElem.SuccessInfo successInfo) {
		if (successInfo == null) {
			return null;
		}

		CardSheetElem.SuccessInfo successInfoCG = new CardSheetElem.SuccessInfo();
		successInfoCG.setText(successInfo.getText());
		successInfoCG.setSubText(successInfo.getSubText());
		successInfoCG.setIconUrl(successInfo.getIconUrl());

		successInfoCG.setContent(buildContent(successInfo.getContent()));

		return successInfoCG;
	}

	private CardSheetElem.SuccessInfo.Content buildContent(com.mmt.hotels.pojo.listing.personalization.CardSheetElem.SuccessInfo.Content content) {
		if (content == null) {
			return null;
		}

		CardSheetElem.SuccessInfo.Content contentCG = new CardSheetElem.SuccessInfo.Content();
		contentCG.setText(content.getText());

		contentCG.setContentList(buildContentList(content.getContentList()));

		return contentCG;

	}

	private List<CardSheetElem.SuccessInfo.Content.ContentList> buildContentList(List<com.mmt.hotels.pojo.listing.personalization.CardSheetElem.SuccessInfo.Content.ContentList> contentList) {
		if (CollectionUtils.isEmpty(contentList)) {
			return null;
		}

		List<CardSheetElem.SuccessInfo.Content.ContentList> contentListCG = new ArrayList<>();
		for (com.mmt.hotels.pojo.listing.personalization.CardSheetElem.SuccessInfo.Content.ContentList contentElem : contentList) {
			CardSheetElem.SuccessInfo.Content.ContentList contentElemCG = new CardSheetElem.SuccessInfo.Content.ContentList();
			contentElemCG.setText(contentElem.getText());
			contentElemCG.setIconUrl(contentElem.getIconUrl());
			contentListCG.add(contentElemCG);
		}

		return contentListCG;
	}

	private List<BenefitsElem> buildBenefitsList(List<com.mmt.hotels.pojo.listing.personalization.BenefitsElem> benefits) {
		if (CollectionUtils.isEmpty(benefits)) {
			return null;
		}
		List<BenefitsElem> benefitsElemListCG = new ArrayList<>();
		for (com.mmt.hotels.pojo.listing.personalization.BenefitsElem benefitsElem : benefits) {
			BenefitsElem benefitsElemCG = new BenefitsElem();
			benefitsElemCG.setText(benefitsElem.getText());
			benefitsElemListCG.add(benefitsElemCG);
		}
		return benefitsElemListCG;
	}


	private CardSheetElem.Style buildCardStyle(com.mmt.hotels.pojo.listing.personalization.CardSheetElem.Style style) {
		if (style == null) {
			return null;
		}
		CardSheetElem.Style styleCG = new CardSheetElem.Style();
		styleCG.setBorderColor(style.getBorderColor());
		return styleCG;
	}


	/**
	 * Method to build hidden Gem Persuasion and add to hotelEntity for Mob-Landing card.
	 */
	private void addPersuasionsForHiddenGemCard(CardPayloadResponse cardPayload) {
		if (cardPayload != null && CollectionUtils.isNotEmpty(cardPayload.getHotelList())) {
			searchHotelsFactory.getResponseService(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue())).buildHiddenGemPersuasions(cardPayload.getHotelList());
		}
	}

	private CardError buildCardError(com.mmt.hotels.pojo.listing.personalization.CardError cardError){
		//Return error whenever there are no hotels available to show in card.
		logger.debug("Adding a error in SelectivePush card if present {}", cardError);
		if(cardError!=null) {
			CardError error = new CardError();
			error.setTitle(cardError.getTitle());
			error.setSubTitle(cardError.getSubTitle());
			error.setImgUrl(cardError.getImgUrl());
			return error;
		}
		return null;
	}

	private List<Hotel> buildCardHotelList(List<SearchWrapperHotelEntity> hotelList)
	{
		//Pass only required fields for hotels that are required by client
		logger.debug("Adding a hoteList in SelectivePush card if present {}", hotelList);
		if(CollectionUtils.isEmpty(hotelList))
		{
			return null;
		}
		List<Hotel> cardHotelList = new ArrayList<Hotel>();
		for(SearchWrapperHotelEntity hotelEntity : hotelList)
		{
			Hotel hotel = new Hotel();
			hotel.setName(hotelEntity.getName());
			hotel.setStarRating(hotelEntity.getStarRating());
			hotel.setId(hotelEntity.getId());
			if(hotelEntity.getDisplayFare()!=null && hotelEntity.getDisplayFare().getDisplayPriceBreakDown()!=null) {
				hotel.setPriceDetail(new PriceDetail());
				hotel.getPriceDetail().setDiscountedPriceWithTax(hotelEntity.getDisplayFare().getDisplayPriceBreakDown().getDisplayPrice() + (hotelEntity.getDisplayFare().getDisplayPriceBreakDown().isTaxIncluded() ? 0 : hotelEntity.getDisplayFare().getDisplayPriceBreakDown().getTotalTax()));
			}
			hotel.setReviewSummary(buildCardReviewSummary(hotelEntity.getCountryCode(),
					hotelEntity.getFlyfishReviewSummary()));

			hotel.setDetailDeeplinkUrl(hotelEntity.getDetailDeeplinkUrl());
			hotel.setTotalRoomCount((hotelEntity.getDisplayFare() != null && hotelEntity.getDisplayFare().getTotalRoomCount() != null) ? hotelEntity.getDisplayFare().getTotalRoomCount() : null);
			hotel.setIsAltAcco(hotelEntity.isAltAcco());
			buildSelectiveHotelPersuasions(hotel,hotelEntity);
			cardHotelList.add(hotel);
		}

		return cardHotelList;
	}

	public void buildSelectiveHotelPersuasions(Hotel hotel, SearchWrapperHotelEntity hotelEntity){

		SelectiveHotelPersuasions cardpersuasionsHES = hotelEntity.getSelectiveHotelPersuasions();
		logger.debug("Adding a selectivePersuasion in SelectivePush card if present {}",cardpersuasionsHES);
		if(cardpersuasionsHES!=null) {
			com.mmt.hotels.clientgateway.response.searchHotels.SelectiveHotelPersuasions cardpersuasionsCG = new com.mmt.hotels.clientgateway.response.searchHotels.SelectiveHotelPersuasions();
			//copying HES CArd persuasion to CGcard persuasion as both have same nodes
			BeanUtils.copyProperties(cardpersuasionsHES, cardpersuasionsCG);
			//In noramal flow we show discount based on this condition.
			if (!enableDiscount(hotelEntity.getDisplayFare() != null ? hotelEntity.getDisplayFare().getDisplayPriceBreakDown() : null)) {
				logger.debug("Disabling Discount for selectivePersuasion in SelectivePush card ");
				cardpersuasionsCG.setDiscount(null);
			}
			hotel.setSelectiveHotelPersuasions(cardpersuasionsCG);
		}
	}
	private ReviewSummary buildCardReviewSummary(String countryCode, Map<OTA, JsonNode> reviewSummaryMap){

		logger.debug("Adding a reviewSummary in SelectivePush card if present {}, \n {} ", countryCode, reviewSummaryMap);
		if (MapUtils.isEmpty(reviewSummaryMap))
			return null;
		JsonNode ratingSummary = null;
		OTA ota = null;
		if (DOM_COUNTRY.equalsIgnoreCase(countryCode)) {
			ratingSummary = reviewSummaryMap.get(OTA.MMT);
			ota = OTA.MMT;
		} else {
			ratingSummary = reviewSummaryMap.get(OTA.TA);
			ota = OTA.TA;
		}
		if(ratingSummary == null){
			ratingSummary = reviewSummaryMap.get(OTA.EXT);
			ota = OTA.EXT;
		}

		if (ratingSummary == null || (ratingSummary.get(CUMULATIVE_RATING_TEXT) != null && ratingSummary.get(CUMULATIVE_RATING_TEXT).intValue() == 0))
			return null;

		ReviewSummary reviewSummary = new ReviewSummary();
		reviewSummary.setSource(ota.getValue());

		if (ratingSummary.get(CUMULATIVE_RATING_TEXT) != null) {
			reviewSummary.setCumulativeRating(ratingSummary.get(CUMULATIVE_RATING_TEXT).floatValue());
		}

		return reviewSummary;
	}
	private CardCondition buildCardCondition(com.mmt.hotels.pojo.listing.personalization.CardCondition cardCondition) {
		if (null == cardCondition) {
			return null;
		}
		CardCondition cardConditionCG = new CardCondition();
		cardConditionCG.setCheckIfFilterApplied(buildFilter(cardCondition.getCheckIfFilterApplied()));
		cardConditionCG.setCheckIfFilterNotApplied(buildFilter(cardCondition.getCheckIfFilterNotApplied()));
		cardConditionCG.setCheckIfFilterPresent(buildFilters(cardCondition.getCheckIfFilterPresent()));
		cardConditionCG.setNoFilterApplied(cardCondition.getNoFilterApplied());
		cardConditionCG.setShouldBeFirstTimeUser(cardCondition.getShouldBeFirstTimeUser());
		cardConditionCG.setShouldBeLoggedInUser(cardCondition.getShouldBeLoggedInUser());
		cardConditionCG.setShouldBeVerifiedUser(cardCondition.getShouldBeVerifiedUser());
		cardConditionCG.setShouldCheckBrinLocked(cardCondition.getShouldCheckBrinLocked());
		cardConditionCG.setShouldCheckFC(cardCondition.getShouldCheckFC());
		cardConditionCG.setShouldCheckPAH(cardCondition.getShouldCheckPAH());
		cardConditionCG.setShouldCheckWallet(cardCondition.getShouldCheckWallet());
		return cardConditionCG;
	}

	private Set<com.mmt.hotels.clientgateway.request.Filter> buildFilters(Set<Filter> checkIfFilterPresent) {
		if (CollectionUtils.isEmpty(checkIfFilterPresent)) {
			return null;
		}
		Set<com.mmt.hotels.clientgateway.request.Filter> set = new HashSet<>();
		for (Filter filter : checkIfFilterPresent) {
			set.add(buildFilter(filter));
		}
		return set;
	}

	private com.mmt.hotels.clientgateway.request.Filter buildFilter(Filter filterHES) {
		if (null == filterHES) {
			return null;
		}
		com.mmt.hotels.clientgateway.request.Filter fltr = new com.mmt.hotels.clientgateway.request.Filter();
		fltr.setFilterGroup(com.mmt.hotels.clientgateway.response.filter.FilterGroup.getFilterGroupFromFilterName(filterHES.getFilterGroup().name()));
		FilterRange filterRange = null;
		if (fltr.getFilterRange() != null) {
			filterRange = new FilterRange();
			filterRange.setMaxValue(filterHES.getFilterRange().getMaxValue());
			filterRange.setMinValue(filterHES.getFilterRange().getMinValue());
		}
		fltr.setFilterRange(filterRange);
		fltr.setFilterValue(filterHES.getFilterValue());
		fltr.setRangeFilter(filterHES.isRangeFilter());
		return fltr;
	}

	private CardPayloadData buildCardPayload(CardPayloadResponse cardPayload, String client,LinkedHashMap<String,String> expDataMap) {
		if (cardPayload == null) {
			return null;
		}
		CardPayloadData cardPayloadData = new CardPayloadData();
		cardPayloadData.setPlacementContextList(buildPlacementContextList(cardPayload));
		cardPayloadData.setSearchContext(buildSearchContext(cardPayload));
		cardPayloadData.setAltAccoData(buildAltAccoData(cardPayload.getAltAccoData()));
		cardPayloadData.setAltAccoDiscovery(buildAltAccoDiscovery(cardPayload.getAltAccoDiscovery(), client));
		cardPayloadData.setGenericCardData(buildGenericCardData(cardPayload.getGenericCardData()));
		cardPayloadData.setValueStaysDataCG(buildValueStaysData(cardPayload.getValueStaysData()));
		cardPayloadData.setHotelList(cardPayload.getHotelList());
		cardPayloadData.setContextualFilterData(buildContextualFilterData(cardPayload.getContextualFilterData()));
		cardPayloadData.setNearByData(buildNearByData(cardPayload.getNearByData()));
		cardPayloadData.setPageImageUrl(cardPayload.getPageImageUrl());
		cardPayloadData.setPageHeaderText(cardPayload.getPageHeaderText());
		cardPayloadData.setPageHeaderSubText(cardPayload.getPageHeaderSubText());
		cardPayloadData.setPolarisData(buildPolarisData(cardPayload.getPolarisData()));
		cardPayloadData.setScratchText(cardPayload.getScratchText());
		cardPayloadData.setScratchColor(cardPayload.getScratchColor());
		cardPayloadData.setTitle(cardPayload.getTitle());
		cardPayloadData.setMetaPersuasion(cardPayload.getMetaPersuasion());
		cardPayloadData.setRewardStatus(cardPayload.getRewardStatus());
		cardPayloadData.setRewardCode(cardPayload.getRewardCode());
		cardPayloadData.setFilterSelectType(cardPayload.getFilterSelectType());
		if(MapUtils.isNotEmpty(expDataMap) && EXP_TRUE_VALUE.equalsIgnoreCase(expDataMap.get(NEW_HOTEL_LIST))){ //Building new hotel list in case this Exp is True
			cardPayloadData.setHotelListNew(convertToHotelListNew(cardPayloadData));
			if(CollectionUtils.isNotEmpty(cardPayloadData.getHotelListNew()))
				cardPayloadData.setHotelList(null); //suppress oldHotelList in case new is built
		}
		List<SpokeCityCG> spokeCityCGS = buildSpokeCityData(cardPayload.getSpokeCityData());
		cardPayloadData.setSpokeCityData(spokeCityCGS);
		cardPayloadData.setTimerCard(buildTimerCard(cardPayload.getTimerCard()));
		cardPayloadData.setViewAllCard(buildViewAllCard(cardPayload.getViewAllCard()));
		return cardPayloadData;
	}

	/**
	 * This method is used to build Spoke city collection card Data for Hub City Search HTL-37166
	 * This method is also used to build Hidden Gems Collection Card Data for Zone Search HTL-37867
	 */
	private List<SpokeCityCG> buildSpokeCityData(List<SpokeCity> spokeCities){
		List<SpokeCityCG> spokeCityCGList = new ArrayList<>();
		if(CollectionUtils.isNotEmpty(spokeCities)){
			for(SpokeCity spokeCity: spokeCities){
				SpokeCityCG spokeCityCG = new SpokeCityCG();
				spokeCityCG.setDesc(spokeCity.getDesc());
				spokeCityCG.setType(spokeCity.getType());
				spokeCityCG.setImgURL(spokeCity.getImgURL());
				spokeCityCG.setLocId(spokeCity.getLocId());
				spokeCityCG.setLocType(spokeCity.getLocType());
				if(spokeCity.getParentLoc()!=null){
					ParentLocCG parentLocCG = new ParentLocCG();
					parentLocCG.setId(spokeCity.getParentLoc().getId());
					parentLocCG.setType(spokeCity.getParentLoc().getType());
					spokeCityCG.setParentLoc(parentLocCG);
				}
				spokeCityCG.setSearchHotelLimit(spokeCity.getSearchHotelLimit());
				spokeCityCG.setSubText2(spokeCity.getSubText2());
				spokeCityCG.setDeepLink(spokeCity.getDeeplink());
				spokeCityCG.setButtonText(spokeCity.getButtonText());
				spokeCityCGList.add(spokeCityCG);
			}
		}
		return spokeCityCGList;
	}

	public List<Hotel> convertToHotelListNew(CardPayloadData cardPayload){
		if(CollectionUtils.isNotEmpty(cardPayload.getHotelList())){
			return searchHotelsFactory.getResponseService(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue())).buildPersonalizedHotels(cardPayload.getHotelList(),null,null,null,null);
		}
		return null;
	}

	/**
	 * This method is used to build data (e.g. starText and valueStaysPersuasion) specific to MMT ValueStays
	 *
	 */
	private ValueStaysDataCG buildValueStaysData(ValueStaysData valueStaysData) {
		ValueStaysDataCG valueStaysDataCG = null;
		if (valueStaysData != null) {
			valueStaysDataCG = new ValueStaysDataCG();
			valueStaysDataCG.setStarText(valueStaysData.getStarText());
			if (valueStaysData.getValueStaysPersuasion() != null) {
				ValueStaysPersuasionCG valueStaysPersuasionCG = new ValueStaysPersuasionCG();
				valueStaysPersuasionCG.setIconType(valueStaysData.getValueStaysPersuasion().getIconType());
				valueStaysPersuasionCG.setIconUrl(valueStaysData.getValueStaysPersuasion().getIconUrl());
				valueStaysPersuasionCG.setText(valueStaysData.getValueStaysPersuasion().getText());
				valueStaysDataCG.setValueStaysPersuasion(valueStaysPersuasionCG);
			}
		}
		return valueStaysDataCG;
	}

	private List<String> buildPlacementContextList(CardPayloadResponse cardPayloadResponse) {
		List<String> placementContextList = null;
		if (cardPayloadResponse != null && CollectionUtils.isNotEmpty(cardPayloadResponse.getPlacementContextList())) {
			placementContextList = cardPayloadResponse.getPlacementContextList()
					.stream()
					.map(AdTechPlacementContext::getContextId)
					.collect(Collectors.toList());
		}
		return placementContextList;
	}

	protected AdTechSearchContext buildSearchContext(CardPayloadResponse cardPayload) {
		AdTechSearchContext searchContext = new AdTechSearchContext();
		List<AdTechSearchContextDetails> currSearchContext = null;
		if (cardPayload.getSearchContext() != null && CollectionUtils.isNotEmpty(cardPayload.getSearchContext().getCurrSearchContext())) {
			currSearchContext = new ArrayList<>();
			for (com.mmt.hotels.model.response.adtech.AdTechSearchContextDetails searchContextDetailsHES : cardPayload.getSearchContext().getCurrSearchContext()) {
				AdTechSearchContextDetails searchContextDetails = new AdTechSearchContextDetails();
				buildSearchContextDetails(searchContextDetails, searchContextDetailsHES);
				currSearchContext.add(searchContextDetails);
			}
		}
		searchContext.setCurrSearchContext(currSearchContext);
		return searchContext;
	}

	private void buildSearchContextDetails(AdTechSearchContextDetails searchContextDetails, com.mmt.hotels.model.response.adtech.AdTechSearchContextDetails searchContextDetailsHES) {
		if (searchContextDetailsHES != null) {
			searchContextDetails.setLob(searchContextDetailsHES.getLob());
			if (searchContextDetailsHES.getDestination() != null) {
				AdTechDestination destination = new AdTechDestination();
				destination.setCityCode(searchContextDetailsHES.getDestination().getCityCode());
				destination.setCountryCode(searchContextDetailsHES.getDestination().getCountryCode());
				destination.setLocusId(searchContextDetailsHES.getDestination().getLocusId());
				destination.setLocusType(searchContextDetailsHES.getDestination().getLocusType());
				searchContextDetails.setDestination(destination);
			}
			searchContextDetails.setStartDate(searchContextDetailsHES.getStartDate());
			searchContextDetails.setEndDate(searchContextDetailsHES.getEndDate());
			searchContextDetails.setTripType(searchContextDetailsHES.getTripType());
			if (searchContextDetailsHES.getHotels() != null) {
				AdTechHotel hotel = new AdTechHotel();
				if(searchContextDetailsHES.getHotels().getRoomCount()!=null){
					hotel.setRoomCount(searchContextDetailsHES.getHotels().getRoomCount());
				}
				AdTechPaxDetails paxDetails = new AdTechPaxDetails();
				if(searchContextDetailsHES.getHotels().getPaxDetails()!=null){
					paxDetails.setAdult(searchContextDetailsHES.getHotels().getPaxDetails().getAdult());
					paxDetails.setChildren(searchContextDetailsHES.getHotels().getPaxDetails().getChildren());
					paxDetails.setChildAges(searchContextDetailsHES.getHotels().getPaxDetails().getChildAges());
				}
				hotel.setPaxDetails(paxDetails);
				searchContextDetails.setHotels(hotel);
			}
			searchContextDetails.setCorrelationKey(searchContextDetailsHES.getCorrelationKey());
			searchContextDetails.setSearchSource(searchContextDetailsHES.getSearchSource());
		}
	}

	private PolarisData buildPolarisData(com.mmt.hotels.model.response.listpersonalization.PolarisData polarisDataCB) {
		if (polarisDataCB == null) {
			return null;
		}
		PolarisData polarisDataCG = new PolarisData();
		polarisDataCG.setShowImage(polarisDataCB.isShowImage());
		polarisDataCG.setTags(buildPolarisTags(polarisDataCB.getTags()));
		polarisDataCG.setPolarisMediaInfo(buildPolarisMediaInfo(polarisDataCB.getPolarisMediaInfo()));
		return polarisDataCG;
	}

	private PolarisMediaInfo buildPolarisMediaInfo(com.mmt.hotels.model.response.listpersonalization.PolarisMediaInfo polarisMediaInfoHES) {
		if (polarisMediaInfoHES == null) return null;
		PolarisMediaInfo polarisMediaInfoCG = new PolarisMediaInfo();
		BeanUtils.copyProperties(polarisMediaInfoHES, polarisMediaInfoCG);
		return polarisMediaInfoCG;
	}

	private List<PolarisTag> buildPolarisTags(List<com.mmt.hotels.model.response.listpersonalization.PolarisTag> tags) {
		if (CollectionUtils.isEmpty(tags)) {
			return null;
		}
		List<PolarisTag> tagList = new ArrayList<>();
		for (com.mmt.hotels.model.response.listpersonalization.PolarisTag polarisTagCB : tags) {
			PolarisTag polarisTagCG = new PolarisTag();
			polarisTagCG.setId(polarisTagCB.getId());
			polarisTagCG.setDesc(polarisTagCB.getDesc());
			polarisTagCG.setImgURL(polarisTagCB.getImgURL());
			polarisTagCG.setLatitude(polarisTagCB.getLatitude());
			polarisTagCG.setLongitude(polarisTagCB.getLongitude());
			polarisTagCG.setLocId(polarisTagCB.getLocId());
			polarisTagCG.setLocType(polarisTagCB.getLocType());
			polarisTagCG.setType(polarisTagCB.getType());
			polarisTagCG.setTypeId(polarisTagCB.getTypeId());
			polarisTagCG.setShowableEntities(polarisTagCB.getShowableEntities());
			polarisTagCG.setBbox(buildBBox(polarisTagCB.getBbox()));
			polarisTagCG.setPoiCategory(polarisTagCB.getPoiCategory());
			polarisTagCG.setSubText(polarisTagCB.getSubText());
			polarisTagCG.setSubText2(polarisTagCB.getSubText2());
			polarisTagCG.setBudget(polarisTagCB.getBudget());
			polarisTagCG.setButtonText(polarisTagCB.getButtonText());
			polarisTagCG.setShortText(polarisTagCB.getShortText());

			tagList.add(polarisTagCG);
		}
		return tagList;
	}

	private LatLong buildBBox(BbLatLong bbox) {
		if (bbox == null) {
			return null;
		}
		LatLong bboxCG = new LatLong();
		if (bbox.getNe() != null) {
			bboxCG.setNe(new LatLongObject());
			bboxCG.getNe().setLat(bbox.getNe().getLat());
			bboxCG.getNe().setLng(bbox.getNe().getLng());
		}
		if (bbox.getSw() != null) {
			bboxCG.setSw(new LatLongObject());
			bboxCG.getSw().setLat(bbox.getSw().getLat());
			bboxCG.getSw().setLng(bbox.getSw().getLng());
		}
		return bboxCG;
	}

	private List<ContextualFilterDataCG> buildContextualFilterData(List<com.mmt.hotels.model.response.listpersonalization.ContextualFilterData> contextualFilterData) {
		if (CollectionUtils.isEmpty(contextualFilterData))
			return null;
		List<ContextualFilterDataCG> contextualFilterDataList = new ArrayList<>();
		for (com.mmt.hotels.model.response.listpersonalization.ContextualFilterData filterData : contextualFilterData) {
			ContextualFilterDataCG dataCG = new ContextualFilterDataCG();
			dataCG.setBgImageUrl(filterData.getBgImageUrl());
			dataCG.setIconUrl(filterData.getIconUrl());
			dataCG.setIndex(filterData.getIndex());
			dataCG.setHasAction(filterData.isHasAction());
			dataCG.setCardActionText(filterData.getCardActionText());
			dataCG.setCardAxis(filterData.getCardAxis());
			dataCG.setCardSubType(filterData.getCardSubType());
			dataCG.setDescText(filterData.getDescText());
			dataCG.setSelected(filterData.isSelected());
			dataCG.setHeaderText(filterData.getHeaderText());
			if (filterData.getCardFilters() != null && filterData.getCardFilters().getFilterList() != null) {
				LinkedHashMap<com.mmt.hotels.clientgateway.response.filter.FilterGroup, LinkedHashSet<com.mmt.hotels.clientgateway.response.filter.Filter>> map = new LinkedHashMap<>();
				for (FilterGroup f : filterData.getCardFilters().getFilterList().keySet()) {
					LinkedHashSet<com.mmt.hotels.clientgateway.response.filter.Filter> filterLinkedHashSet = new LinkedHashSet<>();
					filterData.getCardFilters().getFilterList().get(f).stream().forEach(filterHES -> {
						filterLinkedHashSet.add(buildFilterCG(filterHES));
					});
					map.put(com.mmt.hotels.clientgateway.response.filter.FilterGroup.valueOf(f.name()), filterLinkedHashSet);
				}
				Filters list = new Filters();
				list.setFilterList(map);
				dataCG.setCardFilters(list);
			}
			contextualFilterDataList.add(dataCG);
		}
		return contextualFilterDataList;
	}

	public com.mmt.hotels.clientgateway.response.filter.Filter buildFilterCG(Filter filterHES) {
		if(filterHES == null) {
			return null;
		}
		com.mmt.hotels.clientgateway.response.filter.Filter filterCG = new com.mmt.hotels.clientgateway.response.filter.Filter();
		if(filterHES.getFilterGroup() != null)
			filterCG.setFilterGroup(filterHES.getFilterGroup().name());
		filterCG.setCount(filterHES.getCount());
		filterCG.setRangeFilter(filterHES.isRangeFilter());
		filterCG.setFilterValue(filterHES.getFilterValue());
		filterCG.setSelectedFilterText(filterHES.getSelectedFilterText());
		filterCG.setTitle(filterHES.getTitle());
		filterCG.setSubTitle(filterHES.getSubTitle());
		filterCG.setIconUrl(filterHES.getIconUrl());
		filterCG.setFilterExists(filterHES.getFilterExist());
		if (filterHES.getFilterRange() != null) {
			filterCG.setRangeFilter(true);
			filterCG.setFilterRange(new com.mmt.hotels.clientgateway.response.filter.FilterRange());
			filterCG.getFilterRange().setMaxValue(filterHES.getFilterRange().getMaxValue());
			filterCG.getFilterRange().setMinValue(filterHES.getFilterRange().getMinValue());
		}
		filterCG.setPreApplied(filterHES.getFilterExist());
		filterCG.setTooltip(filterHES.getTooltip());
		filterCG.setDistance(filterHES.getDistance());
		filterCG.setMatchmakerType(filterHES.getMatchmakerType());
		filterCG.setImageUrl(filterHES.getImageUrl());
		filterCG.setIconList(filterHES.getIconList());
		filterCG.setSuggestedFilters(buildSuggestedFilters(filterHES.getSuggestedFilters()));
		if(filterHES.getSequence() != null) {
			filterCG.setSequence(filterHES.getSequence());
		}
		return filterCG;
	}

	public List<com.mmt.hotels.clientgateway.response.filter.Filter> buildSuggestedFilters(List<Filter> suggestedFilters) {
		if(CollectionUtils.isEmpty(suggestedFilters)){
			return null;
		}
		List<com.mmt.hotels.clientgateway.response.filter.Filter> suggestedFilterList = new ArrayList<>();
		suggestedFilters.forEach(filterHes -> suggestedFilterList.add(buildFilterCG(filterHes)));
		return suggestedFilterList;
	}

	private List<NearByLocation> buildNearByData(List<com.mmt.hotels.model.response.nearby.NearByLocation> nearByData) {
		if (CollectionUtils.isEmpty(nearByData))
			return null;
		List<NearByLocation> nearByDataCG = new ArrayList<>();
		for (com.mmt.hotels.model.response.nearby.NearByLocation nearData : nearByData) {
			NearByLocation nearCG = new NearByLocation();
			nearCG.setDistance(nearData.getDistance());
			nearCG.setLocId(nearData.getLocId());
			nearCG.setLocName(nearData.getLocName());
			nearCG.setLocType(nearData.getLocName());
			nearCG.setUnit(nearData.getUnit());
			nearByDataCG.add(nearCG);
		}
		return nearByDataCG;
	}

	private List<GenericCardPayloadDataCG> buildGenericCardData(List<GenericCardPayloadData> genericCardData) {
		if (CollectionUtils.isEmpty(genericCardData)) {
			return null;
		}
		List<GenericCardPayloadDataCG> genericCardPayloadDataList = new ArrayList<>();
		for (GenericCardPayloadData genericCardPayloadData : genericCardData) {
			GenericCardPayloadDataCG genericCardDataCG = new GenericCardPayloadDataCG();
			genericCardDataCG.setTitleText(genericCardPayloadData.getTitleText());
			genericCardDataCG.setText(genericCardPayloadData.getText());
			genericCardDataCG.setSubText(genericCardPayloadData.getSubText());
			genericCardDataCG.setIconUrl(genericCardPayloadData.getIconUrl());
			genericCardDataCG.setImageUrl(genericCardPayloadData.getImageUrl());
			genericCardDataCG.setItemIconType(genericCardPayloadData.getItemIconType());
			genericCardDataCG.setActionUrl(genericCardPayloadData.getActionUrl());
			genericCardDataCG.setActionText(genericCardPayloadData.getActionText());
			genericCardDataCG.setCharityDescription(genericCardPayloadData.getCharityDescription());
			genericCardDataCG.setDataCat(genericCardPayloadData.getDataCat());
			genericCardDataCG.setTag(genericCardPayloadData.getTag());
			genericCardDataCG.setGalleryView(genericCardDataCG.getGalleryView());
			genericCardDataCG.setFilterList(genericCardPayloadData.getFilterList());
			genericCardDataCG.setPromoCode(genericCardPayloadData.getPromoCode());
			genericCardDataCG.setData(buildGenericCardData(genericCardPayloadData.getData()));
			genericCardPayloadDataList.add(genericCardDataCG);
		}
		return genericCardPayloadDataList;
	}

	private List<AltAccoDiscovery> buildAltAccoDiscovery(List<CollectionsResponseBo<SearchWrapperHotelEntityAbridged>> list, String client) {
		if (CollectionUtils.isEmpty(list)) {
			return null;
		}
		List<AltAccoDiscovery> altAccoDiscoveryList = new ArrayList<>();
		for (CollectionsResponseBo collectionsResponseBoCB : list) {
			AltAccoDiscovery altAccoDiscoveryCG = new AltAccoDiscovery();
			altAccoDiscoveryCG.setCollectionsResponse(buildCollectionsResponse(collectionsResponseBoCB.getCollectionsResponse(), client));
			altAccoDiscoveryList.add(altAccoDiscoveryCG);
		}
		return altAccoDiscoveryList;
	}

	private List<CollectionsResponse> buildCollectionsResponse(List<FeaturedCollections> collectionsResponse, String client) {
		if (CollectionUtils.isEmpty(collectionsResponse)) {
			return null;
		}
		List<CollectionsResponse> collectionsResponseList = new ArrayList<>();
		for (FeaturedCollections featuredCollectionsCB : collectionsResponse) {
			CollectionsResponse collectionsResponseCG = new CollectionsResponse();
			collectionsResponseCG.setAppliedFilterMap(buildAppliedFilterMap(featuredCollectionsCB.getAppliedFilterMap()));
			collectionsResponseCG.setDeepLink(featuredCollectionsCB.getDeepLink());
			collectionsResponseCG.setDeepLinkApp(featuredCollectionsCB.getDeepLinkApp());
			collectionsResponseCG.setHeading(featuredCollectionsCB.getHeading());
			// changing back to old hotels object in moblanding response as clients did not move to new object schema .
			collectionsResponseCG.setHotels(convertAbridgedIntoSearchWrapperHotelEntity(featuredCollectionsCB.getHotels()));
//            collectionsResponseCG.setHotels(searchHotelsFactory.getResponseService(client).buildPersonalizedHotels(convertAbridgedIntoSearchWrapperHotelEntity(featuredCollectionsCB.getHotels()),null, null));
			collectionsResponseCG.setPriority(featuredCollectionsCB.getPriority());
			collectionsResponseCG.setSearchContext(buildSearchContext(featuredCollectionsCB.getSearchContext()));
			collectionsResponseCG.setSubHeading(featuredCollectionsCB.getSubHeading());
			collectionsResponseCG.setThreshold(featuredCollectionsCB.getThreshold());
			collectionsResponseList.add(collectionsResponseCG);
		}
		return collectionsResponseList;
	}

	private List<com.mmt.hotels.clientgateway.response.moblanding.CardAction> buildCardAction(List<com.mmt.hotels.pojo.listing.personalization.CardAction> list, FilterConfiguration filterConfiguration) {
		if (list == null)
			return null;

		List<com.mmt.hotels.clientgateway.response.moblanding.CardAction> cardActionList = new ArrayList<>();

		for (com.mmt.hotels.pojo.listing.personalization.CardAction cardActionCB : list) {
			if (cardActionCB == null) {
				continue;
			}
			com.mmt.hotels.clientgateway.response.moblanding.CardAction cardActionCG = new com.mmt.hotels.clientgateway.response.moblanding.CardAction();
			cardActionCG.setLogin(cardActionCB.getIsLogin()); /* TO BE REMOVED */
			cardActionCG.setIsLogin(cardActionCB.getIsLogin());
			cardActionCG.setLoginWithReward(cardActionCB.getIsLoginWithReward());
			cardActionCG.setVerifyWithReward(cardActionCB.getIsVerifyWithReward());
			cardActionCG.setOpenMatchMaker(cardActionCB.getOpenMatchMaker());
			cardActionCG.setWebViewUrl(cardActionCB.getWebViewUrl());
			cardActionCG.setCategories(cardActionCB.getCategories());
			cardActionCG.setFilters(buildMobLandingFilters(cardActionCB.getFilters(), filterConfiguration));
			cardActionCG.setBreakfast(cardActionCB.getIsBreakfast());
			cardActionCG.setFreeWifi(cardActionCB.getIsFreeWifi());
			cardActionCG.setPah(cardActionCB.getIsPah());
			cardActionCG.setMatchmakerTags(buildMatchMakerTags(cardActionCB.getMatchmakerTags()));
			cardActionCG.setPriceBucket(buildPriceBucket(cardActionCB.getPriceBucket()));
			cardActionCG.setStarRating(cardActionCB.getStarRating());
			cardActionCG.setDeeplinkUrl(cardActionCB.getDeeplinkUrl());
			cardActionCG.setTitle(cardActionCB.getTitle());
			cardActionCG.setLocusData(cardActionCB.getLocusData());
			cardActionCG.setSlug(cardActionCB.getSlug());
			cardActionCG.setDescription(cardActionCB.getDescription());
			if(cardActionCB.getAction() != null){
				CardAction.MoreInfoAction action =  new CardAction.MoreInfoAction();
				action.setActionProp(cardActionCB.getAction().getActionProp());
				action.setTitle(cardActionCB.getAction().getTitle());
				cardActionCG.setAction(action);
			}
			cardActionList.add(cardActionCG);
		}
		return cardActionList;
	}

	public com.mmt.hotels.model.response.listpersonalization.Filters buildMobLandingFilters(
			com.mmt.hotels.model.response.listpersonalization.Filters filtersHES,
			FilterConfiguration filterConfiguration) {

		if (filtersHES == null || filtersHES.getFilterList() == null || filtersHES.getFilterList().isEmpty()) {
			return filtersHES;
		}

		// Fetch the existing filter groups from HES
		LinkedHashMap<FilterGroup, LinkedHashSet<Filter>> filterListHES = filtersHES.getFilterList();

		// Get brand filter configurations for CHAIN_INFO
		Map<String, FilterConfigDetail> chainInfoConfig = getChainInfoConfig(filterConfiguration);

		if (filterListHES.containsKey(FilterGroup.CHAIN_INFO)) {
			updateFilters(filterListHES.get(FilterGroup.CHAIN_INFO), chainInfoConfig);
		}

		return filtersHES;
	}

	private Map<String, FilterConfigDetail> getChainInfoConfig(FilterConfiguration filterConfiguration) {
		if (filterConfiguration == null || filterConfiguration.getFilters() == null) {
			return new LinkedHashMap<>();
		}
		return filterConfiguration.getFilters()
				.getOrDefault(BRAND_FILTER, new FilterConfigCategory())
				.getGroups()
				.getOrDefault(FilterGroup.CHAIN_INFO.name(), new LinkedHashMap<>());
	}

	void updateFilters(LinkedHashSet<Filter> filters, Map<String, FilterConfigDetail> config) {
		if (filters == null || config == null) {
			return;
		}

		int count = 0;
		Iterator<Filter> iterator = filters.iterator();
		while (iterator.hasNext() && count < 5) {
			Filter filter = iterator.next();
			if (filter == null || filter.getFilterValue() == null) {
				continue;
			}

			FilterConfigDetail configData = config.get(filter.getFilterValue());
			if (configData != null) {
				filter.setTitle(configData.getTitle() != null ? configData.getTitle() : filter.getTitle());
				filter.setSubTitle(configData.getSubTitle() != null ? configData.getSubTitle() : filter.getSubTitle());
				filter.setImageUrl(configData.getImageUrl() != null ? configData.getImageUrl() : filter.getImageUrl());
			}
			count++;
		}

		// Remove extra filters beyond the limit
		while (iterator.hasNext()) {
			iterator.next();
			iterator.remove();
		}
	}


	private PriceBucketObject buildPriceBucket(PriceBucket priceBucket) {
		if (priceBucket == null)
			return null;
		PriceBucketObject priceBucketObject = new PriceBucketObject();
		priceBucketObject.setCount(priceBucket.getCount());
		priceBucketObject.setMaxPrice(priceBucket.getMaxPrice());
		priceBucketObject.setMinPrice(priceBucket.getMinPrice());
		priceBucketObject.setText(priceBucket.getText());
		return priceBucketObject;
	}

	private List<MatchmakerTagObject> buildMatchMakerTags(List<com.mmt.hotels.model.response.listpersonalization.MatchmakerTag> list) {
		if (CollectionUtils.isEmpty(list)) {
			return null;
		}
		List<MatchmakerTagObject> matchmakerTagObjectList = new ArrayList<>();
		for (com.mmt.hotels.model.response.listpersonalization.MatchmakerTag matchmakerTagCB : list) {
			MatchmakerTagObject matchmakerTagObjectCG = new MatchmakerTagObject();
			matchmakerTagObjectCG.setName(matchmakerTagCB.getName());
			matchmakerTagObjectCG.setTagId(matchmakerTagCB.getTagId());
			matchmakerTagObjectCG.setCount(matchmakerTagCB.getCount());
			matchmakerTagObjectCG.setText(matchmakerTagCB.getText());
			matchmakerTagObjectList.add(matchmakerTagObjectCG);
		}
		return matchmakerTagObjectList;
	}


	private Map<com.mmt.hotels.clientgateway.response.filter.FilterGroup, Set<FilterObject>> buildAppliedFilterMap(Map<FilterGroup, Set<Filter>> appliedFilterMap) {
		if (MapUtils.isEmpty(appliedFilterMap)) {
			return null;
		}
		Map<com.mmt.hotels.clientgateway.response.filter.FilterGroup, Set<FilterObject>> filterGroupCGSetMap = new HashMap<>();
		for (FilterGroup filterGroupCB : appliedFilterMap.keySet()) {

			com.mmt.hotels.clientgateway.response.filter.FilterGroup filterGroup = com.mmt.hotels.clientgateway.response.filter.FilterGroup.getFilterGroupFromFilterName(filterGroupCB.name());
			Set<Filter> filterSetCB = appliedFilterMap.get(filterGroupCB);
			Set<FilterObject> filterSetCG = new HashSet<>();
			for (Filter filterCB : filterSetCB) {
				filterCB.getFilterGroup();
				if (EXACT_ROOM_RECOMMENDATION.equalsIgnoreCase(filterCB.getFilterGroup().name())) {
					continue;
				}
				FilterObject filterCG = new FilterObject();
				filterCG.setFilterGroup(com.mmt.hotels.clientgateway.response.filter.FilterGroup.valueOf(filterCB.getFilterGroup().name()));
				filterCG.setFilterRange(buildFilterRange(filterCB.getFilterRange()));
				filterCG.setFilterValue(filterCB.getFilterValue());
				filterSetCG.add(filterCG);
			}
			filterGroupCGSetMap.put(filterGroup, filterSetCG);
		}
		return filterGroupCGSetMap;
	}

	private FilterRange buildFilterRange(com.mmt.hotels.filter.FilterRange filterRange) {
		if (filterRange == null) {
			return null;
		}
		FilterRange filterRangeCG = new FilterRange();
		filterRangeCG.setMaxValue(filterRange.getMaxValue());
		filterRangeCG.setMinValue(filterRange.getMinValue());
		return filterRangeCG;
	}

	private SearchCriteria buildSearchContext(RecommendedSearchContext searchContext) {
		if (searchContext == null) {
			return null;
		}
		SearchCriteria searchCriteria = new SearchCriteria();
		searchCriteria.setCheckIn(searchContext.getCheckIn());
		searchCriteria.setCheckOut(searchContext.getCheckOut());
		searchCriteria.setCityCode(searchContext.getCityCode());
		searchCriteria.setCountryCode(searchContext.getCountryCode());
		searchCriteria.setLocationId(searchContext.getLocationId());
		searchCriteria.setLocationType(searchContext.getLocationType());
		//searchCriteria.setRoomStayCandidates(buildRoomStayCandidates(searchContext.getRoomStayCandidates()));
		return searchCriteria;
	}

	private List<com.mmt.hotels.clientgateway.response.moblanding.AltAccoResponse> buildAltAccoData(List<AltAccoResponse> altAccoData) {
		if (CollectionUtils.isEmpty(altAccoData))
			return null;

		List<com.mmt.hotels.clientgateway.response.moblanding.AltAccoResponse> altAccoResponseCGList = new ArrayList<>();

		for (AltAccoResponse altAccoResponseCB : altAccoData) {

			com.mmt.hotels.clientgateway.response.moblanding.AltAccoResponse altAccoResponseCG = new com.mmt.hotels.clientgateway.response.moblanding.AltAccoResponse();

			altAccoResponseCG.setBgUrl(altAccoResponseCB.getBgUrl());
			altAccoResponseCG.setListThumbnailUrl(altAccoResponseCB.getListThumbnailUrl());
			altAccoResponseCG.setPropertyDesc(altAccoResponseCB.getPropertyDesc());
			altAccoResponseCG.setPropertyName(altAccoResponseCB.getPropertyName());
			altAccoResponseCG.setPropertyPersuasions(buildPropertyPersuations(altAccoResponseCB.getPropertyPersuasions()));
			altAccoResponseCG.setPropertyType(altAccoResponseCB.getPropertyType());
			altAccoResponseCG.setPropertyTypeList(altAccoResponseCB.getPropertyTypeList());
			altAccoResponseCG.setSequenceHeader(altAccoResponseCB.getSequenceHeader());
			altAccoResponseCG.setSequenceId(altAccoResponseCB.getSequenceId());
			altAccoResponseCG.setSequenceSubHeader(altAccoResponseCB.getSequenceSubHeader());
			altAccoResponseCG.setThumbnailUrl(altAccoResponseCB.getThumbnailUrl());

			altAccoResponseCGList.add(altAccoResponseCG);
		}
		return altAccoResponseCGList;
	}


	private List<PropertyPersuasion> buildPropertyPersuations(List<PropertyPersuasions> propertyPersuasions) {
		if (propertyPersuasions == null)
			return null;

		List<PropertyPersuasion> propertyPersuasionList = new ArrayList<>();

		for (PropertyPersuasions propertyPersuasionsCB : propertyPersuasions) {
			PropertyPersuasion propertyPersuasionCG = new PropertyPersuasion();

			propertyPersuasionCG.setDesc(propertyPersuasionsCB.getDesc());
			propertyPersuasionCG.setIconUrl(propertyPersuasionsCB.getIconUrl());
			propertyPersuasionCG.setTitle(propertyPersuasionsCB.getTitle());

			propertyPersuasionList.add(propertyPersuasionCG);
		}

		return propertyPersuasionList;

	}

	public DoubleBlackInfo getDoubleBlackInfo(DoubleBlackValidateResponse doubleBlackRsp) {
		if (doubleBlackRsp != null) {
			DoubleBlackInfo doubleBlackInfo = new DoubleBlackInfo();
			doubleBlackInfo.setBenefitId(doubleBlackRsp.getBenefitId());
			doubleBlackInfo.setBookingEligible(doubleBlackRsp.isBookingEligible());
			doubleBlackInfo.setDbFailAtPayment(doubleBlackRsp.isDbFailAtPayment());
			doubleBlackInfo.setEnrollCta(doubleBlackRsp.getEnrollCta());
			doubleBlackInfo.setMaxCurrentTier(doubleBlackRsp.getMaxCurrentTier());
			doubleBlackInfo.setMaxGreenTier(doubleBlackRsp.getMaxGreenTier());
			doubleBlackInfo.setMaxProgramTier(doubleBlackRsp.getMaxProgramTier());
			doubleBlackInfo.setMessageHeader(doubleBlackRsp.getMessageHeader());
			doubleBlackInfo.setMessageText(doubleBlackRsp.getMessageText());
			doubleBlackInfo.setMessageIcon(doubleBlackRsp.getMessageIcon());
			doubleBlackInfo.setMessageType(doubleBlackRsp.getMessageType());
			doubleBlackInfo.setMoreVerificationRequired(doubleBlackRsp.isMoreVerificationRequired());
			doubleBlackInfo.setMwPlusBalance(doubleBlackRsp.getMwPlusBalance());
			doubleBlackInfo.setNextTierMwPlus(doubleBlackRsp.getNextTierMwPlus());
			doubleBlackInfo.setRegisteredFirstName(doubleBlackRsp.getRegisteredFirstName());
			doubleBlackInfo.setRegisteredLastName(doubleBlackRsp.getRegisteredLastName());
			doubleBlackInfo.setTrackId(doubleBlackRsp.getTrackId());
			doubleBlackInfo.setUserEligible(doubleBlackRsp.isUserEligible());
			doubleBlackInfo.setUserEmail(doubleBlackRsp.getUserEmail());
			return doubleBlackInfo;
		}
		return null;
	}

	public Map<String, TotalPricing> getPriceMap(DisplayPriceBreakDown displayPriceBreakDown,
												 List<DisplayPriceBreakDown> displayPriceBreakDownList,
												 Map<String, String> expData, Integer roomCount, String askedCurrency,
												 String sellableType, Integer nightCount,
												 boolean isCorp, String segmentId, boolean buildToolTip, boolean groupBookingFunnel,
												 boolean groupBookingPrice, boolean ismyPartnerRequest, String listingType) {
		if (displayPriceBreakDown == null)
			return null;
		String priceDisplayMessage = null == roomCount ? null : getPriceDisplayMessage(expData, roomCount, sellableType, nightCount, groupBookingFunnel, listingType);
		Map<String, TotalPricing> priceMap = new HashMap<>();
		String priceMapKey;
		if (displayPriceBreakDown.getCouponInfo() != null) {
			priceMap.put(displayPriceBreakDown.getCouponInfo().getCouponCode(), getTotalPricing(displayPriceBreakDown, "", "", isCorp, segmentId, expData, groupBookingFunnel,false, null, null, null, null, 0.0,0.0));
			priceMap.get(displayPriceBreakDown.getCouponInfo().getCouponCode()).setPricingKey(displayPriceBreakDown.getPricingKey());
			priceMap.get(displayPriceBreakDown.getCouponInfo().getCouponCode()).setCouponDesc(displayPriceBreakDown.getCouponInfo().getDescription());
//			priceMap.get(displayPriceBreakDown.getCouponInfo().getCouponCode()).setSub(displayPriceBreakDown.getCouponInfo().getDescription());
//			priceMap.get(displayPriceBreakDown.getCouponInfo().getCouponCode()).setCouponDesc(displayPriceBreakDown.getCouponInfo().getDescription());
			priceMap.get(displayPriceBreakDown.getCouponInfo().getCouponCode()).setCouponAmount(displayPriceBreakDown.getCouponInfo().getDiscountAmount());
			priceMap.get(displayPriceBreakDown.getCouponInfo().getCouponCode()).setPriceDisplayMsg(priceDisplayMessage);

			//Price Suffix only to be shown when asked for Experiment PDO = PPPN
			if (expData != null && expData.containsKey("PDO") && PRICE_PPPN.equalsIgnoreCase(expData.get("PDO"))) {
				priceMap.get(displayPriceBreakDown.getCouponInfo().getCouponCode()).setPriceSuffix(priceDisplayMessage);
			}
			priceMap.get(displayPriceBreakDown.getCouponInfo().getCouponCode()).setPriceTaxMsg(
					getShowTaxMessage(expData, roomCount, displayPriceBreakDown.getTotalTax(), askedCurrency));
			priceMap.get(displayPriceBreakDown.getCouponInfo().getCouponCode()).setEmiBankDetails(getEmiBankDetails(displayPriceBreakDown.getEmiDetails()));
			if (buildToolTip)
				priceMap.get(displayPriceBreakDown.getCouponInfo().getCouponCode()).setPriceToolTip(buildPriceToolTip(displayPriceBreakDown, nightCount, askedCurrency));
			setGroupPriceAndSavingsText(priceMap, displayPriceBreakDown.getCouponInfo().getCouponCode(), roomCount, nightCount, (long) displayPriceBreakDown.getSavingPerc(), (long) displayPriceBreakDown.getTotalAmount(), groupBookingFunnel, groupBookingPrice,ismyPartnerRequest);
			priceMapKey = displayPriceBreakDown.getCouponInfo().getCouponCode();
		} else {
			priceMap.put("DEFAULT", getTotalPricing(displayPriceBreakDown, "", "", isCorp, segmentId, expData, groupBookingFunnel,false, null, null, null, null, 0.0,0.0));
			priceMap.get("DEFAULT").setPricingKey(displayPriceBreakDown.getPricingKey());
			priceMap.get("DEFAULT").setPriceDisplayMsg(priceDisplayMessage);
			//Price Suffix only to be shown when asked for Experiment PDO = PPPN
			if (expData != null && expData.containsKey("PDO") && PRICE_PPPN.equalsIgnoreCase(expData.get("PDO"))) {
				priceMap.get("DEFAULT").setPriceSuffix(priceDisplayMessage);
			}
			priceMap.get("DEFAULT").setPriceTaxMsg(
					getShowTaxMessage(expData, roomCount, displayPriceBreakDown.getTotalTax(), askedCurrency));
			priceMap.get("DEFAULT").setEmiBankDetails(getEmiBankDetails(displayPriceBreakDown.getEmiDetails()));
			if (buildToolTip)
				priceMap.get("DEFAULT").setPriceToolTip(buildPriceToolTip(displayPriceBreakDown, nightCount, askedCurrency));
			setGroupPriceAndSavingsText(priceMap, "DEFAULT", roomCount, nightCount, (long) displayPriceBreakDown.getSavingPerc(), (long) displayPriceBreakDown.getTotalAmount(), groupBookingFunnel, groupBookingPrice,ismyPartnerRequest);
			priceMapKey = "DEFAULT";
		}
		buildLinkedRatesPersuausions(priceMapKey,priceMap,displayPriceBreakDown);
		if (CollectionUtils.isNotEmpty(displayPriceBreakDownList)) {
			for (DisplayPriceBreakDown displayPriceBreakDownOther : displayPriceBreakDownList) {
				if (displayPriceBreakDownOther.getCouponInfo() == null)
					continue;
				priceMap.put(displayPriceBreakDownOther.getCouponInfo().getCouponCode(), getTotalPricing(displayPriceBreakDownOther, "", "", isCorp, segmentId, expData, groupBookingFunnel,false, null, null, null, null, 0.0,0.0));
				priceMap.get(displayPriceBreakDownOther.getCouponInfo().getCouponCode()).setPricingKey(displayPriceBreakDownOther.getPricingKey());
				priceMap.get(displayPriceBreakDownOther.getCouponInfo().getCouponCode()).setCouponDesc(displayPriceBreakDownOther.getCouponInfo().getDescription());
				priceMap.get(displayPriceBreakDownOther.getCouponInfo().getCouponCode()).setCouponAmount(displayPriceBreakDownOther.getCouponInfo().getDiscountAmount());
				priceMap.get(displayPriceBreakDownOther.getCouponInfo().getCouponCode()).setPriceDisplayMsg(priceDisplayMessage);
				priceMap.get(displayPriceBreakDownOther.getCouponInfo().getCouponCode()).setNewUserTypeCoupon(displayPriceBreakDownOther.getCouponInfo().getIsNewUser());
				//Price Suffix only to be shown when asked for Experiment PDO = PPPN
				if (expData != null && expData.containsKey("PDO") && PRICE_PPPN.equalsIgnoreCase(expData.get("PDO"))) {
					priceMap.get(displayPriceBreakDownOther.getCouponInfo().getCouponCode()).setPriceSuffix(priceDisplayMessage);
				}
				priceMap.get(displayPriceBreakDownOther.getCouponInfo().getCouponCode()).setPriceTaxMsg(
						getShowTaxMessage(expData, roomCount, displayPriceBreakDownOther.getTotalTax(), askedCurrency));
				priceMap.get(displayPriceBreakDownOther.getCouponInfo().getCouponCode()).setEmiBankDetails(getEmiBankDetails(displayPriceBreakDown.getEmiDetails()));
				if (buildToolTip)
					priceMap.get(displayPriceBreakDownOther.getCouponInfo().getCouponCode()).setPriceToolTip(buildPriceToolTip(displayPriceBreakDown, nightCount, askedCurrency));
				setGroupPriceAndSavingsText(priceMap, displayPriceBreakDownOther.getCouponInfo().getCouponCode(), roomCount, nightCount, (long) displayPriceBreakDown.getSavingPerc(), (long) displayPriceBreakDown.getTotalAmount(), groupBookingFunnel, groupBookingPrice,ismyPartnerRequest);
				priceMapKey = displayPriceBreakDownOther.getCouponInfo().getCouponCode();
				buildLinkedRatesPersuausions(priceMapKey,priceMap,displayPriceBreakDownOther);
			}
		}
		return priceMap;
	}

	private void buildLinkedRatesPersuausions(String priceMapKey, Map<String, TotalPricing> priceMap, DisplayPriceBreakDown displayPriceBreakDown) {

		try{
			LinkedRatePriceCalculations linkedRatePriceCalculations = null;
			if(MapUtils.isNotEmpty(displayPriceBreakDown.getLinkedRatePriceCalculationsMap())){
				for(Map.Entry<String,LinkedRatePriceCalculations> linkedRatePriceCalculationsEntry : displayPriceBreakDown.getLinkedRatePriceCalculationsMap().entrySet()) {
					linkedRatePriceCalculations = linkedRatePriceCalculationsEntry.getValue();
				}

				if(Objects.nonNull(linkedRatePriceCalculations)){
					String discountedPrice = Integer.toString(linkedRatePriceCalculations.getDisplayPriceDifference());
					String parentOriginalPrice = Integer.toString(linkedRatePriceCalculations.getParentOriginalPrice());
					TotalPricing totalPricing = priceMap.get(priceMapKey);
					totalPricing.setLinkedRPBottomSheetTitle(polyglotService.getTranslatedData(GI_LINKED_RATE_PLAN_BOTTOMSHEET_TITLE).replace("{discount}", Currency.INR.getCurrencySymbol() + discountedPrice));
					totalPricing.setLinkedRPDiscountMsg(polyglotService.getTranslatedData(GI_LINKED_RATE_PLAN_DISCOUNT_TEXT).replace("{discount}",Currency.INR.getCurrencySymbol() + discountedPrice));
					totalPricing.setLinkedRPOriginalPriceMsg(polyglotService.getTranslatedData(GI_LINKED_RATE_PLAN_ORIGINAL_PRICE_TEXT).replace("{parentOriginalPrice}",Currency.INR.getCurrencySymbol() + " " + parentOriginalPrice));
					totalPricing.setLinkedRatePriceCalculationsMap(displayPriceBreakDown.getLinkedRatePriceCalculationsMap());
				}
			}
		} catch (Exception e) {
			logger.error("Error in building linked rate persuasions", e);
		}

	}

	public void getPriceDetail(TotalPricing totalPricing, DayUseRoomsResponse dayUseRoomsResponse, DayUseSlotPlan slotPlan, boolean header) {
		if(header && CollectionUtils.isNotEmpty(totalPricing.getDetails())) {
			dayUseRoomsResponse.setPriceDetail( new DayUsePriceDetail());
			for (PricingDetails priceDetail : totalPricing.getDetails()) {
				if(StringUtils.isNotEmpty(priceDetail.getKey()) && TAXES_KEY.equals(priceDetail.getKey())){
					dayUseRoomsResponse.getPriceDetail().setPriceTaxMsg(priceDetail.getLabel());
					dayUseRoomsResponse.getPriceDetail().setTotalTax(priceDetail.getAmount());
				}
				if(StringUtils.isNotEmpty(priceDetail.getKey()) && AMOUNT_LABEL_TOTAL_AMOUNT.equals(priceDetail.getKey())){
					dayUseRoomsResponse.getPriceDetail().setPriceDisplayMsg(priceDetail.getLabel());
					dayUseRoomsResponse.getPriceDetail().setTotalPrice(priceDetail.getAmount());
				}
			}
			dayUseRoomsResponse.getPriceDetail().setCouponAmount(totalPricing.getCouponAmount());
		}else{
			slotPlan.setPriceDetail(new DayUsePriceDetail());
			for (PricingDetails priceDetail : totalPricing.getDetails()) {
				if(StringUtils.isNotEmpty(priceDetail.getKey()) && TAXES_KEY.equals(priceDetail.getKey())){
					slotPlan.getPriceDetail().setPriceTaxMsg(priceDetail.getLabel());
					slotPlan.getPriceDetail().setTotalTax(priceDetail.getAmount());
				}
				if(StringUtils.isNotEmpty(priceDetail.getKey()) && AMOUNT_LABEL_TOTAL_AMOUNT.equals(priceDetail.getKey())){
					slotPlan.getPriceDetail().setPriceDisplayMsg(priceDetail.getLabel());
					double totalPrice = priceDetail.getAmount();
					if (totalPricing.getCouponAmount() > 0 && totalPrice > totalPricing.getCouponAmount()) {
						totalPrice -= totalPricing.getCouponAmount();
					}
					slotPlan.getPriceDetail().setTotalPrice(totalPrice);
				}
			}
			if(slotPlan.getPriceDetail() != null) {
				slotPlan.getPriceDetail().setTotalPrice((slotPlan.getPriceDetail().getTotalPrice() != null ? slotPlan.getPriceDetail().getTotalPrice() : 0) +
						(slotPlan.getPriceDetail().getTotalTax() != null ? slotPlan.getPriceDetail().getTotalTax() : 0));
			}
			slotPlan.getPriceDetail().setCouponAmount(totalPricing.getCouponAmount());
		}
	}

	private void setGroupPriceAndSavingsText(Map<String, TotalPricing> priceMap, String couponCode, Integer roomCount, Integer nightCount, long savingPerc, long displayPrice, boolean groupBookingFunnel, boolean groupBookingPrice,boolean ismyPartnerRequest) {
		if (groupBookingFunnel) {
			NumberFormat numberFormat = com.ibm.icu.text.NumberFormat.getInstance(new Locale("en","IN"));
			String controller = MDC.get(MDCHelper.MDCKeys.CONTROLLER.getStringValue());
			//the check has been put inCase of search-roomapi not to add displayPrice in groupPriceText
			if (utility.isDetailPageAPI(controller) && ismyPartnerRequest) {
				priceMap.get(couponCode).setGroupPriceText(getGroupPriceText(roomCount, nightCount));
			}
			else{
				priceMap.get(couponCode).setGroupPriceText(getGroupPriceText(roomCount, nightCount, numberFormat.format(displayPrice),""));
			}
			if(savingPerc != 0.0 && groupBookingPrice) {
				priceMap.get(couponCode).setSavingsText(polyglotService.getTranslatedData(SAVING_PERC_TEXT)
						.replace("{PERCENTAGE}", String.valueOf(savingPerc)));
			}
		}
	}

	public String getGroupPriceText(Integer roomCount, Integer nightCount, String displayPrice, String client) {
		if(nightCount == 1 && roomCount == 1) {
			return polyglotService.getTranslatedData(Utility.getGroupPriceTextOrSavingPercKey(GROUP_PRICE_TEXT_ONE_NIGHT_ONE_ROOM, client))
					.replace(ConstantsTranslation.AMOUNT.toUpperCase(), String.valueOf(displayPrice))
					.replace("{NIGHT_COUNT}", String.valueOf(nightCount))
					.replace("{ROOM_COUNT}", String.valueOf(roomCount));

		} else if(nightCount == 1) {
			return polyglotService.getTranslatedData(Utility.getGroupPriceTextOrSavingPercKey(GROUP_PRICE_TEXT_ONE_NIGHT, client))
					.replace(ConstantsTranslation.AMOUNT.toUpperCase(), String.valueOf(displayPrice))
					.replace("{NIGHT_COUNT}", String.valueOf(nightCount))
					.replace("{ROOM_COUNT}", String.valueOf(roomCount));
		} else if(roomCount == 1) {
			return polyglotService.getTranslatedData(Utility.getGroupPriceTextOrSavingPercKey(GROUP_PRICE_TEXT_ONE_ROOM, client))
					.replace(ConstantsTranslation.AMOUNT.toUpperCase(), String.valueOf(displayPrice))
					.replace("{NIGHT_COUNT}", String.valueOf(nightCount))
					.replace("{ROOM_COUNT}", String.valueOf(roomCount));
		} else {
			return polyglotService.getTranslatedData(Utility.getGroupPriceTextOrSavingPercKey(GROUP_PRICE_TEXT, client))
					.replace(ConstantsTranslation.AMOUNT.toUpperCase(), String.valueOf(displayPrice))
					.replace("{NIGHT_COUNT}", String.valueOf(nightCount))
					.replace("{ROOM_COUNT}", String.valueOf(roomCount));
		}
	}

	/**
	 * * GroupPriceText build method in case of MyPartner Group Funnel
	 * @param roomCount
	 * @param nightCount
	 * @return
	 */
	public String getGroupPriceText(Integer roomCount, Integer nightCount) {
		if(nightCount == 1) {
			return polyglotService.getTranslatedData(GROUP_PRICE_TEXT_ONE_NIGHT_WITH_TAX)
					.replace("{NIGHT_COUNT}", String.valueOf(nightCount))
					.replace("{ROOM_COUNT}", String.valueOf(roomCount));
		}else {
			return polyglotService.getTranslatedData(GROUP_PRICE_TEXT_WITH_TAX)
					.replace("{NIGHT_COUNT}", String.valueOf(nightCount))
					.replace("{ROOM_COUNT}", String.valueOf(roomCount));
		}
	}

	private EMIDetail getEmiBankDetails(Emi emiDetails) {
		EMIDetail emiDetail = null;
		if (null != emiDetails) {
			emiDetail = new EMIDetail();
			emiDetail.setInterestType(emiDetails.getInterestType());
			emiDetail.setTermType(emiDetails.getTermType());
			emiDetail.setAmount((double) emiDetails.getEmiAmount());
			emiDetail.setBankId(emiDetails.getBankId());
			emiDetail.setBankName(emiDetails.getBankName());
			emiDetail.setInterestRate((double) emiDetails.getInterestRate());
			emiDetail.setInterestType(emiDetails.getInterestType());
			emiDetail.setPayOption(emiDetails.getPayOption());
			emiDetail.setTaxIncluded(emiDetails.isTaxIncluded());
			emiDetail.setTenure(emiDetails.getTenure());
			emiDetail.setTotalCost(emiDetails.getTotalCost());
			emiDetail.setTotalInterest(emiDetails.getTotalInterest());
			emiDetail.setType(emiDetails.getEmiType());
		}
		return emiDetail;
	}

	private String buildPriceToolTip(DisplayPriceBreakDown displayPriceBreakDown, Integer nightCount, String askedCurrency) {
		if (displayPriceBreakDown == null || nightCount == null || StringUtils.isBlank(askedCurrency))
			return null;
		String controller = MDC.get(MDCHelper.MDCKeys.CONTROLLER.getStringValue());
		String msg = null;
		if (utility.isDetailPageAPI(controller)) {
			/* DisplayPrice in case of review page includes CDF, hence calculation of amount for both pages would be different. */
			if (MapUtils.isNotEmpty(displayPriceBreakDown.getOfferDiscountBreakup())
					&& displayPriceBreakDown.getOfferDiscountBreakup().containsKey(PromotionalOfferType.LOS_DEAL)
					&& displayPriceBreakDown.getOfferDiscountBreakup().get(PromotionalOfferType.LOS_DEAL) != null
					&& displayPriceBreakDown.getOfferDiscountBreakup().get(PromotionalOfferType.LOS_DEAL) > 0.0d) {
				double totalAmount = (displayPriceBreakDown.getDisplayPrice()
						+ (displayPriceBreakDown.isTaxIncluded() ? 0 : displayPriceBreakDown.getTotalTax()))
						* displayPriceBreakDown.getPricingDivisor();
				totalAmount = Utility.round(totalAmount, 0);
				String currency = Currency.getCurrencyEnum(StringUtils.isNotBlank(askedCurrency) ? askedCurrency : "INR").getCurrencySymbol();
				msg = (polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TOOL_TIP))
						.replace(ConstantsTranslation.DAYS, nightCount.toString())
						.replace(ConstantsTranslation.AMOUNT, String.valueOf(totalAmount))
						.replace(ConstantsTranslation.CURRENCY, currency);

			}
		}
		return msg;
	}

	private String getShowTaxMessage(Map<String, String> expData, Integer roomCount, double totalTax, String askedCurrency) {
		boolean showTax = false;
		if (totalTax == 0.0d) {
			showTax = false;
		} else if (MapUtils.isNotEmpty(expData)) {
			showTax = (("True".equalsIgnoreCase(expData.get("ST"))) || ("t".equalsIgnoreCase(expData.get("ST"))));
		}

		if (showTax) {
			Double amt = new Double(totalTax);
			return "+ " + Currency.getCurrencyEnum(StringUtils.isNotBlank(askedCurrency) ? askedCurrency : "INR").getCurrencySymbol() + " " + amt.intValue() + " taxes & service fees";
		}
		return null;
	}

	private String getPriceDisplayMessage(Map<String, String> expData, Integer roomCount, String sellableType,
										  Integer nightCount, boolean groupBookingFunnel, String listingType) {
		String exp = "PN";
		if (MapUtils.isNotEmpty(expData) && expData.containsKey("PDO")) {
			exp = expData.get("PDO");
		}

		if (StringUtils.isNotBlank(sellableType) && "bed".equalsIgnoreCase(sellableType)) {
			sellableType = polyglotService.getTranslatedData("BED_SELLABLE_TYPE");
		} else {
			sellableType = polyglotService.getTranslatedData("ROOM_SELLABLE_TYPE");
		}

		switch (exp) {
			case "PRN":
			case "PRNT":
				if(groupBookingFunnel) {
					return polyglotService.getTranslatedData("GROUP_PER_ROOM_PER_NIGHT");
				}
				return polyglotService.getTranslatedData("PER_ROOM_PER_NIGHT").replace("{room}", sellableType);
			case "PN":
				if ((roomCount != null && roomCount.intValue() == 1) || LISTING_TYPE_ENTIRE.equalsIgnoreCase(listingType))
					return polyglotService.getTranslatedData("PER_NIGHT_TEXT");
				else
					return polyglotService.getTranslatedData("PER_NIGHT_FOR_NUM_ROOMS").replace("{num}", roomCount.toString()).replace("{rooms}", sellableType);


			case "PNT":
				if (nightCount == null)
					return polyglotService.getTranslatedData("PER_NIGHT_TEXT");

				if (roomCount != null && roomCount.intValue() == 1)
					if (nightCount != null && nightCount.intValue() == 1)
						return polyglotService.getTranslatedData("PER_NIGHT_TEXT");
					else
						return polyglotService.getTranslatedData("FOR_NUM_ROOMS_PER_NIGHT_WITH_TAX").replace("{num}", roomCount.toString());
				else if (nightCount != null && nightCount.intValue() == 1)
					return polyglotService.getTranslatedData("PER_NIGHT_FOR_NUM_ROOMS_TEXT").replace("{num}", roomCount.toString());
				else
					return polyglotService.getTranslatedData("FOR_NUM_ROOMS_PER_NIGHT_WITH_TAX").replace("{num}", roomCount.toString());
			case "TP":
				if (nightCount == null)
					return polyglotService.getTranslatedData("TOTAL_PRICE_TEXT");

				if (roomCount != null && roomCount.intValue() == 1)
					if (nightCount != null && nightCount.intValue() == 1)
						return polyglotService.getTranslatedData("PER_NIGHT_TEXT");
					else
						return polyglotService.getTranslatedData("FOR_NUM_NIGHTS").replace("{num}", nightCount.toString());
				else if (nightCount != null && nightCount.intValue() == 1)
					return polyglotService.getTranslatedData("PER_NIGHT_FOR_NUM_ROOMS_TEXT").replace("{num}", roomCount.toString());
				else
					return polyglotService.getTranslatedData("FOR_NUM_NIGHTS_NUM_ROOMS").replace("{num_nights}", nightCount.toString()).replace("{num_rooms}", roomCount.toString());

			case "TPT":
				return polyglotService.getTranslatedData("TOTAL_PRICE_TEXT");

			case "PPPN":
				return PPPN_PRICE_SUFFIX;
		}
		return "";
	}

	public void updateCancelPolicyDescription(RatePlan ratePlan, List<CancelPenalty> cancelPenalty) {
		if (CollectionUtils.isNotEmpty(cancelPenalty) && cancelPenalty.get(0).getPenaltyDescription() != null
				&& cancelPenalty.get(0).getPenaltyDescription().getDescription() != null) {
			ratePlan.getCancellationPolicy().setDescription(cancelPenalty.get(0).getPenaltyDescription().getDescription());
		}
	}

	public RatePolicy getConfirmationPolicy(RoomTypeDetails roomTypeDetails) {
		RatePolicy ConfirmationPolicy = null;
		for (Map.Entry<String, RoomType> roomType : roomTypeDetails.getRoomType().entrySet()) {
			getConfirmationPolicy(roomType.getValue().getRatePlanList());
		}
		return ConfirmationPolicy;
	}

	private VideoInfo buildVideoDetails(VideoDetails videoDetails) {
		if (videoDetails != null) {
			VideoInfo video = new VideoInfo();
			video.setUrl(videoDetails.getUrl());
			return video;
		}
		return null;
	}


	public List<MediaInfo> buildMedia(MediaDetails mediaDetails, Map<String, String> expDataMap) {
		if(mediaDetails==null){
			return null;
		}
		List<String> images = new ArrayList<>();
		List<VideoInfo> videos = new ArrayList<>();
		if(CollectionUtils.isNotEmpty(mediaDetails.getImages())) {
			mediaDetails.getImages().forEach(imageDetails -> {
				if (imageDetails != null && StringUtils.isNotEmpty(imageDetails.getUrl())) {
					images.add(imageDetails.getUrl());
				}
			});
		}
		if(CollectionUtils.isNotEmpty(mediaDetails.getVideos())) {
			mediaDetails.getVideos().forEach(videoDetails -> {
				VideoInfo videoInfo = buildVideoDetails(videoDetails);
				if (videoInfo != null) {
					videos.add(videoInfo);
				}
			});
		}


		if (CollectionUtils.isEmpty(images) && CollectionUtils.isEmpty(videos)) {
			return null;
		}
		int mediaLimit = 5;
//		if (MapUtils.isNotEmpty(expDataMap) && expDataMap.containsKey(ExperimentKeys.carouselImageCount.name()) && Integer.parseInt(expDataMap.get(ExperimentKeys.carouselImageCount.name())) > 0 && Utility.isAppRequest()) {
//			try {
//				mediaLimit = Integer.parseInt(expDataMap.get(ExperimentKeys.carouselImageCount.name()));
//			} catch (Exception e) {
//				mediaLimit = listingMediaLimit;
//			}
//		}
//		if (StringUtils.isNotBlank(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue())))
//			mediaLimitMap.getOrDefault(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue()), 5);

		mediaLimit = Math.min((CollectionUtils.isNotEmpty(images) ? images.size() : 0) + (CollectionUtils.isNotEmpty(videos) ? videos.size() : 0), mediaLimit);

		List<MediaInfo> list = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(images)) {
			images.stream().limit(mediaLimit).forEach(e -> {
				MediaInfo m = new MediaInfo();
				m.setUrl(e);
				m.setMediaType("IMAGE");
				list.add(m);
			});
		}

		if (CollectionUtils.isNotEmpty(videos)) {
			List<MediaInfo> videoList = new ArrayList<>();
			for (VideoInfo hotelVideo : videos) {
				MediaInfo m = new MediaInfo();
				m.setTags(hotelVideo.getTags());
				m.setText(hotelVideo.getText());
				m.setThumbnailURL(hotelVideo.getThumbnailUrl());
				m.setTitle(hotelVideo.getTitle());
				m.setUrl(hotelVideo.getUrl());
				m.setMediaType("VIDEO");
				videoList.add(m);
			}

			if (CollectionUtils.isNotEmpty(videoList)) {
				/* Experiment VIDEO:0/1/2 is read from client.
				 0 (videos even if available for a hotel wont be shown on listing screen)
				 1 (the video if available for a hotel will be shown on first position)
				 2 (the video if available for a hotel will be shown on second position)
				 Also - Remove image and then add video. Total media count should be as defined in listing.hotel.media.limit
				 Hence ex 5 media - 4 images/1 video OR 5 images.
				*/
				int position = -1;
				String clientExpKey = "VIDEO";
				if (MapUtils.isNotEmpty(expDataMap)) {
					if (expDataMap.containsKey(clientExpKey)) {
						position = Integer.parseInt(expDataMap.get(clientExpKey));
					}
				}

				if (position == 1 || position == 2) {
					if (list.size() == mediaLimit)
						list.remove(list.size() - 1);
					list.add(position - 1, videoList.get(0));
				} else if (position != 0) {
					list.addAll(videoList);
				}
			}
		}
		if (CollectionUtils.isNotEmpty(list) && list.size() > mediaLimit)
			return list.subList(0, mediaLimit);
		if (CollectionUtils.isEmpty(list))
			return null;
		return list;
	}


	public List<MediaInfo> buildMedia(List<String> mainImages, List<VideoInfo> videoInfos, String expData) {
		if (CollectionUtils.isEmpty(mainImages) && CollectionUtils.isEmpty(videoInfos)) {
			return null;
		}

		int mediaLimit = 5;
		if (StringUtils.isNotBlank(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue())))
			mediaLimitMap.getOrDefault(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue()), 5);
		mediaLimit = Math.min((CollectionUtils.isNotEmpty(mainImages) ? mainImages.size() : 0) + (CollectionUtils.isNotEmpty(videoInfos) ? videoInfos.size() : 0), mediaLimit);

		List<MediaInfo> list = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(mainImages)) {
			mainImages.stream().limit(mediaLimit).forEach(e -> {
				MediaInfo m = new MediaInfo();
				m.setUrl(e);
				m.setMediaType("IMAGE");
				list.add(m);
			});
		}

		if (CollectionUtils.isNotEmpty(videoInfos)) {
			List<MediaInfo> videoList = new ArrayList<>();
			for (VideoInfo hotelVideo : videoInfos) {
				MediaInfo m = new MediaInfo();
				m.setTags(hotelVideo.getTags());
				m.setText(hotelVideo.getText());
				m.setThumbnailURL(hotelVideo.getThumbnailUrl());
				m.setTitle(hotelVideo.getTitle());
				m.setUrl(hotelVideo.getUrl());
				m.setMediaType("VIDEO");
				videoList.add(m);
			}

			if (CollectionUtils.isNotEmpty(videoList)) {
				/* Experiment VIDEO:0/1/2 is read from client.
				 0 (videos even if available for a hotel wont be shown on listing screen)
				 1 (the video if available for a hotel will be shown on first position)
				 2 (the video if available for a hotel will be shown on second position)
				 Also - Remove image and then add video. Total media count should be as defined in listing.hotel.media.limit
				 Hence ex 5 media - 4 images/1 video OR 5 images.
				*/
				int position = -1;
				String clientExpKey = "VIDEO";
				if (StringUtils.isNotBlank(expData)) {
					Type type = new TypeToken<Map<String, String>>() {
					}.getType();
					try {
						expData = expData.replaceAll("^\"|\"$", "");
						Map<String, String> expDataMap = gson.fromJson(expData, type);
						if (expDataMap.containsKey(clientExpKey)) {
							position = Integer.parseInt(expDataMap.get(clientExpKey));
						}
					} catch (Exception e) {
						// Do Nothing - Experiment Data could not be parsed. Add all videos.
						position = 3;
					}
				}
				try {
					if (position == 0) {
						// DO not add videos
					} else if (position == 1 || position == 2) {
						if (list.size() == mediaLimit)
							list.remove(list.size() - 1);
						list.add(position - 1, videoList.get(0));
					} else {
						list.addAll(videoList);
					}
				} catch (NumberFormatException e) {
					// Do Nothing - Experiment Data could not be parsed. Add all videos.
					list.addAll(videoList);
				}
			}
		}
		if (CollectionUtils.isNotEmpty(list) && list.size() > mediaLimit)
			return list.subList(0, mediaLimit);
		if (CollectionUtils.isEmpty(list))
			return null;
		return list;
	}

	private RatePolicy getConfirmationPolicy(Map<String, com.mmt.hotels.model.response.pricing.RatePlan> ratePlanList) {
		RatePolicy confirmationPolicy = null;
		for (Map.Entry<String, com.mmt.hotels.model.response.pricing.RatePlan> ratePlan : ratePlanList.entrySet()) {
			RatePolicy ratePolicy = ratePlan.getValue().getConfirmationPolicy();
			if (confirmationPolicy == null && ratePolicy != null) {
				confirmationPolicy = ratePolicy;
				if (Constants.MOST_RESTRICTED_POLICY.equalsIgnoreCase(ratePolicy.getMostRestrictive())) {
					return confirmationPolicy;
				}
			} else if (confirmationPolicy != null && Constants.MOST_RESTRICTED_POLICY.equalsIgnoreCase(ratePolicy.getMostRestrictive())) {
				confirmationPolicy = ratePolicy;
				return confirmationPolicy;
			}
		}
		return confirmationPolicy;
	}

	private RatePolicy getConfirmationPolicy(List<RoomType> roomTypes) {
		RatePolicy confirmationPolicy = null;
		if (CollectionUtils.isNotEmpty(roomTypes)) {
			for (RoomType roomType : roomTypes) {
				getConfirmationPolicy(roomType.getRatePlanList());
			}
		}
		return confirmationPolicy;
	}

	public List<com.mmt.hotels.clientgateway.response.FacilityGroup> buildAmenities(List<com.mmt.model.FacilityGroup> facilityWithGrp, List<com.mmt.model.FacilityGroup> starFacilities) {
		if (CollectionUtils.isEmpty(facilityWithGrp))
			return null;
		List<com.mmt.hotels.clientgateway.response.FacilityGroup> amenitiesCGList = new ArrayList<>();

		Set<String> starAmenitySet = new HashSet<>();

		if (CollectionUtils.isNotEmpty(starFacilities)) {
			for (com.mmt.model.FacilityGroup facilityGroup : starFacilities) {
				if (CollectionUtils.isNotEmpty(facilityGroup.getFacilities())) {
					for (Facility facility : facilityGroup.getFacilities()) {
						starAmenitySet.add(facility.getName());
					}
				}

			}
		}

		List<com.mmt.hotels.clientgateway.response.Facility> starFacilityCGs = new ArrayList<>();

		for (com.mmt.model.FacilityGroup facilityGroup : facilityWithGrp) {
			com.mmt.hotels.clientgateway.response.FacilityGroup facility = new com.mmt.hotels.clientgateway.response.FacilityGroup();

			facility.setName(facilityGroup.getName());
			List<com.mmt.hotels.clientgateway.response.Facility> facilityCGs = new ArrayList<>();
			for (Facility facilityHes : facilityGroup.getFacilities()) {

				com.mmt.hotels.clientgateway.response.Facility facilityCG = new com.mmt.hotels.clientgateway.response.Facility();
				facilityCG.setAttributeName(facilityHes.getAttributeName());
				facilityCG.setCategoryName(facilityHes.getCategoryName());
				facilityCG.setDisplayType(facilityHes.getDisplayType());
				facilityCG.setHighlightedName(facilityHes.getHighlightedName());
				facilityCG.setName(facilityHes.getName());
				facilityCG.setSequence(facilityHes.getSequence());
				facilityCG.setTags(facilityHes.getTags());
				facilityCG.setType(facilityHes.getType());
				if(facilityHes.getChildAttributes() != null) {
					List<com.mmt.hotels.clientgateway.response.AttributesFacility> childAttributesCG = buildChildAttributesCgFromHes(facilityHes.getChildAttributes());
					facilityCG.setChildAttributes(CollectionUtils.isNotEmpty(childAttributesCG) ? childAttributesCG : null);
				}

				if (starAmenitySet.contains(facilityHes.getName())) {
					starAmenitySet.remove(facilityHes.getName());
					starFacilityCGs.add(facilityCG);
				} else {
					facilityCGs.add(facilityCG);
				}


			}

			if (CollectionUtils.isNotEmpty(facilityCGs)) {
				facility.setFacilities(facilityCGs);
				amenitiesCGList.add(facility);
			}
		}

		if (CollectionUtils.isNotEmpty(starFacilities) && !starAmenitySet.isEmpty()) {
			for (com.mmt.model.FacilityGroup facilityGroup : starFacilities) {
				for (Facility facilityHes : facilityGroup.getFacilities()) {

					if (!starAmenitySet.contains(facilityHes.getName())) {
						continue;
					}
					com.mmt.hotels.clientgateway.response.Facility facilityCG = new com.mmt.hotels.clientgateway.response.Facility();
					facilityCG.setAttributeName(facilityHes.getAttributeName());
					facilityCG.setCategoryName(facilityHes.getCategoryName());
					facilityCG.setDisplayType(facilityHes.getDisplayType());
					facilityCG.setHighlightedName(facilityHes.getHighlightedName());
					facilityCG.setName(facilityHes.getName());
					facilityCG.setSequence(facilityHes.getSequence());
					facilityCG.setTags(facilityHes.getTags());
					facilityCG.setType(facilityHes.getType());
					if(facilityHes.getChildAttributes() != null) {
						List<com.mmt.hotels.clientgateway.response.AttributesFacility> childAttributesCG = buildChildAttributesCgFromHes(facilityHes.getChildAttributes());
						facilityCG.setChildAttributes(CollectionUtils.isNotEmpty(childAttributesCG) ? childAttributesCG : null);
					}
					starFacilityCGs.add(facilityCG);
				}
			}

		}

		if (CollectionUtils.isNotEmpty(starFacilityCGs)) {
			Collections.sort(starFacilityCGs,comparingInt(starFacilityCG -> (starFacilityCG.getSequence() == null ? Integer.MAX_VALUE : starFacilityCG.getSequence())));
			com.mmt.hotels.clientgateway.response.FacilityGroup facility = new com.mmt.hotels.clientgateway.response.FacilityGroup();
			facility.setName(polyglotService.getTranslatedData(STAR_FACILITIES));
			facility.setType(Constants.BOLD_TYPE);
			facility.setFacilities(starFacilityCGs);
			facility.setId(SIGNATURE_AMENITIES);
			amenitiesCGList.add(0, facility);
		}
		return amenitiesCGList;
	}

	public List<String> buildHighlightedAmenities(Set<String> highlights) {
		if (CollectionUtils.isEmpty(highlights))
			return null;
		List<String> highlightedAmenities = new ArrayList<>();
		for (String h : highlights) {
			highlightedAmenities.add(h);
		}
		return highlightedAmenities;
	}

	public ReviewSummary buildReviewSummary(String countryCode, Map<OTA, JsonNode> reviewSummaryMap,DeviceDetails deviceDetails, Map<String, String> expDataMap) {

		if (MapUtils.isEmpty(reviewSummaryMap))
			return null;
		JsonNode ratingSummary = null;
		OTA ota = null;
		if (Constants.DOM_COUNTRY.equalsIgnoreCase(countryCode)) {
			ratingSummary = reviewSummaryMap.get(OTA.MMT);
			ota = OTA.MMT;
		} else {
			// Clean Up as part of GIHTL-15565 for SHOW_MMT_RATING_EXP experiment
			// boolean isMMTRatingExp = MapUtils.isNotEmpty(expDataMap) && Constants.TRUE.equalsIgnoreCase(expDataMap.get(Constants.SHOW_MMT_RATING_EXP));
			ota = utility.getPreferredOtaFromExp(reviewSummaryMap, false);
			ratingSummary = reviewSummaryMap.get(ota);
		}
		if(ratingSummary == null){
			ratingSummary = reviewSummaryMap.get(OTA.EXT);
			ota = OTA.EXT;

			//Added temporarily for backward compatibility . Not needed once Android/Ios version 8.5.9/8.5.6 are rolled out 100%
			if(deviceDetails!=null && ("ANDROID".equalsIgnoreCase(deviceDetails.getBookingDevice()) || "IOS".equalsIgnoreCase(deviceDetails.getBookingDevice()))){
				if(!utility.isValidAppVersion(deviceDetails.getAppVersion(),"ANDROID".equalsIgnoreCase(deviceDetails.getBookingDevice())?"8.5.9":"8.5.6")){
					return null;
				}
			}

		}

		if (ratingSummary == null || (ratingSummary.get("cumulativeRating") != null && ratingSummary.get("cumulativeRating").intValue() == 0))
			return null;
		ReviewSummary reviewSummary = new ReviewSummary();
		reviewSummary.setSource(ota.getValue());
		if (ratingSummary.get("cumulativeRating") != null) {
			reviewSummary.setCumulativeRating(ratingSummary.get("cumulativeRating").floatValue());
		}
		if (ratingSummary.get("totalRatingCount") != null) {
			reviewSummary.setTotalRatingCount(ratingSummary.get("totalRatingCount").intValue());
		}
		if (ratingSummary.get("totalReviewsCount") != null) {
			reviewSummary.setTotalReviewCount(ratingSummary.get("totalReviewsCount").intValue());
		}
		if (ratingSummary.get("disableLowRating") != null) {
			reviewSummary.setDisableLowRating(ratingSummary.get("disableLowRating").booleanValue());
		}
		if (ratingSummary.get("preferredOTA") != null) {
			reviewSummary.setPreferredOTA(ratingSummary.get("preferredOTA").booleanValue());
		}
		JsonNode travellerRatingSummary = ratingSummary.get("travellerRatingSummary");
		if (travellerRatingSummary != null) {
			reviewSummary.setHotelRatingSummary(getHotelRatingSummary(objectMapperUtil.getObjectFromJsonNode(travellerRatingSummary, new TypeReference<TravellerRatingSummaryDTO>() {
			})));
		}
		if (ratingSummary.get("ratingText") != null) {
			reviewSummary.setRatingText(objectMapperUtil.getObjectFromJsonNode(ratingSummary.get("ratingText"), new TypeReference<String>() {
			}));
		}
		if (ratingSummary.get("additionalInfo") != null) {
			reviewSummary.setAdditionalInfo(objectMapperUtil.getObjectFromJsonNode(ratingSummary.get("additionalInfo"), new TypeReference<FlyfishReviewAdditionalInfo>() {
			}));
		}

		if (ota == OTA.EXT) {
			reviewSummary.setDisclaimer(ratingSummary.get("disclaimer"));
			reviewSummary.setReviewHighlights(ratingSummary.get("reviewHighlights"));
			reviewSummary.setReviewHighlightTitle(ratingSummary.get("reviewHighlightTitle"));
		}

		return reviewSummary;
	}


	public ReviewSummary buildReviewSummary(ListingReviewDetails userReviewSummary) {
		if (userReviewSummary == null) {
			return null;
		}
		ReviewSummary reviewSummary = new ReviewSummary();
		reviewSummary.setCumulativeRating((float) userReviewSummary.getRating());
		reviewSummary.setTotalReviewCount(userReviewSummary.getTotalReviewCount());
		reviewSummary.setTotalRatingCount(userReviewSummary.getTotalRatingCount());
		reviewSummary.setRatingText(userReviewSummary.getRatingText());
		if (StringUtils.isNotBlank(userReviewSummary.getOta())) {
			reviewSummary.setPreferredOTA(true);
			reviewSummary.setSource(userReviewSummary.getOta().toUpperCase());
		}

		if (CollectionUtils.isNotEmpty(userReviewSummary.getSubRatings())) {
			reviewSummary.setHotelRatingSummary(userReviewSummary.getSubRatings().stream().map(topicWiseRatings -> {
				ConceptSummary conceptSummary = new ConceptSummary();
				conceptSummary.setReviewCount(topicWiseRatings.getReviewCount());
				conceptSummary.setDisplayText(topicWiseRatings.getName());
				conceptSummary.setValue(topicWiseRatings.getRating());
				conceptSummary.setShow(topicWiseRatings.isShow());
				return conceptSummary;
			}).collect(Collectors.toList()));
		}

		return reviewSummary;
	}
	public List<ConceptSummary> getHotelRatingSummary(TravellerRatingSummaryDTO travellerRatingSummaryDTO) {
		if (travellerRatingSummaryDTO == null || CollectionUtils.isEmpty(travellerRatingSummaryDTO.getHotelSummary())) {
			return null;
		}

		List<ConceptSummary> hotelratingSummary = new ArrayList<>();
		for (ConceptSummaryDTO conceptSummaryDTO : travellerRatingSummaryDTO.getHotelSummary()) {
			ConceptSummary conceptSummary = new ConceptSummary();
			conceptSummary.setConcept(conceptSummaryDTO.getConcept());
			conceptSummary.setHeroTag(conceptSummaryDTO.isHeroTag());
			conceptSummary.setReviewCount(conceptSummaryDTO.getReviewCount());
			conceptSummary.setShow(conceptSummaryDTO.getShow());
			conceptSummary.setSubConcepts(buildSubConcepts(conceptSummaryDTO.getSubConcepts()));
			conceptSummary.setValue(conceptSummaryDTO.getValue());
			conceptSummary.setDisplayText(conceptSummaryDTO.getDisplayText());
			hotelratingSummary.add(conceptSummary);
		}
		return hotelratingSummary;
	}

	private List<SubConcept> buildSubConcepts(List<SubConceptDTO> subConceptDTOList) {
		List<SubConcept> subConcepts = null;
		if (CollectionUtils.isNotEmpty(subConceptDTOList)) {
			subConcepts = new ArrayList<>();
			for (SubConceptDTO subConceptDTO : subConceptDTOList) {
				SubConcept subConcept = new SubConcept();
				subConcept.setPriorityScore(subConceptDTO.getPriorityScore());
				subConcept.setRelatedReviewCount(subConceptDTO.getRelatedReviewCount());
				subConcept.setSentiment(subConceptDTO.getSentiment());
				subConcept.setSubConcept(subConceptDTO.getSubConcept());
				subConcept.setTagType(subConceptDTO.getTagType());
				subConcept.setDisplayText(subConceptDTO.getDisplayText());
				subConcepts.add(subConcept);
			}
		}
		return subConcepts;
	}

	public GeoLocation buildGeoLocation(com.mmt.hotels.model.response.searchwrapper.GeoLocation geoLocation) {
		if (geoLocation == null)
			return null;
		GeoLocation geoLocationCG = new GeoLocation();
		geoLocationCG.setDistance(geoLocation.getDistanceMeter());
		geoLocationCG.setLatitude(StringUtils.isNotBlank(geoLocation.getLatitude()) ? Double.valueOf(geoLocation.getLatitude()) : null);
		geoLocationCG.setLongitude(StringUtils.isNotBlank(geoLocation.getLongitude()) ? Double.valueOf(geoLocation.getLongitude()) : null);
		return geoLocationCG;
	}

	public GeoLocation buildGeoLocation(GeoLocationDetails geoLocation) {
		if (geoLocation == null)
			return null;
		GeoLocation geoLocationCG = new GeoLocation();
		//geoLocationCG.setDistance(geoLocation.getDistanceMeter());
		geoLocationCG.setLatitude(StringUtils.isNotBlank(geoLocation.getLatitude()) ? Double.valueOf(geoLocation.getLatitude()) : null);
		geoLocationCG.setLongitude(StringUtils.isNotBlank(geoLocation.getLongitude()) ? Double.valueOf(geoLocation.getLongitude()) : null);
		return geoLocationCG;
	}


	public List<Poi> getPois(List<POIInfo> poiList) {
		if (CollectionUtils.isEmpty(poiList)) return null;
		List<Poi> pois = new LinkedList<>();
		for (POIInfo poiInfoHES : poiList) {
			Poi poi = new Poi();
			BeanUtils.copyProperties(poiInfoHES, poi);
			BeanUtils.copyProperties(poiInfoHES.getCentre(), poi.getCentre());
			if (null != poiInfoHES.getMeta()) {
				poi.setMeta(new MetaInfo());
				BeanUtils.copyProperties(poiInfoHES.getMeta(), poi.getMeta());
				poi.getMeta().setRanking(poiInfoHES.getMeta().getRanking());
				if (poiInfoHES.getMeta().getCategory() != null && StringUtils.isNotBlank(poiInfoHES.getMeta().getCategory().getName())) {
					poi.getMeta().setCategory(poiInfoHES.getMeta().getCategory().getName());
				}
			}
			pois.add(poi);
		}
		return pois;
	}

	public String getCorporateSegmentId(RoomTypeDetails roomTypeDetails) {
		if (null == roomTypeDetails) return "";
		return getCorporateSegmentId(roomTypeDetails.getRoomType().values().stream().collect(Collectors.toList()));
	}

	public String getCorporateSegmentId(List<RoomType> roomTypes) {
		String corpSegmentId = "";
		if (CollectionUtils.isEmpty(roomTypes)) return "";
		Optional<com.mmt.hotels.model.response.pricing.RatePlan> corpRatePlan = roomTypes.stream().map(roomType -> roomType.getRatePlanList().values()).
				flatMap(Collection::stream).filter(ratePlan -> corpSegments.contains(ratePlan.getSegmentId())).findFirst();
		if (corpRatePlan.isPresent()) return corpRatePlan.get().getSegmentId();
		return corpSegmentId;
	}

	public List<SearchWrapperHotelEntity> convertAbridgedIntoSearchWrapperHotelEntity(List<SearchWrapperHotelEntity> list) {
		if (CollectionUtils.isEmpty(list)) {
			return null;
		}
		List<SearchWrapperHotelEntity> newRespList = new ArrayList<>();
		for (SearchWrapperHotelEntityAbridged abridged : list) {
			SearchWrapperHotelEntity searchWrapperHotelEntity = new SearchWrapperHotelEntity();
			BeanUtils.copyProperties(abridged, searchWrapperHotelEntity);
			newRespList.add(searchWrapperHotelEntity);
		}
		return newRespList;
	}

	public LinkedHashMap<String, PersuasionResponse> buildSafetyPersuasionList(List<String> categories) {


		if (CollectionUtils.isEmpty(categories)) {
			return null;
		}

		LinkedHashMap<String, PersuasionResponse> persuasionList = new LinkedHashMap<>();

		if (StringUtils.isNotBlank(mySafetyDataPolyglot)) {

			try {

				persuasionResponseMap = objectMapperUtil.getObjectFromJsonWithType(mySafetyDataPolyglot,
						new TypeReference<Map<String, Map<String, PersuasionResponse>>>() {
						}, DependencyLayer.CLIENTGATEWAY);

				polyglotHelper.translatePersuasionMap(persuasionResponseMap);

				Map<String, PersuasionResponse> persuasionMap = persuasionResponseMap.get("mySafetyData");

				for (String category : categories) {

					if (persuasionMap.containsKey(category)) {

						persuasionList.put(category, persuasionMap.get(category));
					}
				}

			} catch (Exception e) {
				logger.warn("Error in buildSafetyPersuasionList");
			}


		}
		return persuasionList;

	}


	public Map<String, HotelTag> buildValueStaysHotelTag(String tagTitle, Map<String, HotelTag> hotelTagMap) {
		if(MapUtils.isEmpty(hotelTagMap)) {
			hotelTagMap = new HashMap<>();
		}
		HotelTag hotelTag = new HotelTag();
		hotelTag.setTitle(polyglotService.getTranslatedData(tagTitle));
		hotelTag.setBackground(valueStayBackground);
		hotelTag.setIcon(Utility.isGCC() ? iconUrlGcc : iconUrl);
		hotelTag.setType(HotelTagType.VALUE_STAYS.getValue());
		hotelTagMap.put("PC_HOTEL_TOP", hotelTag);
		return hotelTagMap;
	}

	public Map<String, Map<String, MobgenJsonBO>> buildMobgenJsonBO() {

		try {

			mobgenJsonBOMap = objectMapperUtil.getObjectFromJsonWithType(mobgenJsonBO,
					new TypeReference<Map<String, Map<String, MobgenJsonBO>>>() {
					}, DependencyLayer.CLIENTGATEWAY);

			polyglotHelper.translateMobgenJsonBO(mobgenJsonBOMap);

		} catch (Exception e) {
			logger.warn("Error in buildMobgenJsonBO");
		}

		return mobgenJsonBOMap;
	}

	public Map<String, MobgenStringsBO> buildMobgenStringsBO() {

		try {

			mobgenStringsBOMap = objectMapperUtil.getObjectFromJsonWithType(mobgenStringsBO,
					new TypeReference<Map<String, MobgenStringsBO>>() {
					}, DependencyLayer.CLIENTGATEWAY);
			polyglotHelper.translateMobgenStringsBO(mobgenStringsBOMap);

		} catch (Exception e) {
			logger.warn("Error in buildMobgenStringBO");
		}
		return mobgenStringsBOMap;
	}

	public LinkedHashMap<String, Map<String, HotelCategoryData>> buildHotelCategoryDataMap(List<String> categories) {
		if (CollectionUtils.isEmpty(categories)) {
			return null;
		}
		LinkedHashMap<String, Map<String, HotelCategoryData>> applicableHotelCategoryData = new LinkedHashMap<>();

		try {

			String jsonAsStringMapping = gson.toJson(hotelCategoryDataMap);
			if (StringUtils.isNotBlank(jsonAsStringMapping)) {
				hotelCategoryDataMapModified = objectMapperUtil.getObjectFromJsonWithType(jsonAsStringMapping,
						new TypeReference<Map<String, Map<String, HotelCategoryData>>>() {
						}, DependencyLayer.CLIENTGATEWAY);
			}

		} catch (Exception e) {
			logger.warn("Error in building hotelCategoryDataMapModified");
		}


		if (MapUtils.isNotEmpty(hotelCategoryDataMapModified)) {
			for (String category : categories) {
				if (!CORP_ID_CONTEXT.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.IDCONTEXT.getStringValue())) && MyBiz_Assured.equalsIgnoreCase(category)) {
					continue;
				}
				if (hotelCategoryDataMapModified.containsKey(category)) {
					polyglotHelper.translateHotelCategoryDataMap(hotelCategoryDataMapModified.get(category));
					applicableHotelCategoryData.put(category, hotelCategoryDataMapModified.get(category));

				}
			}
		}
		return applicableHotelCategoryData;
	}

	public LinkedHashMap<String, HotelCategoryDataWeb> buildHotelCategoryDataWeb(List<String> categories) {
		if (CollectionUtils.isEmpty(categories)) {
			return null;
		}
		LinkedHashMap<String, HotelCategoryDataWeb> applicableHotelCategoryDataWeb = new LinkedHashMap();

		try {

			String jsonAsStringMapping = gson.toJson(hotelCategoryDataWebMapNew);
			if (StringUtils.isNotBlank(jsonAsStringMapping)) {
				hotelCategoryDataWebMapModified = objectMapperUtil.getObjectFromJsonWithType(jsonAsStringMapping,
						new TypeReference<Map<String, HotelCategoryDataWeb>>() {
						}, DependencyLayer.CLIENTGATEWAY);
			}

		} catch (Exception e) {
			logger.warn("Error in building hotelCategoryDataMapModified");
		}

		if (MapUtils.isNotEmpty(hotelCategoryDataWebMapModified)) {
			for (String category : categories) {
				if (!CORP_ID_CONTEXT.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.IDCONTEXT.getStringValue())) && MyBiz_Assured.equalsIgnoreCase(category)) {
					continue;
				}
				if (hotelCategoryDataWebMapModified.containsKey(category)) {
					polyglotHelper.translateHotelCategoryDataWebMapNew(hotelCategoryDataWebMapModified.get(category));
					applicableHotelCategoryDataWeb.put(category, hotelCategoryDataWebMapModified.get(category));

				}
			}
		}
		return applicableHotelCategoryDataWeb;
	}

	public boolean enableSaveValue(String expData) {
		/*//GIHTL-15565 Clean Up AB_EXPT_DISABLE_SAVE_VALUE
		 * return true if experiment disableSaveValue is not 1
		 * */
		return false;
//		if (StringUtils.isNotBlank(expData)) {
//			Type type = new TypeToken<Map<String, String>>() {
//			}.getType();
//			try {
//				expData = expData.replaceAll("^\"|\"$", "");
//				Map<String, String> expDataMap = gson.fromJson(expData, type);
//				if(expDataMap.containsKey(HOMESTAY_PERSUASION_ALC)
//						&& expDataMap.get(HOMESTAY_PERSUASION_ALC).equalsIgnoreCase("T"))
//					return false;
//
//				return MapUtils.isNotEmpty(expDataMap) &&
//						expDataMap.containsKey(AB_EXPT_DISABLE_SAVE_VALUE) && Integer.parseInt(expDataMap.get(AB_EXPT_DISABLE_SAVE_VALUE)) != 1;
//			} catch (Exception e) {
//				return false;
//			}
//		}
//		return false;
	}


	public boolean getHCARDV2(String expData) {
		if (StringUtils.isNotBlank(expData)) {
			Type type = new TypeToken<Map<String, String>>() {
			}.getType();
			try {
				expData = expData.replaceAll("^\"|\"$", "");
				Map<String, String> expDataMap = gson.fromJson(expData, type);
				return (expDataMap.containsKey(HCARDV2.name())
						&& expDataMap.get(HCARDV2.name()).equalsIgnoreCase("True"));
			} catch (Exception e) {
				return false;
			}
		}
		return false;
	}

	public boolean enableAmenitiesPersuasion(String expData, String funnelSource,boolean isMyPartnerRequest) {
		/*
		 * returns true if amenities persuasion is to be shown
		 * on the basis of experiment disableAmenities
		 * 0 - show amenities, 1 - don't show amenities, 2 - contextual amenities
		 * */
return true;
// GIHTl-15565 Clean Up AB_EXPT_DISABLE_AMENITIES
//		if (StringUtils.isNotBlank(expData)) {
//			Type type = new TypeToken<Map<String, String>>() {
//			}.getType();
//			try {
//				expData = expData.replaceAll("^\"|\"$", "");
//				Map<String, String> expDataMap = gson.fromJson(expData, type);
//				if (FUNNEL_SOURCE_HOMESTAY.equalsIgnoreCase(funnelSource)
//					&& expDataMap.containsKey(HOMESTAY_PERSUASION_ALC)
//					&& expDataMap.get(HOMESTAY_PERSUASION_ALC).equalsIgnoreCase("T")){
//					return false;
//				}else {
////					return isMyPartnerRequest || (MapUtils.isNotEmpty(expDataMap) &&
////							expDataMap.containsKey(AB_EXPT_DISABLE_AMENITIES) && Integer.parseInt(expDataMap.get(AB_EXPT_DISABLE_AMENITIES)) != 1);
//				return true;
//				}
//			} catch (Exception e) {
//				return false;
//			}
//		}
//		return false;
	}

	/*
	 * myPartner change log :
	 *  This is the discount check on the combined mmtDiscount
	 *  The conditions like  - value > 100 and > 5% of the base Price is dynamic and will be controlled from fpm
	 * */
	public boolean enableDiscount(DisplayPriceBreakDown displayPriceBreakDown) {

		return Objects.nonNull(discountParameters) && Objects.nonNull(displayPriceBreakDown) &&
				(
						displayPriceBreakDown.getTotalSaving() >= discountParameters.get(Constants.DISCOUNT_THRESHOLD) &&
								displayPriceBreakDown.getTotalSaving() >= (discountParameters.get(Constants.DISCOUNT_THRESHOLD_PERCENT) * displayPriceBreakDown.getDisplayPrice())
				);
	}

	public List<ReasonForBooking> buildReasonForBooking(List<com.mmt.hotels.model.response.corporate.ReasonForBooking> reasonForBooking) {
		if(CollectionUtils.isNotEmpty(reasonForBooking)) {
			List<com.mmt.hotels.clientgateway.response.corporate.ReasonForBooking> reasonForBookings = new ArrayList<>();
			for(com.mmt.hotels.model.response.corporate.ReasonForBooking reason : reasonForBooking) {
				com.mmt.hotels.clientgateway.response.corporate.ReasonForBooking node = new com.mmt.hotels.clientgateway.response.corporate.ReasonForBooking();
				node.setInputType(reason.getInputType());
				node.setText(reason.getText());
				reasonForBookings.add(node);
			}
			return reasonForBookings;
		}
		return null;
	}


	public boolean enableDiscount(com.gommt.hotels.orchestrator.model.response.listing.PriceDetail priceDetail) {
		if (priceDetail == null) {
			return false;
		}
		double saving = priceDetail.getHotelDiscount() + priceDetail.getCouponDiscount();
		return Objects.nonNull(discountParameters) && (saving >= discountParameters.get(Constants.DISCOUNT_THRESHOLD) && saving >= (discountParameters.get(Constants.DISCOUNT_THRESHOLD_PERCENT) * priceDetail.getDisplayPrice()));
	}

	public List<com.mmt.hotels.clientgateway.response.AttributesFacility> buildChildAttributesCgFromHes(List<com.mmt.model.AttributesFacility> childAttributesHes) {

	List<com.mmt.hotels.clientgateway.response.AttributesFacility> childAttributesCG = new ArrayList<>();
	for(com.mmt.model.AttributesFacility childAttributeHes : childAttributesHes) {
		com.mmt.hotels.clientgateway.response.AttributesFacility childAttributeCG = new com.mmt.hotels.clientgateway.response.AttributesFacility();
		BeanUtils.copyProperties(childAttributeHes, childAttributeCG);
		childAttributesCG.add(childAttributeCG);
	}
	return  childAttributesCG;
	}

	public List<ReasonForSkipApproval> buildSkipApprovalReasons(List<com.mmt.hotels.model.response.corporate.ReasonForSkipApproval> skipApprovalReasonsHES) {
		if(CollectionUtils.isNotEmpty(skipApprovalReasonsHES)) {
			List<com.mmt.hotels.clientgateway.response.corporate.ReasonForSkipApproval> skipApprovalReasons = new ArrayList<>();
			for(com.mmt.hotels.model.response.corporate.ReasonForSkipApproval reason : skipApprovalReasonsHES) {
				com.mmt.hotels.clientgateway.response.corporate.ReasonForSkipApproval node = new com.mmt.hotels.clientgateway.response.corporate.ReasonForSkipApproval();
				node.setInputType(reason.getInputType());
				node.setText(reason.getText());
				node.setEnablePersonalCorpBooking(reason.getEnablePersonalCorpBooking());
				skipApprovalReasons.add(node);
			}
			return skipApprovalReasons;
		}
		return null;
	}

	public CorpAutobookRequestorConfigBO buildCorpAutobookRequestorConfig(AutobookRequestorConfigBO autobookRequestorConfigHES) {
		if(autobookRequestorConfigHES != null) {
			CorpAutobookRequestorConfigBO corpAutobookRequestorConfig = new CorpAutobookRequestorConfigBO();
			corpAutobookRequestorConfig.setTitle(autobookRequestorConfigHES.getTitle());
			corpAutobookRequestorConfig.setSubTitle(autobookRequestorConfigHES.getSubTitle());
			return corpAutobookRequestorConfig;
		}
		return null;
	}

	public GeoLocation buildGeoLocation(HotelResult hotelResult) {
		if (hotelResult == null || hotelResult.getLatitude() == null || hotelResult.getLongitude() == null)
			return null;
		GeoLocation geoLocationCG = new GeoLocation();
		geoLocationCG.setLatitude(hotelResult.getLatitude());
		geoLocationCG.setLongitude(hotelResult.getLongitude());
		return geoLocationCG;
	}


	public com.mmt.hotels.clientgateway.response.rooms.CancellationPolicyTimeline buildCancellationPolicyTimeline(com.mmt.hotels.model.response.pricing.CancellationTimeline cancellationTimeline) {
		if (cancellationTimeline != null && CollectionUtils.isNotEmpty(cancellationTimeline.getCancellationPolicyTimelineList())) {
			com.mmt.hotels.clientgateway.response.rooms.CancellationPolicyTimeline cancellationPolicyTimeline = new com.mmt.hotels.clientgateway.response.rooms.CancellationPolicyTimeline();
			cancellationPolicyTimeline.setTimeline(buildCancellationTimelineList(cancellationTimeline.getCancellationPolicyTimelineList()));
			cancellationPolicyTimeline.setCancellationDate(cancellationTimeline.getCancellationDate());
			cancellationPolicyTimeline.setCancellationDateTime(cancellationTimeline.getCancellationDateTime());
			cancellationPolicyTimeline.setCardChargeDate(cancellationTimeline.getCardChargeDate());
			cancellationPolicyTimeline.setCardChargeDateTime(cancellationTimeline.getCardChargeDateTime());
			cancellationPolicyTimeline.setDateFormat(cancellationTimeline.getDateFormat());
			cancellationPolicyTimeline.setCardChargeText(cancellationTimeline.getCardChargeText());
			cancellationPolicyTimeline.setCardChargeTextTitle(cancellationTimeline.getCardChargeTextTitle());
			cancellationPolicyTimeline.setCardChargeTextMsg(cancellationTimeline.getCardChargeTextMsg());
			cancellationPolicyTimeline.setBnplTitleText(cancellationTimeline.getBnplTitleText());
			cancellationPolicyTimeline.setBookingAmountText(cancellationTimeline.getBookingAmountText());
			cancellationPolicyTimeline.setFreeCancellationBenefits(buildFreeCancellationBenefits(cancellationTimeline.getFreeCancellationBenefits()));
			cancellationPolicyTimeline.setTitle(polyglotService.getTranslatedData("RATEPLAN_CANCELLATION_POLICY"));
			return cancellationPolicyTimeline;
		}
		return null;
	}

	private List<com.mmt.hotels.clientgateway.response.rooms.CancellationTimeline> buildCancellationTimelineList(List<CancellationPolicyTimeline> cancellationPolicyTimelineList) {
		if (CollectionUtils.isNotEmpty(cancellationPolicyTimelineList)) {
			List<com.mmt.hotels.clientgateway.response.rooms.CancellationTimeline> cancellationTimelineList = new ArrayList<>();
			cancellationPolicyTimelineList.forEach(cancellationPolicyTimelineHES -> cancellationTimelineList.add(buildTimeline(cancellationPolicyTimelineHES)));
			return cancellationTimelineList;
		}
		return null;
	}

	private com.mmt.hotels.clientgateway.response.rooms.CancellationTimeline buildTimeline(CancellationPolicyTimeline cancellationPolicyTimelineHES) {
		if (cancellationPolicyTimelineHES != null) {
			com.mmt.hotels.clientgateway.response.rooms.CancellationTimeline cancellationPolicyTimeline = new  com.mmt.hotels.clientgateway.response.rooms.CancellationTimeline();
			cancellationPolicyTimeline.setRefundable(cancellationPolicyTimelineHES.isRefundable());
			cancellationPolicyTimeline.setText(cancellationPolicyTimelineHES.getText());
			cancellationPolicyTimeline.setStartDate(cancellationPolicyTimelineHES.getStartDate());
			cancellationPolicyTimeline.setEndDate(cancellationPolicyTimelineHES.getEndDate());
			cancellationPolicyTimeline.setEndDateTime(cancellationPolicyTimelineHES.getEndDateTime());
			return cancellationPolicyTimeline;
		}
		return null;
	}

	public com.mmt.hotels.clientgateway.response.rooms.PaymentPlan buildPaymentPlan(com.mmt.hotels.model.response.pricing.PaymentPlan paymentPlan) {
		if(paymentPlan != null && paymentPlan.getAmount() != 0.0) {
			com.mmt.hotels.clientgateway.response.rooms.PaymentPlan paymentPlanCG = new com.mmt.hotels.clientgateway.response.rooms.PaymentPlan();
			paymentPlanCG.setText(paymentPlan.getText());
			paymentPlanCG.setAmount(paymentPlan.getAmount());
			if(CollectionUtils.isNotEmpty(paymentPlan.getPaymentPolicy())) {
				AtomicInteger index = new AtomicInteger(1);
				paymentPlanCG.setPaymentPolicy(new ArrayList<>());
				paymentPlan.getPaymentPolicy().forEach(policy-> {
					com.mmt.hotels.clientgateway.response.rooms.PaymentPlan policyCG = new com.mmt.hotels.clientgateway.response.rooms.PaymentPlan();
					policyCG.setSequence(index.getAndIncrement());
					policyCG.setText(policy.getText());
					policyCG.setAmount(policy.getAmount());
					policyCG.setPaymentDate(buildPaymentDate(policy.getPaymentDateText())); // HTL-37096
					paymentPlanCG.getPaymentPolicy().add(policyCG);
				});
				paymentPlanCG.setPenaltyText(polyglotService.getTranslatedData(PAYMENT_PLAN_PENALTY_TEXT));
			}
			return paymentPlanCG;
		}
		return null;
	}

	private String buildPaymentDate(String paymentDateHES){
		if(paymentDateHES != null && paymentDateHES.split(COMMA).length > 1){
			return paymentDateHES.split(COMMA)[0];
		}
		return null;
	}

	public MyBizQuickPayConfigBO buildMyBizQuickPayConfig(DisplayPriceBreakDown displayPriceBrkDwn) {
		MyBizQuickPayConfigBO myBizQuickPayConfigBO = new MyBizQuickPayConfigBO();
		myBizQuickPayConfigBO.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.MYBIZ_QUICKPAY_TITLE));
		myBizQuickPayConfigBO.setSubTitle(polyglotService.getTranslatedData(ConstantsTranslation.MYBIZ_QUICKPAY_SUBTITLE));
		myBizQuickPayConfigBO.setText(polyglotService.getTranslatedData(ConstantsTranslation.MYBIZ_QUICKPAY_TEXT));
		myBizQuickPayConfigBO.setCtaSubText(Constants.DEVICE_OS_DESKTOP.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue())) ?
				polyglotService.getTranslatedData(ConstantsTranslation.MYBIZ_QUICKPAY_CTA_DESKTOP) : polyglotService.getTranslatedData(ConstantsTranslation.MYBIZ_QUICKPAY_CTA_DESKTOP));

		myBizQuickPayConfigBO.setText(populateMyBizQuickPayTextWithPricingInfo(myBizQuickPayConfigBO.getText(), displayPriceBrkDwn));
		return myBizQuickPayConfigBO;

	}

	private String populateMyBizQuickPayTextWithPricingInfo(String text, DisplayPriceBreakDown displayPriceBrkDwn) {
		if (displayPriceBrkDwn != null) {
			double totalDiscountAmount = displayPriceBrkDwn.getMmtDiscount() + displayPriceBrkDwn.getBlackDiscount() + displayPriceBrkDwn.getCdfDiscount() + displayPriceBrkDwn.getWallet();
			double priceAfterDiscountAmount = displayPriceBrkDwn.getBasePrice();
			if (totalDiscountAmount > 0.0d) {
				priceAfterDiscountAmount = priceAfterDiscountAmount - totalDiscountAmount;
			}
			double totalPrice = displayPriceBrkDwn.getDisplayPrice();
			double hotelTaxes = totalPrice - priceAfterDiscountAmount;
			text = StringUtils.replace(text, "{TOTAL_AMOUNT}", "" + totalPrice);
			text = StringUtils.replace(text, "{PRICE_AFTER_DISCOUNT}", "" + priceAfterDiscountAmount);
			text = StringUtils.replace(text, "{HOTEL_TAXES}", "" + hotelTaxes);

			return text;
		}
		return null;

	}
	public int getManthanGocashValue(RoomTypeDetails roomDetails){
		if (roomDetails!= null && roomDetails.getTotalDisplayFare() != null && roomDetails.getTotalDisplayFare().getDisplayPriceBreakDown() != null &&
				roomDetails.getTotalDisplayFare().getDisplayPriceBreakDown().getCouponInfo() != null
				&& roomDetails.getTotalDisplayFare().getDisplayPriceBreakDown().getCouponInfo().getHybridDiscounts() != null
				&& roomDetails.getTotalDisplayFare().getDisplayPriceBreakDown().getCouponInfo().getHybridDiscounts().containsKey(Constants.GOCASH_KEY) &&
				roomDetails.getTotalDisplayFare().getDisplayPriceBreakDown().getCouponInfo().getHybridDiscounts().get(Constants.GOCASH_KEY) > 0.0d){
			return roomDetails.getTotalDisplayFare().getDisplayPriceBreakDown().getCouponInfo().getHybridDiscounts().get(Constants.GOCASH_KEY).intValue();
		}
		return 0;
	}

	public int getLoyaltyInstantDiscount(RoomTypeDetails roomDetails){
		if (roomDetails!= null && roomDetails.getTotalDisplayFare() != null && roomDetails.getTotalDisplayFare().getDisplayPriceBreakDown() != null &&
				roomDetails.getTotalDisplayFare().getDisplayPriceBreakDown().getBlackDiscount() > 0.0d){
			return (int)roomDetails.getTotalDisplayFare().getDisplayPriceBreakDown().getBlackDiscount();
		}
		return 0;
	}

	public BookingMessageCard getBookingMessageCardById(LoyaltyMessageResponse loyaltyMessageResponse, String cardId) {
		if (loyaltyMessageResponse != null && loyaltyMessageResponse.getCards() != null) {
			for (BookingMessageCard card : loyaltyMessageResponse.getCards()) {
				if (card.getCardId().equals(cardId)) {
					return card;
				}
			}
		}
		return null;
	}

	public LoyaltyMessageResponse buildFooterMessageFromNewLoyaltyResponse(LoyaltyMessageResponse loyaltyMessage, Map<String, String> expData, com.mmt.hotels.model.response.mmtprime.BlackInfo binfo, LoyaltyMessageResponse footerMessage) {
		boolean isGoTribe3ExpEnabled = expData != null && expData.containsKey("goTribe3") && "true".equalsIgnoreCase(expData.get("goTribe3"));
			// Irrespective of old and new app. we need to pick messaging data from loyalty.
			if(loyaltyMessage != null && loyaltyMessage.getCards() != null){
				BookingMessageCard card = getBookingMessageCardById(loyaltyMessage, REVIEW_GO_CASH_GOTRIBE1);
				if(card != null && card.getData() != null ) { //StringUtils.isNotEmpty(binfo.getTierName())
					footerMessage.setTierName(binfo.getTierName());
					footerMessage.setMessageTitle(card.getData().getHeader());
					footerMessage.setIconType(Constants.GOTRIBE_KEY);

					if(!isGoTribe3ExpEnabled)  { // old app so setting old data.
						footerMessage.setIconUrl(binfo.getOldFooterStripIconUrl());
						footerMessage.setIconColor(FOOTER_GOTRIBE_BG_COLOR);
						footerMessage.setIconType(GOTRIBE_KEY);
						String message = card.getData().getMessage();
						message = message.replace("<b>", "");
						message = message.replace("</b>", "");
						footerMessage.setMessageText(message);
					} else {
						footerMessage.setMessageText(card.getData().getMessage());
						footerMessage.setIconUrlV2(card.getData().getIconUrl());
						footerMessage.setLineBgColor(card.getData().getStyle().getLineBgColour());
						if (card.getData().getStyle().getBgColorGradient() != null && card.getData().getStyle().getBgColorGradient().size() > 0)
							footerMessage.setBackgroundColor(card.getData().getStyle().getBgColorGradient().get(0));

					}
					return footerMessage;
				}
			}
		return null;
	}

	public LoyaltyMessageResponse buildFooterStrip(RoomTypeDetails roomDetails, LoyaltyMessageResponse loyaltyMessage,
												   ClmPersuasion clmData, List<String> hydraSegments, Map<String, String> expData, com.mmt.hotels.model.response.mmtprime.BlackInfo binfo, boolean isDomestic) {
		LoyaltyMessageResponse footerMessage = new LoyaltyMessageResponse();

		if(binfo != null && binfo.getIsNewLoyaltyProgramForGI()) // New loyalty program.
			return buildFooterMessageFromNewLoyaltyResponse(loyaltyMessage, expData, binfo, footerMessage);

		boolean isGoTribe3ExpEnabled = expData != null && expData.containsKey("goTribe3") && "true".equalsIgnoreCase(expData.get("goTribe3"));
		if (!Utility.gocashVariant(expData, isDomestic)) {
			if(isGoTribe3ExpEnabled && loyaltyMessage != null) { // For new apps.
				loyaltyMessage.setLineBgColor(binfo != null ? binfo.getLineBgColor() : null);
				loyaltyMessage.setIconUrlV2(binfo != null ? binfo.getFooterStripIconUrl() : "");
				loyaltyMessage.setBackgroundColor(binfo != null ? binfo.getReviewPageFooterBgColor() : "");
			}
			return loyaltyMessage;
		}

		if (loyaltyMessage != null && (loyaltyMessage.getWalletEarn() != 0.0d || StringUtils.isNotBlank(loyaltyMessage.getMessageText()))){
			footerMessage.setTierName(loyaltyMessage.getTierName());
			footerMessage.setMessageTitle(loyaltyMessage.getMessageTitle());
			footerMessage.setMessageText(loyaltyMessage.getMessageText());
			if(isGoTribe3ExpEnabled) { // For new apps.
				footerMessage.setLineBgColor(binfo != null ? binfo.getLineBgColor() : null);
				footerMessage.setIconUrlV2(binfo != null ? binfo.getFooterStripIconUrl() : "");
				footerMessage.setBackgroundColor(binfo != null ? binfo.getReviewPageFooterBgColor() : "");
			} else {
				footerMessage.setIconUrl(loyaltyMessage.getIconUrl());
				footerMessage.setIconColor(loyaltyMessage.getIconColor());
			}
		}
		if (loyaltyMessage != null && (loyaltyMessage.getWalletEarn() != 0.0d || StringUtils.isNotBlank(loyaltyMessage.getMessageText())) && getManthanGocashValue(roomDetails) > 0){
			footerMessage.setIconType(loyaltyMessage.getIconType());
			footerMessage.setMessageText(String.format(GOTRIBE_USER_GOCASH_TEXT, (int)loyaltyMessage.getWalletEarn() + getManthanGocashValue(roomDetails)));
			if (getLoyaltyInstantDiscount(roomDetails) > 0) {
				footerMessage.setMessageText(String.format(GOTRIBE_USER_GOCASH_INSTANT_DISC_TEXT, getLoyaltyInstantDiscount(roomDetails), (int)loyaltyMessage.getWalletEarn() + getManthanGocashValue(roomDetails)));
			}
			footerMessage.setIconType(Constants.GOTRIBE_KEY);
		} else if (loyaltyMessage != null && loyaltyMessage.getWalletEarn() != 0.0d){
			footerMessage.setIconType(Constants.GOTRIBE_KEY);
		} else if (getManthanGocashValue(roomDetails) > 0 || (clmData != null && Integer.parseInt(clmData.getBenefitAmount()) > 0)) { // clm is going to be deprecated after new loyalty program is live.
			int clmAmount = 0;
			if (clmData != null && Integer.parseInt(clmData.getBenefitAmount()) > 0) {
				clmAmount = Integer.parseInt(clmData.getBenefitAmount());
			}
			footerMessage.setIconType(Constants.COIN_KEY);
			footerMessage.setBackgroundColor(Constants.FOOTER_BG_COLOR);
			footerMessage.setIconUrl(Constants.COIN_URL);
			footerMessage.setMessageText(String.format(Constants.FOOTER_GOCASH_TEXT, (getManthanGocashValue(roomDetails) + clmAmount)));
			if (clmAmount > 0 && hydraSegments != null) {
				logger.debug("Hydra Segments {}", hydraSegments);
				if ((hydraSegments.contains("g1001") || hydraSegments.contains("g1021") && getManthanGocashValue(roomDetails) > 0) || (!isDomestic && hydraSegments.contains("g1003") && getManthanGocashValue(roomDetails) > 0)) {
					if(isDomestic)
						footerMessage.setMessageText(String.format(FOOTER_GOCASH_NEWUSER_DH_TEXT, clmAmount+getManthanGocashValue(roomDetails)));
					else
						footerMessage.setMessageText(String.format(FOOTER_GOCASH_NEWUSER_IH_TEXT, clmAmount+getManthanGocashValue(roomDetails)));
					return footerMessage;
				}else if ((hydraSegments.contains("g1001") || hydraSegments.contains("g1021") && getManthanGocashValue(roomDetails) <= 0) || (!isDomestic && hydraSegments.contains("g1003") && getManthanGocashValue(roomDetails) > 0)){
					footerMessage.setMessageText(String.format(FOOTER_GOCASH_NEWUSER, clmAmount));
					return footerMessage;
				}
			}
		}
		return footerMessage;
	}
	public static int getManthanGocashValueValidateCoupon(DisplayPriceBreakDown displayPriceBreakDown){
		if (displayPriceBreakDown!= null && displayPriceBreakDown.getCouponInfo() != null
				&& displayPriceBreakDown.getCouponInfo().getHybridDiscounts() != null
				&& displayPriceBreakDown.getCouponInfo().getHybridDiscounts().containsKey(Constants.GOCASH_KEY) &&
				displayPriceBreakDown.getCouponInfo().getHybridDiscounts().get(Constants.GOCASH_KEY) > 0.0d){
			return displayPriceBreakDown.getCouponInfo().getHybridDiscounts().get(Constants.GOCASH_KEY).intValue();
		}
		return 0;
	}

	public static int getLoyaltyInstantDiscountValidateCoupon(DisplayPriceBreakDown displayPriceBreakDown){
		if (displayPriceBreakDown!= null && displayPriceBreakDown.getBlackDiscount() > 0.0d){
			return (int)displayPriceBreakDown.getBlackDiscount();
		}
		return 0;
	}

	public LoyaltyMessageResponse buildFooterStripValidateCoupon(DisplayPriceBreakDown displayPriceBreakDown, LoyaltyMessageResponse loyaltyMessage, ClmPersuasion clmData, List<String> hydraSegments, Map<String,String> expData, com.mmt.hotels.model.response.mmtprime.BlackInfo binfo, String countryCode) {
		LoyaltyMessageResponse footerMessage = new LoyaltyMessageResponse();
		boolean isGoTribe3ExpEnabled = expData.containsKey("goTribe3") && expData.get("goTribe3") != null && "true".equalsIgnoreCase(expData.get("goTribe3"));
		if(binfo != null && binfo.getIsNewLoyaltyProgramForGI()) // New gotribe program.
			return buildFooterMessageFromNewLoyaltyResponse(loyaltyMessage, expData, binfo, footerMessage);

		if(!Utility.gocashVariant(expData, StringUtils.equalsIgnoreCase(countryCode, DOM_COUNTRY))) {
			if(isGoTribe3ExpEnabled && loyaltyMessage != null) { // For new apps.
				loyaltyMessage.setLineBgColor(binfo != null ? binfo.getLineBgColor() : null);
				loyaltyMessage.setIconUrlV2(binfo != null ? binfo.getFooterStripIconUrl() : "");
				loyaltyMessage.setBackgroundColor(binfo != null ? binfo.getReviewPageFooterBgColor() : "");
			}
			return loyaltyMessage;
		}
		if (loyaltyMessage != null && (loyaltyMessage.getWalletEarn() != 0.0d || StringUtils.isNotBlank(loyaltyMessage.getMessageText()))){
			footerMessage.setTierName(loyaltyMessage.getTierName());
			footerMessage.setMessageTitle(loyaltyMessage.getMessageTitle());
			footerMessage.setMessageText(loyaltyMessage.getMessageText());
			footerMessage.setIconUrl(loyaltyMessage.getIconUrl());
			if(isGoTribe3ExpEnabled) { // For new apps.
				footerMessage.setLineBgColor(binfo != null ? binfo.getLineBgColor(): null);
				footerMessage.setIconUrlV2(binfo != null ? binfo.getFooterStripIconUrl() : "");
				footerMessage.setBackgroundColor(binfo != null ? binfo.getReviewPageFooterBgColor() : "");
				//footerMessage.setIconColor(FOOTER_GOTRIBE_BG_COLOR);
			} else {
				footerMessage.setIconUrl(loyaltyMessage.getIconUrl());
				footerMessage.setIconColor(loyaltyMessage.getIconColor());
			}
		}
		if (loyaltyMessage != null && (loyaltyMessage.getWalletEarn() != 0.0d  || StringUtils.isNotBlank(loyaltyMessage.getMessageText()) ) && getManthanGocashValueValidateCoupon(displayPriceBreakDown) > 0){
			footerMessage.setMessageText(String.format(GOTRIBE_USER_GOCASH_TEXT, (int)loyaltyMessage.getWalletEarn() + getManthanGocashValueValidateCoupon(displayPriceBreakDown)));
			if (getLoyaltyInstantDiscountValidateCoupon(displayPriceBreakDown) > 0) {
				footerMessage.setMessageText(String.format(GOTRIBE_USER_GOCASH_INSTANT_DISC_TEXT, getLoyaltyInstantDiscountValidateCoupon(displayPriceBreakDown), (int)loyaltyMessage.getWalletEarn() + getManthanGocashValueValidateCoupon(displayPriceBreakDown)));
			}
			footerMessage.setIconType(Constants.GOTRIBE_KEY);
		} else if (loyaltyMessage != null && loyaltyMessage.getWalletEarn() != 0.0d){
			footerMessage.setIconType(Constants.GOTRIBE_KEY);
		} else
		if (getManthanGocashValueValidateCoupon(displayPriceBreakDown) > 0 || (clmData != null && Integer.parseInt(clmData.getBenefitAmount()) > 0)) {
			int clmAmount = 0;
			if (clmData != null && Integer.parseInt(clmData.getBenefitAmount()) > 0) {
				clmAmount = Integer.parseInt(clmData.getBenefitAmount());
			}
			footerMessage.setIconType(Constants.COIN_KEY);
			footerMessage.setBackgroundColor(Constants.FOOTER_BG_COLOR);
			footerMessage.setIconUrl(Constants.COIN_URL);
			footerMessage.setMessageText(String.format(Constants.FOOTER_GOCASH_TEXT, (getManthanGocashValueValidateCoupon(displayPriceBreakDown) + clmAmount)));
			if (clmAmount > 0 && hydraSegments != null) {
				logger.debug("Hydra Segments {}", hydraSegments);
				if (((hydraSegments.contains("g1001") || hydraSegments.contains("g1021")) && getManthanGocashValueValidateCoupon(displayPriceBreakDown) > 0) || (hydraSegments.contains("g1003") && getManthanGocashValueValidateCoupon(displayPriceBreakDown) > 0)) {
					if(StringUtils.equalsIgnoreCase(countryCode, DOM_COUNTRY))
						footerMessage.setMessageText(String.format(FOOTER_GOCASH_NEWUSER_DH_TEXT, clmAmount+getManthanGocashValueValidateCoupon(displayPriceBreakDown)));
					else
						footerMessage.setMessageText(String.format(FOOTER_GOCASH_NEWUSER_IH_TEXT, clmAmount+getManthanGocashValueValidateCoupon(displayPriceBreakDown)));
					return footerMessage;
				}else if (((hydraSegments.contains("g1001") || hydraSegments.contains("g1021")) && getManthanGocashValueValidateCoupon(displayPriceBreakDown) <= 0) || (hydraSegments.contains("g1003") && getManthanGocashValueValidateCoupon(displayPriceBreakDown) <=0)){
					footerMessage.setMessageText(String.format(FOOTER_GOCASH_NEWUSER, clmAmount));
					return footerMessage;
				}
			}
		}
		return footerMessage;
	}

	public BNPLDisabledReason getBNPLDisabledReason(boolean userLevelBnplDisabled, boolean bnplDisabledDueToNonBnplCouponApplied, boolean insuranceAddonSelected, boolean goCashApplied) {

		if (userLevelBnplDisabled) {
			return BNPLDisabledReason.ACTIVE_BOOKINGS_THRESHOLD;
		} else if (bnplDisabledDueToNonBnplCouponApplied && goCashApplied && insuranceAddonSelected) {
			return BNPLDisabledReason.NON_BNPL_COUPON_GOCASH_INSURANCE_APPLIED;
		} else if (bnplDisabledDueToNonBnplCouponApplied && goCashApplied) {
			return BNPLDisabledReason.NON_BNPL_COUPON_GOCASH_APPLIED;
		} else if (bnplDisabledDueToNonBnplCouponApplied && insuranceAddonSelected) {
			return BNPLDisabledReason.NON_BNPL_COUPON_INSURANCE_APPLIED;
		} else if (goCashApplied && insuranceAddonSelected) {
			return BNPLDisabledReason.GOCASH_INSURANCE_APPLIED;
		} else if (bnplDisabledDueToNonBnplCouponApplied) {
			return BNPLDisabledReason.NON_BNPL_COUPON_APPLIED;
		} else if (insuranceAddonSelected) {
			return BNPLDisabledReason.INSURANCE_APPLIED;
		} else if (goCashApplied) {
			return BNPLDisabledReason.GOCASH_APPLIED;
		}
		return null;
	}

	public String getBnplDisabledMessage(BNPLDisabledReason reasonForDisabling) {
		if (reasonForDisabling == null) return null;
		String unAvailMessage = null;
		switch (reasonForDisabling) {
			case ACTIVE_BOOKINGS_THRESHOLD:
				unAvailMessage = polyglotService.getTranslatedData(GI_ACTIVE_BOOKINGS_THRESHOLD_TEXT);
				break;
			case NON_BNPL_COUPON_APPLIED:
				unAvailMessage = polyglotService.getTranslatedData(GI_NON_BNPL_COUPON_APPLIED_TEXT);
				break;
			case INSURANCE_APPLIED:
				unAvailMessage = polyglotService.getTranslatedData(GI_INSURANCE_APPLIED_TEXT);
				break;
			case GOCASH_APPLIED:
				unAvailMessage = polyglotService.getTranslatedData(GI_GOCASH_APPLIED_TEXT);
				break;
			case NON_BNPL_COUPON_GOCASH_APPLIED:
				unAvailMessage = polyglotService.getTranslatedData(GI_NON_BNPL_COUPON_GOCASH_APPLIED_TEXT);
				break;
			case NON_BNPL_COUPON_INSURANCE_APPLIED:
				unAvailMessage = polyglotService.getTranslatedData(GI_NON_BNPL_COUPON_INSURANCE_APPLIED_TEXT);
				break;
			case GOCASH_INSURANCE_APPLIED:
				unAvailMessage = polyglotService.getTranslatedData(GI_GOCASH_INSURANCE_APPLIED_TEXT);
				break;
			case NON_BNPL_COUPON_GOCASH_INSURANCE_APPLIED:
				unAvailMessage = polyglotService.getTranslatedData(GI_NON_BNPL_COUPON_GOCASH_INSURANCE_APPLIED_TEXT);
				break;

		}
		return unAvailMessage;
	}

	public boolean checkIfInvalidCoupon(TotalPricing totalPricing) {
		boolean bnplDisabledDueToNonBnplCouponApplied = false;
		if (totalPricing != null && totalPricing.getCoupons() != null) {
			for (Coupon coupon : totalPricing.getCoupons()) {
				if (coupon.isAutoApplicable() && !coupon.isBnplAllowed()) {
					bnplDisabledDueToNonBnplCouponApplied = true;
					break;
				}
			}
		}

		return bnplDisabledDueToNonBnplCouponApplied;
	}

	public UserCard GetUserCardById(List<UserCard> cards, String templatedId, int tierNumber) {
		if (cards != null) {
			for (UserCard card : cards) {
				if (card.getTemplateId().equals(templatedId)) {
					card.setTierNumber(tierNumber); // As a quick fix this tier number is being set. Later loyalty will directly pass tierNumber in userCards node itself.
					return card;
				}
			}
		}
		return null;
	}


	private void setBnplDisabledStateDetails(String disabledMsg, BNPLVariant bnplVariant, BNPLDetails bnplDetails, String countryCode) {
		if (StringUtils.isNotBlank(disabledMsg)) {
			bnplDetails.setBnplDisabledMsg(disabledMsg);
		}
		if (StringUtils.isNotBlank(disabledMsg)) {
			bnplDetails.setBnplNewVariantSubText(disabledMsg);
		}
		if (utility.isIHFunnel(countryCode, "")) {
			bnplDetails.setBnplNewVariantText(polyglotService.getTranslatedData(BNPLVariant.BNPL_AT_0.equals(bnplVariant) ? GI_BNPL_0_VARIANT_TEXT_IH : GI_BNPL_1_VARIANT_TEXT_IH));
		} else {
			bnplDetails.setBnplNewVariantText(polyglotService.getTranslatedData(BNPLVariant.BNPL_AT_1.equals(bnplVariant) ? GI_BNPL_1_VARIANT_TEXT : GI_BNPL_0_VARIANT_TEXT));
		}
		if(bnplVariant!=null && !BNPLVariant.BNPL_NOT_APPLICABLE.equals(bnplVariant)){
			bnplDetails.setBnplVariant(bnplVariant.name());
		}
	}

	public RateplansUpgrade prepareUpgradeInfo(HotelRates hotelRates, TotalPricing totalPricing, int los, BlackBenefits blackBenefits, String selectedRoomCode) {
		UpgradeInfo upgradeInfo = hotelRates.getUpgradeInfo() != null ? hotelRates.getUpgradeInfo() : new UpgradeInfo();
		RoomInfo baseRoomInfo;
		if(StringUtils.isNotEmpty(selectedRoomCode) && MapUtils.isNotEmpty(hotelRates.getRoomInfo()) && hotelRates.getRoomInfo().get(selectedRoomCode) != null)
			baseRoomInfo = hotelRates.getRoomInfo().get(selectedRoomCode);
		else
			baseRoomInfo = MapUtils.isNotEmpty(hotelRates.getRoomInfo()) ? hotelRates.getRoomInfo().values().stream().findFirst().orElse(new RoomInfo()) : new RoomInfo();		RoomInfo upgradedRoomInfo = upgradeInfo.getRoomInfo();
		com.mmt.hotels.model.response.mmtprime.BlackInfo blackInfo = hotelRates.getBlackInfo() != null ? hotelRates.getBlackInfo() : new com.mmt.hotels.model.response.mmtprime.BlackInfo();
		RateplansUpgrade rateplansUpgrade = new RateplansUpgrade();
		rateplansUpgrade.setTierImage(upgradeInfo.getTierImage());
		rateplansUpgrade.setTierImageV2(upgradeInfo.getTierImageV2());
		rateplansUpgrade.setBlackPopupIcon(upgradeInfo.getBlackPopupIcon());
		rateplansUpgrade.setTitle(polyglotService.getTranslatedData(getUpgradePopUpTitleKey(blackBenefits)));
		rateplansUpgrade.setSubTitle(polyglotService.getTranslatedData(GOTRIBE_BENEFITS_SUB_TITLE_REVIEW_POPUP));
		rateplansUpgrade.setDisclaimer(polyglotService.getTranslatedData(UPGRADE_RATE_PLAN_POPUP_DISCLAIMER));
		rateplansUpgrade.setSelectedTitle(polyglotService.getTranslatedData(SELECTED_TITLE));
		rateplansUpgrade.setUpgradedTitle(polyglotService.getTranslatedData(getUpgradedTitleKey(blackBenefits)));
		rateplansUpgrade.setSelectedRateplans(buildRatePlanDetails(totalPricing, baseRoomInfo, los));
		rateplansUpgrade.setUpgradedRateplans(buildRatePlanDetails(totalPricing, upgradedRoomInfo, los));
		rateplansUpgrade.setUpgradeType(utility.getUpgradeType(blackBenefits));

		if (blackBenefits != null && blackBenefits.isMealUpgrade() && !blackBenefits.isRoomUpgrade()) {
			String mealPlanCode = MEAL_PLAN_CODE_ROOM_ONLY;
			if (StringUtils.isNotBlank(upgradeInfo.getUpgradedMealPlanCode())) {
				mealPlanCode = upgradeInfo.getUpgradedMealPlanCode();
			}
			rateplansUpgrade.setMealUpgradeDescription(polyglotService.getTranslatedData(MEAL_UPGRADE_TEXT).replace("{meal}", polyglotService.getTranslatedData(mealPlanMapPolyglot.get(mealPlanCode))));
		}

		rateplansUpgrade.setPositiveCtaText(polyglotService.getTranslatedData(UPGRADE_POSITIVE_CTA_TEXT));
		rateplansUpgrade.setNegativeCtaText(polyglotService.getTranslatedData(UPGRADED_NEGATIVE_CTA_TEXT));
		//Setting the 1st inclusion in upgraded rate plan
		if(CollectionUtils.isNotEmpty(rateplansUpgrade.getUpgradedRateplans()) && CollectionUtils.isNotEmpty(upgradeInfo.getInclusions())) {
			rateplansUpgrade.getUpgradedRateplans().get(0).setInclusionsList(new ArrayList<>());
			rateplansUpgrade.getUpgradedRateplans().get(0).getInclusionsList().add(utility.prepareBookedInclusionFromInclusion(upgradeInfo.getInclusions().get(0)));
		}
		if (CollectionUtils.isNotEmpty(rateplansUpgrade.getUpgradedRateplans()) && upgradeInfo.getUpgradeRateplanOriginalPriceDetails() != null) {
			updateOriginalPriceTextInUpgradedRateplan(rateplansUpgrade.getUpgradedRateplans(), upgradeInfo.getUpgradeRateplanOriginalPriceDetails(), los);
		}
		return rateplansUpgrade;
	}

	private void updateOriginalPriceTextInUpgradedRateplan(List<RatePlanDetails> upgradedRateplans, UpgradedRateplanOriginalPriceDetails upgradeRateplanOriginalPriceDetails, int los) {
		int price = (int) (upgradeRateplanOriginalPriceDetails.getDisplayPrice() + upgradeRateplanOriginalPriceDetails.getTaxes());
		if (price > 0 && CollectionUtils.isNotEmpty(upgradedRateplans) && upgradedRateplans.get(0) != null && upgradedRateplans.get(0).getPriceMap() != null) {
			price = price / los;
			upgradedRateplans.get(0).getPriceMap().setOriginalPrice(price);
			upgradedRateplans.get(0).getPriceMap().setOriginalPriceMsg(polyglotService.getTranslatedData(UPGRADE_RATE_PLAN_ORIGINAL_PRICE_GI));
		}
	}

	private String getUpgradedTitleKey(BlackBenefits blackBenefits) {
		if (blackBenefits != null) {
			if (blackBenefits.isRoomUpgrade() && blackBenefits.isMealUpgrade()) {
				return UPGRADED_ROOM_AND_MEAL_TITLE;
			} else if (blackBenefits.isRoomUpgrade()) {
				return UPGRADED_ROOM_TITLE;
			} else if (blackBenefits.isMealUpgrade()) {
				return UPGRADED_MEAL_TITLE;
			}
		}
		return UPGRADED_TITLE;
	}

	private String getUpgradePopUpTitleKey(BlackBenefits blackBenefits) {
		if (blackBenefits != null) {
			if (blackBenefits.isRoomUpgrade() && blackBenefits.isMealUpgrade()) {
				return ROOM_AND_MEAL_UPGRADE_POPUP_TITLE_GI;
			} else if (blackBenefits.isRoomUpgrade()) {
				return ROOM_UPGRADE_POPUP_TITLE_GI;
			} else if (blackBenefits.isMealUpgrade()) {
				return MEAL_UPGRADE_POPUP_TITLE_GI;
			}
		}
		return DEFAULT_UPGRADE_POPUP_TITLE_GI;
	}

	private List<RatePlanDetails> buildRatePlanDetails(TotalPricing totalPricing, RoomInfo baseRoomInfo, int los) {
		List<RatePlanDetails> ratePlanDetailsList = new ArrayList<>();
		if (baseRoomInfo != null) {
			RatePlanDetails ratePlanDetails = new RatePlanDetails();
			ratePlanDetails.setRoomName(baseRoomInfo.getRoomName());
			ratePlanDetails.setRoomDesc(buildRoomDesc(baseRoomInfo));
			ratePlanDetails.setRoomHighlights(buildRoomHighlights(baseRoomInfo));
			ratePlanDetails.setPriceMap(buildPriceMap(totalPricing, los));
			ratePlanDetailsList.add(ratePlanDetails);
		}
		return ratePlanDetailsList;
	}

	private TotalPricing buildPriceMap(TotalPricing totalPricingInput, int los) {
		TotalPricing totalPricing = new TotalPricing();
		if (totalPricingInput != null) {
			totalPricing.setDetails(preparePriceDetailsPerNight(totalPricingInput.getDetails(), totalPricingInput.getCoupons(), los));
			totalPricing.setNoCouponText(totalPricingInput.getNoCouponText());
			totalPricing.setCouponSubtext(totalPricingInput.getCouponSubtext());
			totalPricing.setPriceDisplayMsg(polyglotService.getTranslatedData(PER_NIGHT_TEXT));
			totalPricing.setPriceTaxMsg(getTaxMessage(totalPricing.getDetails(), StringUtils.isNotBlank(totalPricingInput.getCurrency()) ? totalPricingInput.getCurrency() : DEFAULT_CUR_INR));
			totalPricing.setCouponAmount(totalPricingInput.getCouponAmount());
			totalPricing.setGroupPriceText(totalPricingInput.getGroupPriceText());
			totalPricing.setSavingsText(totalPricingInput.getSavingsText());
			totalPricing.setPinCodeMandatory(totalPricingInput.isPinCodeMandatory());
			totalPricing.setCurrency(totalPricingInput.getCurrency());
			totalPricing.setPricingKey(totalPricingInput.getPricingKey());
		}
		return totalPricing;
	}

	private String getTaxMessage(List<PricingDetails> details, String currency) {
		Double taxAmount = 0.0;
		for (PricingDetails pricingDetails : details) {
			taxAmount = taxAmount + (Constants.TAXES_KEY.equalsIgnoreCase(pricingDetails.getKey()) ? pricingDetails.getAmount() : 0.0);
		}
		return PLUS + SPACE + Currency.getCurrencyEnum(currency).getCurrencySymbol() + SPACE + taxAmount.intValue() + SPACE + polyglotService.getTranslatedData(ConstantsTranslation.TAXES_ROOM_UPGRADE_LABEL);
	}

	private List<PricingDetails> preparePriceDetailsPerNight(List<PricingDetails> details, List<Coupon> coupons, int los) {
		double preAppliedCouponAmount = CollectionUtils.isNotEmpty(coupons) ? coupons.stream().filter(Coupon::isAutoApplicable).findFirst().map(Coupon::getCouponAmount).orElse(0.0) : 0.0;
		List<PricingDetails> pricingDetails = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(details)) {
			for (PricingDetails pricingDetail : details) {
				PricingDetails pricingDetailPerNight = new PricingDetails();
				pricingDetailPerNight.setAmount(getAmount(pricingDetail.getKey(), pricingDetail.getAmount(), los, preAppliedCouponAmount));
				pricingDetailPerNight.setLabel(pricingDetail.getLabel());
				pricingDetailPerNight.setType(pricingDetail.getType());
				pricingDetailPerNight.setKey(pricingDetail.getKey());
				pricingDetailPerNight.setHotelierCurrencyAmount(pricingDetail.getHotelierCurrencyAmount() != null && los > 0 ? pricingDetail.getHotelierCurrencyAmount() / los : null);
				pricingDetailPerNight.setHotelierCurrencyCode(pricingDetail.getHotelierCurrencyCode());
				pricingDetailPerNight.setSubTitle(pricingDetail.getSubTitle());
				pricingDetailPerNight.setSubLine(pricingDetail.getSubLine());
				pricingDetails.add(pricingDetailPerNight);
			}
		}
		return pricingDetails;
	}

	private double getAmount(String key, double amount, int los, double preAppliedCouponAmount) {
		key = key.toUpperCase();
		if (preAppliedCouponAmount != 0.0) {
			if ("TOTAL_DISCOUNT".equalsIgnoreCase(key)) {
				return los > 0 ? Utility.round((amount + preAppliedCouponAmount) / los, 0) : 0.0;
			}
			if (Arrays.asList("PRICE_AFTER_DISCOUNT", "TOTAL_AMOUNT").contains(key)) {
				return los > 0 ? Utility.round((amount - preAppliedCouponAmount) / los, 0) : 0.0;
			}
		}
		return los > 0 ? Utility.round(amount / los, 0) : 0.0;
	}

	private String buildRoomDesc(RoomInfo roomInfo) {
		String roomDesc = "";
		if (StringUtils.isNotEmpty(roomInfo.getRoomSize())) {
			roomDesc = roomDesc + roomInfo.getRoomSize() + SPACE + roomInfo.getRoomSizeUnit();
		}
		if (CollectionUtils.isNotEmpty(roomInfo.getBeds()) && StringUtils.isNotBlank(roomInfo.getBeds().get(0).getType())) {
			roomDesc +=  PIPE_SEPARATOR + roomInfo.getBeds().get(0).getType();
		}
		if (StringUtils.isNotEmpty(roomInfo.getRoomViewName())) {
			roomDesc += PIPE_SEPARATOR + roomInfo.getRoomViewName();
		}
		return roomDesc;
	}

	private List<RoomHighlight> buildRoomHighlights(RoomInfo roomInfo) {
		List<RoomHighlight> roomHighlightList = new ArrayList<>();

		if(StringUtils.isNotBlank(roomInfo.getRoomSize())) {
			RoomHighlight roomHighlight = new RoomHighlight();
			roomHighlight.setIconUrl("https://gos3.ibcdn.com/roomSizeBlack-1678093548.png");
			roomHighlight.setText(roomInfo.getRoomSize() + " " + roomInfo.getRoomSizeUnit());
			roomHighlight.setDescription(roomHighlight.getText());
			roomHighlight.setIdentifier(RoomHighlightType.ROOM_SIZE.name());
			roomHighlightList.add(roomHighlight);
		}
		if (CollectionUtils.isNotEmpty(roomInfo.getBeds()) && StringUtils.isNotBlank(roomInfo.getBeds().get(0).getType())) {
			RoomHighlight roomHighlight = new RoomHighlight();
			roomHighlight.setIconUrl("https://gos3.ibcdn.com/bedBlackIcon-1678093474.png");
			List<String> bedTypeList = new ArrayList<>();
			roomInfo.getBeds().forEach(bedType -> {
				String bedTypeText;
				if (bedType.getCount() > 1) {
					bedTypeText = bedType.getCount() + SPACE_X_SPACE + bedType.getType();
				} else {
					bedTypeText = bedType.getType();
				}
				bedTypeList.add(bedTypeText);
			});
			roomHighlight.setText(String.join(COMMA_SPACE, bedTypeList));
			roomHighlight.setDescription(String.join(COMMA_SPACE, bedTypeList));
			roomHighlight.setIdentifier(RoomHighlightType.BED_TYPE.name());
			roomHighlightList.add(roomHighlight);
		}
		if (StringUtils.isNotEmpty(roomInfo.getRoomViewName())) {
			RoomHighlight roomHighlight = new RoomHighlight();
			roomHighlight.setIconUrl("https://gos3.ibcdn.com/roomViewIcon-1678093525.png");
			roomHighlight.setText(roomInfo.getRoomViewName());
			roomHighlight.setDescription(roomInfo.getRoomViewName());
			roomHighlight.setIdentifier(RoomHighlightType.ROOM_VIEW.name());
			roomHighlightList.add(roomHighlight);
		}
		// As per product : max guests
//		if (roomInfo.getMaxGuestCount() > 0) {
//			RoomHighlight roomHighlight = new RoomHighlight();
//			roomHighlight.setIconUrl("https://gos3.ibcdn.com/paxBlackIcon-1678093500.png");
//			if (roomInfo.getMaxGuestCount() == 1) {
//				String countText = String.format("Max %s Guest",roomInfo.getMaxGuestCount());
//				roomHighlight.setText(countText);
//				roomHighlight.setDescription(countText);
//			}else {
//				String countText = String.format("Max %s Guests",roomInfo.getMaxGuestCount());
//				roomHighlight.setText(countText);
//				roomHighlight.setDescription(countText);
//			}
//			roomHighlight.setIdentifier(RoomHighlightType.GUEST_COUNT.name());
//			roomHighlightList.add(roomHighlight);
//		}
		return roomHighlightList;
	}

	public TagInfo prepareTagInfo(BlackBenefits blackBenefits) {
		if (blackBenefits != null) {
			if (blackBenefits.isRoomUpgrade()) {
				return TagInfo.builder()
						.title(polyglotService.getTranslatedData(ConstantsTranslation.ROOM_UPGRADED_TAG_TITLE))
						.color(polyglotService.getTranslatedData(UPGRADED_TAG_TITLE_COLOR))
						.build();
			}
		}
		return null;
	}

	public PersuasionResponse getSaleCampaignPersuasion(CampaignPojo campaignPojo) {
		if (null != campaignPojo && StringUtils.isNotBlank(campaignPojo.getHeading())) {
			PersuasionResponse persuasionResponse = new PersuasionResponse();
			persuasionResponse.setTitle(campaignPojo.getHeading());
			persuasionResponse.setTitleColor(campaignPojo.getHeadingColor());
			persuasionResponse.setSubText(campaignPojo.getDescription());
			persuasionResponse.setIconUrl(campaignPojo.getIconUrl());
			persuasionResponse.setBackgroundImage(campaignPojo.getBackgroundImage());
			persuasionResponse.setId(campaignPojo.getId());
			return persuasionResponse;
		}
		return null;
	}

	private TimerCard buildTimerCard(com.mmt.hotels.model.response.altaccodata.TimerCard hesTimerCard){
		if(hesTimerCard != null){
			TimerCard timerCard = new TimerCard();
			timerCard.setBottomTitle(hesTimerCard.getBottomTitle());
			timerCard.setBottomSubtitle(hesTimerCard.getBottomSubtitle());
			timerCard.setBgImageUrl(hesTimerCard.getBgImageUrl());
			timerCard.setTimerTextPrefix(hesTimerCard.getTimerTextPrefix());
			timerCard.setTimerRemaining(hesTimerCard.getTimerRemaining());
			return timerCard;
		}
		return null;
	}

	private ViewAllCard buildViewAllCard(com.mmt.hotels.model.response.altaccodata.ViewAllCard hesViewAllCard){
		if(hesViewAllCard != null){
			ViewAllCard viewAllCard = new ViewAllCard();
			viewAllCard.setTitle(hesViewAllCard.getTitle());
			viewAllCard.setSubTitle(hesViewAllCard.getSubTitle());
			viewAllCard.setIconUrl(hesViewAllCard.getIconUrl());
			viewAllCard.setCtaTitle(hesViewAllCard.getCtaTitle());
			viewAllCard.setVoyCityID(hesViewAllCard.getVoyCityID());
			return viewAllCard;
		}
		return null;
	}

	public CardData addBusinessIdentificationCard(int savingPerc, boolean isReviewPageApi) {
		CardData cardData = new CardData();
		CardInfo cardInfo = new CardInfo();
		cardInfo.setTemplateId(CardTemplateId.BUSINESSIDENTIFICATION_CARD.name());
		cardInfo.setTitleText(polyglotService.getTranslatedData(ConstantsTranslation.BUSINESSIDENTIFICATION_CARD_TITLE_TEXT));
		cardInfo.setTitleTextColor(BUSINESSIDENTIFICATION_CARD_TITLE_TEXT_COLOR);
		if (isReviewPageApi) {
			cardInfo.setSubText(polyglotService.getTranslatedData(ConstantsTranslation.BUSINESSIDENTIFICATION_CARD_SUB_TEXT_REVIEW_PAGE));
		} else {
			cardInfo.setSubText(polyglotService.getTranslatedData(ConstantsTranslation.BUSINESSIDENTIFICATION_CARD_SUB_TEXT).replace(SAVING_PERC, String.valueOf(savingPerc)));
		}
		cardInfo.setIconURL(BUSINESSIDENTIFICATION_CARD_ICON_URL);
		cardInfo.setBgGradientStart(BUSINESSIDENTIFICATION_BG_GRADIENT_START);
		cardInfo.setBgGradientEnd(BUSINESSIDENTIFICATION_BG_GRADIENT_END);
		cardData.setCardInfo(cardInfo);
		return cardData;
	}

	public CardData buildBusinessIdentificationCards(HotelRates hotelRates, String affiliateId) {
		if(StringUtils.isEmpty(affiliateId) || hotelRates==null){
			return null;
		}
		try {
			String controller = MDC.get(MDCHelper.MDCKeys.CONTROLLER.getStringValue());
			Set<String> allowedSegments = new HashSet<>();
			boolean isReviewPageApi = false;
			if(utility.isDetailPageAPI(controller)){
				String segmentId = hotelRates.getLowestRate()!=null?hotelRates.getLowestRate().getSegmentId(): EMPTY_STRING;
				if(StringUtils.isNotEmpty(segmentId)){
					allowedSegments.add(segmentId);
				}
			}else if(utility.isReviewPageAPI(controller) && CollectionUtils.isNotEmpty(hotelRates.getAvailableSegments())){
				isReviewPageApi = true;
				allowedSegments.addAll(hotelRates.getAvailableSegments());
			}
			if (utility.isBusinessIdentificationApplicable(affiliateId,allowedSegments)) {
				int savingPerc = 0;
				if (hotelRates != null) {
					if (hotelRates.getRoomTypeDetails() != null && MapUtils.isNotEmpty(hotelRates.getRoomTypeDetails().getRoomType())
							&& hotelRates.getRoomTypeDetails().getRoomType().entrySet().iterator().next() != null) {
						RoomType roomType = hotelRates.getRoomTypeDetails().getRoomType().entrySet().iterator().next().getValue();
						if (roomType != null && MapUtils.isNotEmpty(roomType.getRatePlanList()) && roomType.getRatePlanList().entrySet().iterator().next() != null) {
							com.mmt.hotels.model.response.pricing.RatePlan ratePlan = roomType.getRatePlanList().entrySet().iterator().next().getValue();
							if (ratePlan != null && ratePlan.getDisplayFare() != null && ratePlan.getDisplayFare().getDisplayPriceBreakDown() != null) {
								savingPerc = (int) ratePlan.getDisplayFare().getDisplayPriceBreakDown().getSavingPerc();
							}
						}
					} else if (hotelRates.getRecommendedRoomTypeDetails() != null && hotelRates.getRecommendedRoomTypeDetails().getTotalDisplayFare() != null && hotelRates.getRecommendedRoomTypeDetails().getTotalDisplayFare().getDisplayPriceBreakDown() != null) {
						savingPerc = (int) hotelRates.getRecommendedRoomTypeDetails().getTotalDisplayFare().getDisplayPriceBreakDown().getSavingPerc();
					} else if (hotelRates.getOccupencyLessRoomTypeDetails() != null && hotelRates.getOccupencyLessRoomTypeDetails().getTotalDisplayFare() != null && hotelRates.getOccupencyLessRoomTypeDetails().getTotalDisplayFare().getDisplayPriceBreakDown() != null) {
						savingPerc = (int) hotelRates.getOccupencyLessRoomTypeDetails().getTotalDisplayFare().getDisplayPriceBreakDown().getSavingPerc();
					}
				}
				return addBusinessIdentificationCard(savingPerc, isReviewPageApi);
			}
		} catch (Exception ex) {
			logger.error("Error while building business identification cards {}", ex.getMessage());
		}
		return null;
	}

    public PersuasionResponse getLongStayBenefitPersuasion(LongStayBenefits longStayBenefits) {
		PersuasionResponse persuasionResponse = null;
		if(longStayBenefits != null){
			persuasionResponse = new PersuasionResponse();
			persuasionResponse.setTitle(longStayBenefits.getTitle());
			persuasionResponse.setTitleColor(longStayBenefits.getTitleColor());
			persuasionResponse.setPersuasions(buildLongStayBenefitPersuasions(longStayBenefits));
		}
		return persuasionResponse;
    }

	private List<PersuasionData> buildLongStayBenefitPersuasions(LongStayBenefits longStayBenefits) {
		List<PersuasionData> persuasionDataList = new ArrayList<>();
		if(CollectionUtils.isNotEmpty(longStayBenefits.getInclusionsList())){
			for(Inclusion inclusion : longStayBenefits.getInclusionsList()){
				if(LOS.equalsIgnoreCase(inclusion.getInclusionType())) {
					PersuasionData persuasionData = new PersuasionData();
					persuasionData.setText(inclusion.getCode());
					persuasionData.setIcon(LOS_BENEFIT_ICON_URL);
					persuasionDataList.add(persuasionData);
				}
			}
		}
		return persuasionDataList;
	}

	public PersuasionResponse buildTajGiftCardOrHotelCreditPersuasion(HotelBenefitInfo hotelBenefitInfo) {
		PersuasionResponse tajGiftCardPersuasion = new PersuasionResponse();
		String controller = MDC.get(MDCHelper.MDCKeys.CONTROLLER.getStringValue());
		if(!utility.isReviewPageAPI(controller)) {
			//This method is being called only for review and thank-you page
			//set title only for thank-you page
			tajGiftCardPersuasion.setTitle(hotelBenefitInfo.getTitle());
		}
		tajGiftCardPersuasion.setTitleColor("#000000");
		tajGiftCardPersuasion.setPersuasionType(hotelBenefitInfo.getBenefitType());
		tajGiftCardPersuasion.setPersuasionText(hotelBenefitInfo.getText());
		tajGiftCardPersuasion.setIconUrl(hotelBenefitInfo.getIconUrl());
		tajGiftCardPersuasion.setHtml(true);
		Style style = new Style();
		style.setTextColor("#4a4a4a");
		style.setIconHeight(37);
		style.setIconWidth(41);
		tajGiftCardPersuasion.setStyle(style);
		return tajGiftCardPersuasion;
	}
}


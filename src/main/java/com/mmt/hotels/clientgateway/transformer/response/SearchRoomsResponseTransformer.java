package com.mmt.hotels.clientgateway.transformer.response;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.mmt.hotels.clientgateway.businessobjects.AdditionalChargesBO;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.enums.ExperimentKeys;
import com.mmt.hotels.clientgateway.enums.OfferCard;
import com.mmt.hotels.clientgateway.enums.RoomHighlightType;
import com.mmt.hotels.clientgateway.enums.SelectRoomCardFilterType;
import com.mmt.hotels.clientgateway.pms.*;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.request.dayuse.DayUseRoomsRequest;
import com.mmt.hotels.clientgateway.response.FacilityGroup;
import com.mmt.hotels.clientgateway.response.TotalPricing;
import com.mmt.hotels.clientgateway.response.*;
import com.mmt.hotels.clientgateway.response.availrooms.Alert;
import com.mmt.hotels.clientgateway.response.availrooms.FeatureFlags;
import com.mmt.hotels.clientgateway.response.dayuse.rooms.*;
import com.mmt.hotels.clientgateway.response.moblanding.CardData;
import com.mmt.hotels.clientgateway.response.rooms.LinkedRate;
import com.mmt.hotels.clientgateway.response.rooms.MediaData;
import com.mmt.hotels.clientgateway.response.rooms.PackageInclusionDetails;
import com.mmt.hotels.clientgateway.response.rooms.QuickBook;
import com.mmt.hotels.clientgateway.response.rooms.RoomTariff;
import com.mmt.hotels.clientgateway.response.rooms.*;
import com.mmt.hotels.clientgateway.response.rooms.SharedInfo;
import com.mmt.hotels.clientgateway.response.rooms.Space;
import com.mmt.hotels.clientgateway.response.rooms.SpaceData;
import com.mmt.hotels.clientgateway.response.rooms.StayDetail;
import com.mmt.hotels.clientgateway.response.searchHotels.BorderGradient;
import com.mmt.hotels.clientgateway.response.staticdetail.AllInclusiveCard;
import com.mmt.hotels.clientgateway.response.thankyou.RtbCard;
import com.mmt.hotels.clientgateway.response.thankyou.RtbPersuasionCard;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionObject;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionStyle;
import com.mmt.hotels.clientgateway.util.*;
import com.mmt.hotels.model.enums.BNPLVariant;
import com.mmt.hotels.model.enums.LinkedRateSubType;
import com.mmt.hotels.model.enums.LinkedRateValue;
import com.mmt.hotels.model.response.dayuse.MissingSlotDetail;
import com.mmt.hotels.model.response.dayuse.Slot;
import com.mmt.hotels.model.response.mmtprime.BlackInfo;
import com.mmt.hotels.model.response.pricing.*;
import com.mmt.hotels.model.response.pricing.Inclusion;
import com.mmt.hotels.model.response.pricing.LinkedRateDetail;
import com.mmt.hotels.model.response.pricing.RatePlan;
import com.mmt.hotels.model.response.pricing.WalletSurge;
import com.mmt.hotels.model.response.pricing.CancelPenalty.CancellationType;
import com.mmt.hotels.model.response.staticdata.*;
import com.mmt.hotels.model.response.staticdata.SleepingBedInfo;
import com.mmt.hotels.model.response.staticdata.SleepingInfoArrangement;
import com.mmt.hotels.pojo.response.CampaignPojo;
import com.mmt.hotels.util.Tuple;
import com.mmt.model.*;
import com.mmt.model.RoomInfo;
import com.mmt.propertymanager.config.PropertyManager;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.*;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.HOSTEL_BEDS_AVAILABLE_TEXT;
import static com.mmt.hotels.clientgateway.constants.ControllerConstants.DETAIL_SEARCH_ROOMS;
import static com.mmt.hotels.clientgateway.constants.ControllerConstants.DETAIL_SEARCH_SLOTS;
import static com.mmt.hotels.clientgateway.enums.ExperimentKeys.roomUpsell;
import static com.mmt.hotels.clientgateway.util.Utility.getExperimentValue;
import static java.lang.Math.*;

@Component
public abstract class SearchRoomsResponseTransformer {

	@Autowired
	private CommonResponseTransformer commonResponseTransformer;

	@Autowired
	private Utility utility;

	@Autowired
	PropertyManager propManager;

	@Autowired
	private DateUtil dateUtil;

	@Autowired
	private DayUseUtil dayUseUtil;

	@Autowired
	private MetricAspect metricAspect;

	@Value("${pah.without.cc.text}")
	private String pahwithoutccText;

	@Value("${pah.with.cc.text}")
	private String pahwithccText;

	@Value("${pah.gcc.text}")
	private String pahGccText;

	@Value("#{'${mypat_exclusive_rate_segmentId.list}'.split(',')}")
	private Set<String> mypatExclusiveRateSegmentIdList;

	@Value("${flyer.persuasion.color.detail}")
	private String flyerPersuasionColorDetail;

	@Value("${flyer.persuasion.image.url.detail}")
	private String flyerPersuasionImageUrlDetail;

	@Value("#{'${combo.title.meal.plan.code}'.split(',')}")
	private List<String> mealPlanCodeList;

	private int apLimitForInclusionIcons = 2;

	private Map<String,String> mealPlanMapPolyglot;

	private int ratePlanMoreOptionsLimit = 1;
	private boolean mealplanFilterEnable;
	private boolean partnerExclusiveFilterEnable;
	private Map<String,Map<String,List<String>>> supplierToRateSegmentMapping;

	@Autowired
	protected PolyglotService polyglotService;

	@Autowired
	private ObjectMapperUtil objectMapperUtil;

	@Autowired
	private InjectHermesResponseTransformer injectHermesResponseTransformer;

	private static final Gson gson = new Gson();
	private static final Logger LOGGER = LoggerFactory.getLogger(SearchRoomsResponseTransformer.class);

	private Map<String,Map<String,Map<String,Integer>>> ratePlanDisplayLogic;
	Map<String, String> rtbCardConfigs;

	String mandatoryChargesAlert;

	AllInclusiveCard allInclusiveCard;

	private MissingSlotDetail missingSlotDetails = null;

	private Map<String, DayUsePersuasion> dayUseFunnelPersuasions = null;

	private Map<String, GroupRatePlanFilter> groupRatePlanFilterConfMap;

	@Value("${group.filters.GI}")
	private String groupFilterMap;

	private int thresholdForSlashedAndDefaultHourPrice = 0;

	private Map<String,StayTypeInfo> actionInfoMap = new HashMap<>();

	@Autowired
	private MobConfigHelper mobConfigHelper;

	@Value("${elite.package.icon.url}")
	private String elitePackageIconUrl;

	@Value("${elite.package.icon.url.updated}")
	private String elitePackageIconUrlUpdated;

	@Value("${elite.package.type}")
	private String elitePackageType;

	@Autowired
	OfferCardUtil offerCardUtil;

	@Autowired
	SelectRoomV2ResponseTransformer selectRoomV2ResponseTransformer;

	@PostConstruct
	public void init() {
		CommonConfig commonConfig = propManager.getProperty("commonConfig", CommonConfig.class);
		thresholdForSlashedAndDefaultHourPrice = commonConfig.thresholdForSlashedAndDefaultHourPrice();
		mealPlanMapPolyglot = commonConfig.mealPlanMapPolyglot();
		commonConfig.addPropertyChangeListener("mealPlanMapPolyglot", evt -> mealPlanMapPolyglot = commonConfig.mealPlanMapPolyglot());
		ratePlanMoreOptionsLimit  = commonConfig.ratePlanMoreOptionsLimit();
		commonConfig.addPropertyChangeListener("ratePlanMoreOptionsLimit", evt -> ratePlanMoreOptionsLimit = commonConfig.ratePlanMoreOptionsLimit());
		ratePlanDisplayLogic = commonConfig.ratePlanDisplayLogic();
		commonConfig.addPropertyChangeListener("ratePlanDisplayLogic",evt->ratePlanDisplayLogic = commonConfig.ratePlanDisplayLogic());
		apLimitForInclusionIcons = commonConfig.apLimitForInclusionIcons();
		mealplanFilterEnable = commonConfig.mealplanFilterEnable();
		commonConfig.addPropertyChangeListener("mealplanFilterEnable", event -> mealplanFilterEnable = commonConfig.mealplanFilterEnable());
		partnerExclusiveFilterEnable = commonConfig.partnerExclusiveFilterEnable();
		commonConfig.addPropertyChangeListener("partnerExclusiveFilterEnable", event -> partnerExclusiveFilterEnable = commonConfig.partnerExclusiveFilterEnable());
		rtbCardConfigs = commonConfig.rtbCardConfigs();
		commonConfig.addPropertyChangeListener("rtbCardConfigs", event -> rtbCardConfigs = commonConfig.rtbCardConfigs());
		mandatoryChargesAlert = commonConfig.mandatoryChargesAlert();
		commonConfig.addPropertyChangeListener("mandatoryChargesAlert", event -> mandatoryChargesAlert = commonConfig.mandatoryChargesAlert());
		allInclusiveCard = commonConfig.allInclusiveCard();
		commonConfig.addPropertyChangeListener("allInclusiveCard", event -> allInclusiveCard = commonConfig.allInclusiveCard());
		supplierToRateSegmentMapping = commonConfig.supplierToRateSegmentMapping();
		commonConfig.addPropertyChangeListener("supplierToRateSegmentMapping", event -> supplierToRateSegmentMapping = commonConfig.supplierToRateSegmentMapping());
		//Added missing slot and persuasion details of dayUse detail page
		missingSlotDetails = commonConfig.missingSlotDetails();
		commonConfig.addPropertyChangeListener("missingSlotDetails", event -> missingSlotDetails = commonConfig.missingSlotDetails());
		dayUseFunnelPersuasions = commonConfig.dayUseFunnelPersuasions();
		commonConfig.addPropertyChangeListener("dayUseFunnelPersuasions", event -> dayUseFunnelPersuasions = commonConfig.dayUseFunnelPersuasions());
		groupRatePlanFilterConfMap = gson.fromJson(groupFilterMap, new TypeToken<Map<String, GroupRatePlanFilter>>() {}.getType());

		try{
			HotelMobConfig hotelMobileConfigFromPMS = mobConfigHelper.getHotelMobileConfigFromPMS();
			JsonNode actionInfoJsonNode = hotelMobileConfigFromPMS.getConfigJson().get("actionInfo");
			actionInfoMap = objectMapperUtil.getObjectFromJsonNode(actionInfoJsonNode,  new TypeReference<Map<String,StayTypeInfo>>() {
			});
		}catch (Exception e){
			LOGGER.error("Error while fetching actionInfo from commonConfig");
		}
	}

	/*
	 * myPartner change log :
	 * 	This original method signature is retained. This makes test cases backward compatible
	 * 	The overloaded method is used for all other cases
	 * */
	public SearchRoomsResponse convertSearchRoomsResponse(RoomDetailsResponse roomDetailsResponse, HotelsRoomInfoResponseEntity hotelsRoomInfoResponseEntity,
														  HotelImage hotelImage, Map<String, String> expData, List<RoomStayCandidate> roomStayCandidates,
														  SearchCriteria searchRoomsCriteria, List<Filter> filterCriteria, RequestDetails requestDetails) {
		return convertSearchRoomsResponse(roomDetailsResponse, hotelsRoomInfoResponseEntity,
				hotelImage, expData, roomStayCandidates,
				searchRoomsCriteria, filterCriteria, requestDetails, null);
	}


	/*
	 * myPartner change log :
	 * 	convertSearchRoomsResponse is overloaded to have another parameter CommonModifierResponse which contains profileType and subProfileType
	 *
	 * 	unlike filter-count api [where another parameter is added to the existing method] without overloading for test cases [since there was
	 * 	only a single test case and that is been handled]
	 * 	All calls B2C/CORP/myPartner will flow through this
	 * */
	public SearchRoomsResponse convertSearchRoomsResponse(RoomDetailsResponse roomDetailsResponse, HotelsRoomInfoResponseEntity hotelsRoomInfoResponseEntity,
														  HotelImage hotelImage, Map<String, String> expData, List<RoomStayCandidate> roomStayCandidates, SearchCriteria searchRoomsCriteria,
														  List<Filter> filterCriteria, RequestDetails requestDetails, CommonModifierResponse commonModifierResponse) {
		SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();
		long startTime = System.currentTimeMillis();
		try {
			String client = MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue());
			String askedCurrency = searchRoomsCriteria != null ? searchRoomsCriteria.getCurrency() : "INR";
			if (CollectionUtils.isEmpty(roomDetailsResponse.getHotelRates())) {
				return searchRoomsResponse;
			}
			HotelRates hotelRates = roomDetailsResponse.getHotelRates().get(0);
			boolean isLuxeHotel = utility.isLuxeHotel(hotelRates.getCategories());
			boolean isAltAccoHotel = hotelRates.isAltAcco();
			boolean isBlackRevamp = utility.isExperimentTrue(expData, GOTRIBE_REVAMP_POKUS_EXP_KEY);

			String checkIn = searchRoomsCriteria.getCheckIn();
			String checkOut = searchRoomsCriteria.getCheckOut();
			int los = dateUtil.getDaysDiff(LocalDate.parse(checkIn), LocalDate.parse(checkOut));
			int ap = dateUtil.getDaysDiff(LocalDate.now(), LocalDate.parse(checkIn));

			//GIHTL-15565 Removed Exp as part of clean up.
			//			boolean isBlockPAH = roomDetailsResponse.getExpData() != null && roomDetailsResponse.getExpData().containsKey("blockPAH")
//					&& StringUtils.isNotBlank(roomDetailsResponse.getExpData().get("blockPAH")) && Boolean.parseBoolean(roomDetailsResponse.getExpData().get("blockPAH"));
			boolean isBlockPAH = false;

			List<String> hydrSegment=null;
			if(commonModifierResponse != null && commonModifierResponse.getHydraResponse()!=null && commonModifierResponse.getHydraResponse().getHydraMatchedSegment()!=null)
				hydrSegment = new ArrayList<String>(commonModifierResponse.getHydraResponse().getHydraMatchedSegment());

			searchRoomsResponse.setSpotlightApplicable(hotelRates.isSpotlightApplicable());
			searchRoomsResponse.setOffers(getOffers(hotelRates.getOffers()));
			searchRoomsResponse.setAppliedOffers(getOffers(hotelRates.getAppliedOffers()));
			searchRoomsResponse.setBlackInfo(commonResponseTransformer.buildBlackInfo(hotelRates.getBlackInfo()));
			searchRoomsResponse.setDoubleBlackInfo(commonResponseTransformer.getDoubleBlackInfo(hotelRates.getDoubleBlackInfo()));
			if (isBlackRevamp) searchRoomsResponse.setGoTribeInfo(commonResponseTransformer.buildGoTribeInfo(hotelRates.getBlackInfo(),  polyglotService.getTranslatedData(GOTRIBE_BENEFITS_TITLE_DETAIL), null, true));
			searchRoomsResponse.setMsmeCorpCard(hotelRates.getMsmeCorpCard());
			searchRoomsResponse.setHydraSegments(hydrSegment);
			searchRoomsResponse.setUserLoyaltyStatus(hotelRates.getUserLoyaltyStatus());
			searchRoomsResponse.setMustReadRules(hotelRates.getMustReadRules());
			/*GIHTL-15565 Removed Exp as part of clean up.
			if (utility.isExperimentOn(roomDetailsResponse.getExpData(), Constants.ALL_INCLUSIVE_PLAN_EXPERIMENT) && hotelRates.isAnyRateAllinclusive()) {
				searchRoomsResponse.setAllInclusiveInclusions(hotelRates.getAllInclusiveInclusions());
				searchRoomsResponse.setAllInclusiveCard(buildAllInclusiveCard(hotelRates));
			}*/
			Map<String, String> ratePlanCodeAndNameMap = new HashMap<>();

			/* Make Diff Types of Rooms */
//			if (hotelRates.getRoomTypeDetails() != null && hotelRates.getOccupencyLessRoomTypeDetails() == null) {
			if (hotelRates.getRoomTypeDetails() != null) {
				/*
				 * myPartner change log : commonModifierResponse floated down
				 * */
				searchRoomsResponse.setExactRooms(getRooms(hotelRates,
						hotelRates.getRoomTypeDetails(), hotelsRoomInfoResponseEntity, hotelImage,
						hotelRates.getListingType(), expData, true, askedCurrency, requestDetails.getFunnelSource(),
						hotelRates.getWalletSurge(), hotelRates.getSegments(), los, hotelRates.getStarRating(), ap, isBlockPAH,
						hotelRates.getRoomPersuasions(), commonModifierResponse, false, ratePlanCodeAndNameMap, isLuxeHotel,
						isAltAccoHotel, hotelRates.isUserGCCAndMmtExclusive(), hotelRates.isGroupBookingPrice(),
						false, hotelRates.getBlackInfo(), hotelRates.getPropertyType(), false));
				searchRoomsResponse.setExtraGuestDetailPersuasion(buildExtraGuestDetailPersuasion(hotelRates.getRoomTypeDetails(), isAltAccoHotel));
			} else {
				searchRoomsResponse.setRecommendedCombos(getRecommendedCombos(hotelRates, hotelsRoomInfoResponseEntity, hotelImage, hotelRates.getListingType(), expData, askedCurrency, requestDetails.getFunnelSource(),
						los, ap, isBlockPAH, hotelRates.getRoomPersuasions(), commonModifierResponse, ratePlanCodeAndNameMap, isLuxeHotel, isAltAccoHotel));
				if(searchRoomsResponse.getExtraGuestDetailPersuasion()==null) {
					searchRoomsResponse.setExtraGuestDetailPersuasion(buildExtraGuestDetailPersuasion(hotelRates.getRecommendedRoomTypeDetails(), isAltAccoHotel));
				}
				if(searchRoomsResponse.getExtraGuestDetailPersuasion()==null &&  CollectionUtils.isNotEmpty(hotelRates.getOtherRecommendedRooms())) {
					searchRoomsResponse.setExtraGuestDetailPersuasion(buildExtraGuestDetailPersuasion(hotelRates.getOtherRecommendedRooms().get(0), isAltAccoHotel));
				}
			}
			searchRoomsResponse.setOccupancyRooms(getRooms(hotelRates, hotelRates.getOccupencyLessRoomTypeDetails(), hotelsRoomInfoResponseEntity, hotelImage, hotelRates.getListingType(), expData, false,
					askedCurrency, requestDetails.getFunnelSource(), hotelRates.getWalletSurge(), hotelRates.getSegments(), los, hotelRates.getStarRating(), ap, isBlockPAH,
					hotelRates.getRoomPersuasions(), commonModifierResponse, false, ratePlanCodeAndNameMap, isLuxeHotel, isAltAccoHotel, hotelRates.isUserGCCAndMmtExclusive(), hotelRates.isGroupBookingPrice(), true, null, hotelRates.getPropertyType(), false));

			if(searchRoomsResponse.getExtraGuestDetailPersuasion()==null) {
				searchRoomsResponse.setExtraGuestDetailPersuasion(buildExtraGuestDetailPersuasion(hotelRates.getOccupencyLessRoomTypeDetails(), isAltAccoHotel));
			}
			if (hotelRates.getPackageRoomDetails() != null && CollectionUtils.isNotEmpty(searchRoomsResponse.getExactRooms())) {
				searchRoomsResponse.setPackageRooms(
						getRooms(hotelRates, hotelRates.getPackageRoomDetails(), hotelsRoomInfoResponseEntity, hotelImage, hotelRates.getListingType(), expData,
								true, askedCurrency, requestDetails.getFunnelSource(),
								hotelRates.getWalletSurge(), hotelRates.getSegments(), los, hotelRates.getStarRating(), ap,
								isBlockPAH, hotelRates.getRoomPersuasions(), commonModifierResponse, true, ratePlanCodeAndNameMap, isLuxeHotel, isAltAccoHotel, hotelRates.isUserGCCAndMmtExclusive(), hotelRates.isGroupBookingPrice(), false, hotelRates.getBlackInfo(), hotelRates.getPropertyType(), false));
				updatePackageInclusionBaseRatePlanName(ratePlanCodeAndNameMap, searchRoomsResponse.getPackageRooms());
				if(searchRoomsResponse.getExtraGuestDetailPersuasion()==null) {
					searchRoomsResponse.setExtraGuestDetailPersuasion(buildExtraGuestDetailPersuasion(hotelRates.getPackageRoomDetails(), isAltAccoHotel));
				}
			}
			if(hotelRates.getOccassionPackageRoomDetails() != null && hotelRates.getOccassionPackageRoomDetails().getOccassionDetails() != null) {
				searchRoomsResponse.setRecommendedRooms(
						getRooms(hotelRates, hotelRates.getOccassionPackageRoomDetails(), hotelsRoomInfoResponseEntity, hotelImage, hotelRates.getListingType(), expData,
								true, askedCurrency, requestDetails.getFunnelSource(),
								hotelRates.getWalletSurge(), hotelRates.getSegments(), los, hotelRates.getStarRating(), ap,
								isBlockPAH, hotelRates.getRoomPersuasions(), commonModifierResponse, false, ratePlanCodeAndNameMap, isLuxeHotel, isAltAccoHotel, hotelRates.isUserGCCAndMmtExclusive(), hotelRates.isGroupBookingPrice(), false, hotelRates.getBlackInfo(), hotelRates.getPropertyType(), true));
			} else {
				searchRoomsResponse.setRecommendedRooms(
						getRooms(hotelRates, hotelRates.getPackageRoomDetails(), hotelsRoomInfoResponseEntity, hotelImage, hotelRates.getListingType(), expData,
								true, askedCurrency, requestDetails.getFunnelSource(),
								hotelRates.getWalletSurge(), hotelRates.getSegments(), los, hotelRates.getStarRating(), ap,
								isBlockPAH, hotelRates.getRoomPersuasions(), commonModifierResponse, true, ratePlanCodeAndNameMap, isLuxeHotel, isAltAccoHotel, hotelRates.isUserGCCAndMmtExclusive(), hotelRates.isGroupBookingPrice(), false, hotelRates.getBlackInfo(), hotelRates.getPropertyType(), false));
			}
			if((searchRoomsResponse != null && searchRoomsResponse.getRecommendedCombos() != null && searchRoomsResponse.getRecommendedCombos().size() > 1) || (hotelRates.getOccupencyLessRoomTypeDetails() != null && hotelRates.getOccupencyLessRoomTypeDetails().getRoomType() != null && hotelRates.getOccupencyLessRoomTypeDetails().getRoomType().size() > 1))
				commonModifierResponse.setSkipRoomSelectEnabled(false);

			/* Make Diff Types of Rooms */
			searchRoomsResponse.setAddons(commonResponseTransformer.getAddons(hotelRates.getAddOns()));
			boolean bnplNewVariant = false;
			if (MapUtils.isNotEmpty(roomDetailsResponse.getExpData())) {
				bnplNewVariant = roomDetailsResponse.getExpData().containsKey(EXP_BNPL_NEW_VARIANT) && Boolean.parseBoolean(roomDetailsResponse.getExpData().get(EXP_BNPL_NEW_VARIANT));
			}
			BNPLVariant bnplVariant = hotelRates.getBnplVariant();
			/* Using filter method only to update the group filter.
			* New filter listing for GI have to add in groupRatePlanFilterList in getFilters()
			*/
			searchRoomsResponse.setGroupFilters(getFilters(searchRoomsResponse.getExactRooms(), searchRoomsResponse.getOccupancyRooms(), filterCriteria, requestDetails.getFunnelSource(), ap, isBlockPAH, bnplVariant, commonModifierResponse, isLuxeHotel, groupRatePlanFilterConfMap,hotelRates.getPropertyType()));

			//Desciption of the card filter
			List<GroupRatePlanFilter> cardFilters = getCardFilters(searchRoomsResponse.getExactRooms(), searchRoomsResponse.getOccupancyRooms(), filterCriteria, requestDetails.getFunnelSource(), ap, isBlockPAH, bnplVariant, commonModifierResponse, isLuxeHotel, groupRatePlanFilterConfMap, hotelRates.getPropertyType());
			CardFilter cardFilter = new CardFilter();
			if (cardFilters != null && cardFilters.size() > 0){
				cardFilter.setIsEligibleForCardFilter(true);
			} else {
				cardFilter.setIsEligibleForCardFilter(false);
			}
			if (cardFilters != null && utility.isExperimentTrue(expData, roomUpsell.getKey())) {
				cardFilter.setCardFilters(cardFilters);
			}
			searchRoomsResponse.setCardFilter(cardFilter);
			searchRoomsResponse.setSearchRoomDeeplinkUrl(hotelRates.getSearchRoomDeeplinkUrl());
			searchRoomsResponse.setDetailDeeplinkUrl(hotelRates.getDetailDeeplinkUrl());
			searchRoomsResponse.setRecentDeepLink(hotelRates.getListingDeepLinkWithoutFilters()); //				set listingDeepLinkWithoutFilters as recentDeepLink to pass to the client
			searchRoomsResponse.setFeatureFlags(getFeatureFlags(hotelRates, searchRoomsResponse.getExactRooms(), (commonModifierResponse!=null?commonModifierResponse.getExpDataMap():null), commonModifierResponse));
			searchRoomsResponse.setContextDetails(getContextDetails(hotelRates));
			if(!(Constants.FUNNEL_SOURCE_HOMESTAY.equalsIgnoreCase(requestDetails.getFunnelSource()) || Constants.ANDROID.equalsIgnoreCase(client))) {
				searchRoomsResponse.setImpInfo(getImpInfo(searchRoomsResponse.getRecommendedCombos(), roomStayCandidates));
			}
			searchRoomsResponse.setPropertySellableType(buildPropertySellableType(roomDetailsResponse));
			searchRoomsResponse.setSelectedRatePlanCode(hotelRates.getSelectedRatePlanCode());

			searchRoomsResponse.setCardData(setDetailPageCards(hotelRates, roomDetailsResponse, searchRoomsResponse, expData));
			mappingSpaceIdToEachRoomCode(searchRoomsResponse,hotelRates.getSpaceIdToSleepingInfoArrMap());
			Pair<Boolean, Boolean> bedAndRoomPresent = new ImmutablePair<>(false,false);

			boolean isNewDetailPageTrue = commonModifierResponse != null && MapUtils.isNotEmpty(commonModifierResponse.getExpDataMap()) && utility.isExperimentTrue(commonModifierResponse.getExpDataMap(), NEW_DETAIL_PAGE);
			boolean isOHSExpEnable = Constants.PROPERTY_TYPE_HOSTEL.equalsIgnoreCase(hotelRates.getPropertyType()); //Turning it on All Hostels.
			if(isOHSExpEnable){
				bedAndRoomPresent = addSellableLabelFromSellableType(searchRoomsResponse);
			}
			searchRoomsResponse.setRoomInfo(buildRoomInfo(hotelRates, searchRoomsResponse, isOHSExpEnable,bedAndRoomPresent,isNewDetailPageTrue));
			searchRoomsResponse.setRtbPersuasionCard(buildRtbPersuasionCard(hotelRates));
			searchRoomsResponse.setPaymentCard(buildPaymentCard(requestDetails, hotelRates));

			if (hotelsRoomInfoResponseEntity != null && CollectionUtils.isNotEmpty(hotelsRoomInfoResponseEntity.getHtlRmInfo())) {
				searchRoomsResponse.setSharedSpaces(getSpaceData(hotelsRoomInfoResponseEntity.getHtlRmInfo().get(0).getSharedSpaces(), null, expData));
			}
			if (Constants.LISTING_TYPE_ENTIRE.equalsIgnoreCase(hotelRates.getListingType())) {
				Optional<RoomDetails> optionalRoomDetails = Optional.ofNullable(searchRoomsResponse.getExactRooms())
						.flatMap(rooms -> rooms.stream().findFirst());
				if(optionalRoomDetails.isPresent()){
					RoomDetails roomDetails = optionalRoomDetails.get();
					roomDetails.getRatePlans().stream().findFirst().ifPresent(ratePlan -> {
						if (ratePlan.getTariffs() != null && ratePlan.getTariffs().get(0) !=null && ratePlan.getTariffs().get(0).getOccupancydetails()!=null) {
							int roomCount = ratePlan.getTariffs().get(0).getOccupancydetails().getRoomCount();
							if(roomCount>1) {
								searchRoomsResponse.setPropertyLayoutTitleText(MessageFormat.format(polyglotService.getTranslatedData(GI_MULTIPLE_ENTIRE_PROPERTY_LAYOUT_TEXT), roomCount, hotelRates.getPropertyType(),hotelRates.getPropertyType()));
							}
							else {
								searchRoomsResponse.setPropertyLayoutTitleText(MessageFormat.format(polyglotService.getTranslatedData(ENTIRE_PROPERTY_LAYOUT_TEXT),hotelRates.getPropertyType()));
							}
						}
					});
				} else{
					searchRoomsResponse.setPropertyLayoutTitleText(MessageFormat.format(polyglotService.getTranslatedData(ENTIRE_PROPERTY_LAYOUT_TEXT),hotelRates.getPropertyType()));
				}
			} else {
				searchRoomsResponse.setPropertyLayoutTitleText(polyglotService.getTranslatedData(ROOM_BY_ROOM_PROPERTY_LAYOUT_TEXT));
			}
			if (!requestDetails.isLoggedIn()){
				searchRoomsResponse.setLoginPersuasion(buildLoginPersuasion());
			}

			searchRoomsResponse.setHotelDetails(buildHotelDetails(hotelRates,requestDetails.getFunnelSource(), client, roomDetailsResponse.getLocusData(), expData));

			if (!DEVICE_OS_DESKTOP.equalsIgnoreCase(client) && !CORP_ID_CONTEXT.equalsIgnoreCase(requestDetails.getIdContext()))
				searchRoomsResponse.setBanner(buildBanner(searchRoomsResponse, hotelRates));
			if (commonModifierResponse!=null)
			{
				sortBySellableType(searchRoomsResponse,hotelRates.getPropertyType(),commonModifierResponse.getExpDataMap());
				addSellableLabelFromSellableType(searchRoomsResponse);
			}
			searchRoomsResponse.setHotelPersuasions(hotelRates.getHotelPersuasions());
			// Setting Detail page persuasion for Flyer Deals
//			if (CollectionUtils.isNotEmpty(roomDetailsResponse.getHotelRates())) {
//				Optional<HotelRates> hotelRate = roomDetailsResponse.getHotelRates().stream().findFirst();
//				if (hotelRate.isPresent() && !hotelRate.get().isExclusiveFlyerRateAvailable()) {
//					PrimaryOffer primaryOffer = getPrimaryOfferForFlyerDeal(hotelRates);
//					searchRoomsResponse.setPrimaryOffer(primaryOffer);
//				}
//			}
			if (utility.isB2CFunnel() && hotelRates.getCampaignPojo() != null
					&& StringUtils.isNotBlank(hotelRates.getCampaignPojo().getHeading())) {
				Map<String, TemplateData> templateDataMap = null != searchRoomsResponse.getTemplateData()
						? searchRoomsResponse.getTemplateData() : new HashMap<>();
				addSaleCampaignTemplate(templateDataMap, hotelRates.getCampaignPojo());
				searchRoomsResponse.setTemplateData(templateDataMap);
			}
			searchRoomsResponse.setRoomAdvisory(hotelRates.getRoomAdvisory());

			/*Setting Lucky Data*/
			LuckyData luckyData = new LuckyData();
//			luckyData.setLuckyProperty(hotelRates.isAnyRateLuckyAvailable());
			if(roomDetailsResponse.getLuckyUserContext() != null){
				LuckyUserDetails luckyUserDetails = new LuckyUserDetails();
				luckyUserDetails.setLuckyUserContext(roomDetailsResponse.getLuckyUserContext());
//				if (luckyData.isLuckyProperty()){
//					luckyUserDetails.setCurrent(roomDetailsResponse.getCurrentServerTime());
//					luckyUserDetails.setEnd(roomDetailsResponse.getLuckyUserEndTime());
//				}
				luckyData.setLuckyUserDetails(luckyUserDetails);
			}
			/*Setting Lucky Data V2*/
			LuckyData luckyDataV2 = new LuckyData();
			luckyDataV2.setLuckyProperty(hotelRates.isAnyLuckyCouponAvailable());
			if (luckyDataV2.isLuckyProperty() && roomDetailsResponse.getLuckyCouponStartTime() != null
					&& roomDetailsResponse.getLuckyCouponEndTime() != null){
				luckyDataV2.setLuckyPersuasion(polyglotService.getTranslatedData(GI_LUCKY_TIMER_TEXT));
				LuckyUserDetails luckyUserDetails = new LuckyUserDetails();
				luckyUserDetails.setCurrent(roomDetailsResponse.getLuckyCouponStartTime());
				luckyUserDetails.setEnd(roomDetailsResponse.getLuckyCouponEndTime());
				luckyDataV2.setLuckyUserDetails(luckyUserDetails);
				searchRoomsResponse.setLuckyDataV2(luckyDataV2);
			}
			if(commonModifierResponse != null) {
				searchRoomsResponse.setExpData(commonModifierResponse.getExpDataMap());
				searchRoomsResponse.setVariantKey(commonModifierResponse.getVariantKey());
			}
			searchRoomsResponse.setLuckyData(luckyData);
			removeDuplicateData(searchRoomsResponse);
		} finally {
			metricAspect.addToTimeInternalProcess(Constants.PROCESS_DETAIL_RESPONSE_PROCESS, DETAIL_SEARCH_ROOMS, System.currentTimeMillis() - startTime);
		}
		return searchRoomsResponse;
	}

	private void removeDuplicateData(SearchRoomsResponse searchRoomsResponse) {
		// Sale Campaign Details has been moved and now becomes a part of the cardData.
		// This code block is just for supporting sale campaign in older versions.
		boolean isSaleCampaignCardPresent = false;
		if (CollectionUtils.isNotEmpty(searchRoomsResponse.getCardData())) {
			isSaleCampaignCardPresent = searchRoomsResponse.getCardData().stream()
					.anyMatch(cardData -> null != cardData && null != cardData.getCardInfo()
							&& OfferCard.SALE_CAMPAIGN.name().equalsIgnoreCase(cardData.getCardInfo().getId()));
		}
		if (isSaleCampaignCardPresent && MapUtils.isNotEmpty(searchRoomsResponse.getTemplateData())) {
			searchRoomsResponse.getTemplateData().remove(SALE_CAMPAIGN);
		}
	}

	private List<CardData> setDetailPageCards(HotelRates hotelRates, RoomDetailsResponse roomDetailsResponse,
											  SearchRoomsResponse searchRoomsResponse, Map<String, String> expData) {
		if (hotelRates == null || roomDetailsResponse == null) {
			return null;
		}
		List<CardData> detailPageCards = new ArrayList<>();
		CardData businessIdentificationCard = commonResponseTransformer.buildBusinessIdentificationCards(hotelRates, roomDetailsResponse.getAffiliateId());
		if (businessIdentificationCard != null) {
			detailPageCards.add(businessIdentificationCard);
		}
		if (utility.isExperimentTrue(expData, LOS_BENEFITS_EXP_KEY) || utility.isExperimentTrue(expData, TAJ_GIFT_CARD_EXP_KEY)) {
			List<CardData> offerCards = offerCardUtil.getDetailPageOfferCards(hotelRates, searchRoomsResponse,roomDetailsResponse);
			if (CollectionUtils.isNotEmpty(offerCards)) {
				detailPageCards.addAll(offerCards);
			}
		}
		return detailPageCards;
	}

	private void addSaleCampaignTemplate(Map<String, TemplateData> templateDataMap, CampaignPojo campaignPojo) {
		TemplateData templateData = new TemplateData();
		templateData.setTitleText(campaignPojo.getHeading());
		templateData.setTitleTextColor(campaignPojo.getHeadingColor());
		templateData.setDescription(campaignPojo.getDescription());
		templateData.setIconUrl(campaignPojo.getIconUrl());
		templateData.setBackgroundImage(campaignPojo.getBackgroundImage());
		templateData.setTemplateId(campaignPojo.getId());
		templateData.setTemplateName(campaignPojo.getName());
		templateDataMap.put(SALE_CAMPAIGN, templateData);
	}

	/***
	 * spaceIdToSleepingInfoArrMap contains data which is coming from HES, that will mapped manually to SearchRoomsResponse for each roomCode
	 * @param searchRoomsResponse
	 * @param spaceIdToSleepingInfoArrMap
	 */
	private void mappingSpaceIdToEachRoomCode(SearchRoomsResponse searchRoomsResponse, LinkedHashMap<String, List<SleepingInfoArrangement>> spaceIdToSleepingInfoArrMap) {
		if (CollectionUtils.isNotEmpty(searchRoomsResponse.getExactRooms())) {
			List<RoomDetails> roomDetailsList = searchRoomsResponse.getExactRooms();
			if (CollectionUtils.isNotEmpty(roomDetailsList)) {
				utility.addSleepingInfoArrangementIntoRoomDetails(roomDetailsList,spaceIdToSleepingInfoArrMap);
			}
		}
		if (CollectionUtils.isNotEmpty(searchRoomsResponse.getRecommendedCombos())) {
			List<RoomDetails> roomDetailsList = searchRoomsResponse.getRecommendedCombos().get(0).getRooms();
			if (CollectionUtils.isNotEmpty(roomDetailsList)) {
				utility.addSleepingInfoArrangementIntoRoomDetails(roomDetailsList,spaceIdToSleepingInfoArrMap);
			}
		}
		if (CollectionUtils.isNotEmpty(searchRoomsResponse.getOccupancyRooms())) {
			List<RoomDetails> roomDetailsList = searchRoomsResponse.getOccupancyRooms();
			if (CollectionUtils.isNotEmpty(roomDetailsList)) {
				utility.addSleepingInfoArrangementIntoOccupancyRooms(roomDetailsList);
			}
		}

	}

//	// Primary offer for Detail page -> Flyer Deals
//	private PrimaryOffer getPrimaryOfferForFlyerDeal(HotelRates hotelRates) {
//		PrimaryOffer primaryOffer = null;
//		Style style = null;
//		if (hotelRates != null) {
//			primaryOffer = new PrimaryOffer();
//			style = new Style();
//			primaryOffer.setDescription(polyglotService.getTranslatedData(FLYER_DESCRIPTION));
//			primaryOffer.setIconUrl(flyerPersuasionImageUrlDetail);
//			primaryOffer.setType(FLYER_TEXT);
//			style.setBgColor(flyerPersuasionColorDetail);
//			primaryOffer.setStyle(style);
//		}
//		return primaryOffer;
//	}

	/**
	 * Build day use response for following nodes:
	 * Header price details,
	 * set room list,
	 * set room plan list,
	 * set available as well as missing slot details,
	 * set day use persuasions.
	* */
	public DayUseRoomsResponse convertSearchSlotsResponse(RoomDetailsResponse roomDetailsResponse, HotelsRoomInfoResponseEntity hotelsRoomInfoResponseEntity,
														  HotelImage hotelImage, DayUseRoomsRequest dayUseRoomsRequest, CommonModifierResponse commonModifierResponse) {
		long startTime = System.currentTimeMillis();
		DayUseRoomsResponse dayUseRoomsResponse = new DayUseRoomsResponse();
		try {
			if (CollectionUtils.isEmpty(roomDetailsResponse.getHotelRates())) {
				return dayUseRoomsResponse;
			}
			Map<String, String> expData = dayUseRoomsRequest.getExpDataMap();
			SearchRoomsCriteria searchSlotCriteria = dayUseRoomsRequest.getSearchCriteria();
			com.mmt.hotels.clientgateway.request.FeatureFlags featureFlags = dayUseRoomsRequest.getFeatureFlags();
			RequestDetails requestDetails = dayUseRoomsRequest.getRequestDetails();

			String askedCurrency = (searchSlotCriteria != null && StringUtils.isNotBlank(searchSlotCriteria.getCurrency())) ? searchSlotCriteria.getCurrency() : Constants.DEFAULT_CUR_INR;
			String checkIn = searchSlotCriteria != null ? searchSlotCriteria.getCheckIn() : String.valueOf(0);
			String checkOut = searchSlotCriteria != null ? searchSlotCriteria.getCheckOut() : String.valueOf(0);
			int los = dateUtil.getDaysDiff(LocalDate.parse(checkIn), LocalDate.parse(checkOut));
			int ap = dateUtil.getDaysDiff(LocalDate.now(), LocalDate.parse(checkIn));
//			boolean isBlockPAH = roomDetailsResponse.getExpData() != null && roomDetailsResponse.getExpData().containsKey("blockPAH")
//					&& StringUtils.isNotBlank(roomDetailsResponse.getExpData().get("blockPAH")) && Boolean.parseBoolean(roomDetailsResponse.getExpData().get("blockPAH"));
//GIHTL-15565
			boolean isBlockPAH = false;
			HotelRates hotelRates = roomDetailsResponse.getHotelRates().get(0);
			boolean isLuxeHotel = utility.isLuxeHotel(hotelRates.getCategories());
			boolean isAltAccoHotel = hotelRates.isAltAcco();

			Map<String, String> ratePlanCodeAndNameMap = new HashMap<>();

			//Set price details at header
			if(!(hotelRates.getRoomTypeDetails() != null && hotelRates.getOccupencyLessRoomTypeDetails() == null)){
				dayUseRoomsResponse.setSearchType("R");
				dayUseRoomsResponse.setDefaultPriceKey("DEFAULT");
				//search slot lowest rate plan test for 1 night block.
				if(hotelRates.getLowestRate() != null && !hotelRates.getLowestRate().isSlotRate()) {
					//commonResponseTransformer.getPriceDetail(totalPricing, dayUseRoomsResponse, null, true);
					if(hotelRates.getLowestRate().getAvailDetails() != null && hotelRates.getLowestRate().getAvailDetails().getOccupancyDetails() != null) {
						dayUseRoomsResponse.setOccupancyDetails(buildOccupencyDetails(hotelRates.getLowestRate().getAvailDetails().getOccupancyDetails()));
						dayUseRoomsResponse.setAvailCount(hotelRates.getLowestRate().getAvailDetails().getCount());
					}
					dayUseRoomsResponse.setPriceDetail( new DayUsePriceDetail());
					dayUseRoomsResponse.getPriceDetail().setPriceDisplayMsg(polyglotService.getTranslatedData(DAYUSE_PER_NIGHT));
					dayUseRoomsResponse.getPriceDetail().setTotalPrice(hotelRates.getLowestRate().getDiscountedLowestRate());
					dayUseRoomsResponse.getPriceDetail().setTotalTax(hotelRates.getLowestRate().getTax() != null ? hotelRates.getLowestRate().getTax():0);
					if(hotelRates.getLowestRate().getTax() != null && hotelRates.getLowestRate().getTax() > 0){
						String translatedText = polyglotService.getTranslatedData(DAYUSE_PER_NIGHT_TAX);
						translatedText = MessageFormat.format(translatedText, hotelRates.getLowestRate().getTax());
						dayUseRoomsResponse.getPriceDetail().setPriceTaxMsg(translatedText);
					}
				}
			}

			//Set Rooms, Rate Plans and Slot
			if(CollectionUtils.isNotEmpty(roomDetailsResponse.getDayUseRecommendedRoomTypeDetails())){
				for(RoomTypeDetails roomTypeDetails : roomDetailsResponse.getDayUseRecommendedRoomTypeDetails()){
					getDayUseRoom(hotelRates,roomTypeDetails, hotelsRoomInfoResponseEntity, hotelImage, dayUseRoomsResponse, hotelRates.getListingType(), expData, false, askedCurrency,
							requestDetails.getFunnelSource(), hotelRates.getWalletSurge(), hotelRates.getSegments(), los, hotelRates.getStarRating(), ap, isBlockPAH, hotelRates.getRoomPersuasions(),
							commonModifierResponse, false, ratePlanCodeAndNameMap, isLuxeHotel, isAltAccoHotel , dayUseRoomsRequest, hotelRates.isUserGCCAndMmtExclusive());
				}
			}

			//Populate missing slots
			if(dayUseRoomsRequest.getSearchCriteria() != null && dayUseRoomsRequest.getSearchCriteria().getSlot() != null)
				populateMissingSlots(dayUseRoomsResponse, dayUseRoomsRequest.getSearchCriteria().getSlot().getTimeSlot());

			//Set context details
			dayUseRoomsResponse.setContextDetails(getContextDetails(hotelRates));

			//Set feature flags
			dayUseRoomsResponse.setFeatureFlags(getFeatureFlags(hotelRates, null, null, null));
			dayUseRoomsResponse.getFeatureFlags().setDayUsePersuasion(featureFlags != null && featureFlags.isDayUsePersuasion());

			//Set day use persuasions
			dayUseRoomsResponse.setDayUsePersuasions(buildDayUsePersuasion(roomDetailsResponse.getDayUsePersuasions(), featureFlags, dayUseRoomsRequest.getRequestDetails()));

		} finally {
			metricAspect.addToTimeInternalProcess(Constants.PROCESS_DETAIL_RESPONSE_PROCESS, DETAIL_SEARCH_SLOTS, System.currentTimeMillis() - startTime);
		}
		return dayUseRoomsResponse;
	}

	private RoomTariff buildOccupencyDetails(OccupancyDetails occupancyDetails) {
		RoomTariff roomTariff = null;
		if(occupancyDetails != null){
			roomTariff = new RoomTariff();
			roomTariff.setRoomCount(occupancyDetails.getBedRoomCount());
			roomTariff.setNumberOfAdults(occupancyDetails.getAdult());
			roomTariff.setNumberOfChildren(occupancyDetails.getChild());
		}
		return roomTariff;
	}

	private void populateMissingSlots(DayUseRoomsResponse dayUseRoomsResponse, Integer slotTime) {
		if(dayUseRoomsResponse == null || CollectionUtils.isEmpty(dayUseRoomsResponse.getSlotPlans())){
			return ;
		}
		List<DayUseSlotPlan> dayUseSlotPlanList = dayUseRoomsResponse.getSlotPlans();
		Set<Integer> slotDetailCount = new HashSet<>();
		for(DayUseSlotPlan dayUseSlotPlan : dayUseSlotPlanList){
			if(dayUseSlotPlan.getSlot() != null) {
				slotDetailCount.add(dayUseSlotPlan.getSlot().getDuration());
			}
		}

		if(!slotDetailCount.isEmpty() && slotDetailCount.size() < 3) {
			DayUseSlotPlan dayUseSlotPlan = null;
			com.mmt.hotels.clientgateway.response.dayuse.Slot slot = null;
			Iterator<Integer> itr = missingSlotDetails.getDuration().iterator();
			while(itr.hasNext()) {
				Integer value = itr.next();
				if(!slotDetailCount.contains(value)){
					dayUseSlotPlan = new DayUseSlotPlan();
					slot = new com.mmt.hotels.clientgateway.response.dayuse.Slot();
					slot.setDuration(value);
					com.mmt.hotels.model.response.dayuse.Slot tempSlot = new com.mmt.hotels.model.response.dayuse.Slot();
					tempSlot.setDuration(slot.getDuration());
					tempSlot.setTimeSlot(String.valueOf(slotTime));
					slot.setTimeSlot(utility.calculateTimeSlot_Meridiem(tempSlot));
					dayUseSlotPlan.setSlot(slot);
					dayUseSlotPlanList.add(dayUseSlotPlan);
				}
			}
			dayUseRoomsResponse.setSlotPlans(dayUseSlotPlanList);
		}
	}

	private void getDayUseRoom(HotelRates hotelRates,RoomTypeDetails roomTypeDetails, HotelsRoomInfoResponseEntity hotelsRoomInfoResponseEntity, HotelImage hotelImage, DayUseRoomsResponse dayUseRoomsResponse, String listingType,
							   Map<String, String> expData, boolean ratePlanGroup,
							   String askedCurrency, String funnelSource,
							   WalletSurge walletSurge, com.mmt.hotels.model.response.searchwrapper.Segments segments,
							   int days, Integer hotelStarRating, int ap, boolean isBlockPAH, Map<String, JsonNode> roomPersuasionMap,
							   CommonModifierResponse commonModifierResponse, boolean isPackageRoom, Map<String, String> ratePlanCodeAndNameMap, boolean isLuxeHotel, boolean isAltAccoHotel, DayUseRoomsRequest dayUseRoomsRequest, boolean userGCCAndMmtExclusive) {
		if(roomTypeDetails == null)
			return;

		Map<String, List<String>> roomImageMap = new HashMap<>();
		if (hotelImage != null && hotelImage.getImageDetails() != null && MapUtils.isNotEmpty(hotelImage.getImageDetails().getProfessional())
				&& hotelImage.getImageDetails().getProfessional().containsKey("R")) {
			List<ProfessionalImageEntity> images = hotelImage.getImageDetails().getProfessional().get("R");
			if (CollectionUtils.isNotEmpty(images)) {
				images.forEach( image -> {
					if (StringUtils.isNotBlank(image.getUrl())) {
						roomImageMap.computeIfAbsent(image.getCatCode(), k -> new ArrayList<>());
						roomImageMap.get(image.getCatCode()).add(image.getUrl().startsWith("http") ? image.getUrl() : "https:" + image.getUrl());
					}
				});
			}
		}

		Map<String, RoomInfo> staticRoomInfoMap = new HashMap<>();
		Map<String, DayUseRoom> roomCodeAndRoomMap = new HashMap<>();
		for (String roomCode : roomTypeDetails.getRoomType().keySet()) {
			ExtraGuestDetail extraGuestDetail = null;
			String sellableType = null;
			List<SelectRoomRatePlan> roomRatePlanList = null;
			DayUseRoom dayUseRoom = null;
			if(  MapUtils.isNotEmpty(roomTypeDetails.getRoomType().get(roomCode).getRatePlanList())) {
				RatePlan ratePlan = roomTypeDetails.getRoomType().get(roomCode).getRatePlanList().values().stream().findFirst().orElse(null);
				if(ratePlan!=null && ratePlan.getExtraGuestDetail()!=null){
					extraGuestDetail = ratePlan.getExtraGuestDetail();
				}
			}
			if (null!=hotelsRoomInfoResponseEntity && hotelsRoomInfoResponseEntity.getHtlRmInfo()!=null && CollectionUtils.isNotEmpty(hotelsRoomInfoResponseEntity.getHtlRmInfo()))
				staticRoomInfoMap = hotelsRoomInfoResponseEntity.getHtlRmInfo().get(0).getHotelRoomInfo();
			RoomInfo roomInfo = MapUtils.isNotEmpty(staticRoomInfoMap) ? staticRoomInfoMap.get(roomCode) : null;

			//Set Room List
			dayUseRoom = setDayUseRoomList(roomTypeDetails.getRoomType().get(roomCode), roomImageMap, roomCode, roomInfo, extraGuestDetail, isAltAccoHotel,expData);

			if (roomInfo != null &&  null != roomInfo.getRoomAttributes())
				sellableType = roomInfo.getRoomAttributes().getSellableType();
			if(FUNNEL_DAYUSE.equalsIgnoreCase(funnelSource) && roomTypeDetails.getTotalDisplayFare() != null &&
					roomTypeDetails.getTotalDisplayFare().getDisplayPriceBreakDown() != null &&
					MapUtils.isNotEmpty(roomTypeDetails.getRoomType().get(roomCode).getRatePlanList()) &&
					roomTypeDetails.getRoomType().get(roomCode).getRatePlanList().entrySet().iterator().next().getValue().getDisplayFare() != null &&
					roomTypeDetails.getRoomType().get(roomCode).getRatePlanList().entrySet().iterator().next().getValue().getDisplayFare().getDisplayPriceBreakDown() != null)
			{
				if (roomTypeDetails.getTotalDisplayFare().getDisplayPriceBreakDown().getCouponInfo() != null) {
					roomTypeDetails.getRoomType().get(roomCode).getRatePlanList().entrySet().iterator().next().getValue().getDisplayFare().getDisplayPriceBreakDown().setCouponInfo(roomTypeDetails.getTotalDisplayFare().getDisplayPriceBreakDown().getCouponInfo());
				}
				roomTypeDetails.getRoomType().get(roomCode).getRatePlanList().entrySet().iterator().next().getValue().getDisplayFare().getDisplayPriceBreakDown().setMmtServiceCharge(roomTypeDetails.getTotalDisplayFare().getDisplayPriceBreakDown().getMmtServiceCharge());
			}
			roomRatePlanList = getRatePlans(hotelRates,roomTypeDetails.getRoomType().get(roomCode), listingType,
					expData, ratePlanGroup, askedCurrency, sellableType,funnelSource,
					days, ap , isBlockPAH,commonModifierResponse, isPackageRoom, ratePlanCodeAndNameMap, isLuxeHotel, userGCCAndMmtExclusive, false, null, false,null,null);

			// Populate unique room based on roomCode in roomCodeAndRoomMap
			if (dayUseRoom != null) {
				roomCodeAndRoomMap.putIfAbsent(dayUseRoom.getRoomCode(), dayUseRoom);
			}

			//Set Slot plans and populate occupancyDetails on unique room after getting it from roomCodeAndRoomMap
			setDayUseSlotPlanList(roomRatePlanList, dayUseRoomsResponse, roomTypeDetails, roomCode, roomCodeAndRoomMap.get(roomCode));

			double slashedPrice = dayUseRoomsResponse.getPriceDetail() != null ? dayUseRoomsResponse.getPriceDetail().getTotalPrice() : 0.0;

			if (!dayUseUtil.shouldSetPriceDetailsForDayUseOnDetailPage(dayUseRoomsResponse.getSlotPlans(), slashedPrice)) {
				dayUseRoomsResponse.setPriceDetail(null);
			}

			if (!dayUseUtil.isXPercentRulePassedOnDetailPage(dayUseRoomsResponse.getSlotPlans(), slashedPrice, thresholdForSlashedAndDefaultHourPrice)) {
				dayUseRoomsResponse.setPriceDetail(null);
			}

			//TODO get(0) or ALL to set roomplans
			if(CollectionUtils.isEmpty(dayUseRoomsResponse.getRatePlans()) && CollectionUtils.isNotEmpty(roomRatePlanList) && roomRatePlanList.get(0) != null){
				dayUseRoomsResponse.setRatePlans(new ArrayList<>());
				dayUseRoomsResponse.getRatePlans().add(roomRatePlanList.get(0));
			}
			else if(CollectionUtils.isNotEmpty(roomRatePlanList) && roomRatePlanList.get(0) != null){
				dayUseRoomsResponse.getRatePlans().add(roomRatePlanList.get(0));
			}
		}

		//Set Room List
		if (MapUtils.isNotEmpty(roomCodeAndRoomMap) && CollectionUtils.isNotEmpty(roomCodeAndRoomMap.values())) {
			dayUseRoomsResponse.setRooms(new ArrayList<>(roomCodeAndRoomMap.values()));
		}

	}

	private DayUseRoom setDayUseRoomList(RoomType roomType, Map<String, List<String>> roomImageMap, String roomCode, RoomInfo roomInfo, ExtraGuestDetail extraGuestDetail, boolean isAltAccoHotel,Map<String, String> expData) {
		if(roomType == null || roomInfo == null){
			return null;
		}
		DayUseRoom dayUseRoom = new DayUseRoom();
		dayUseRoom.setRoomCode(roomType.getRoomTypeCode());
		dayUseRoom.setRoomName(roomType.getRoomTypeName());
		dayUseRoom.setAmenities(commonResponseTransformer.buildAmenities(roomInfo.getFacilityWithGrp(), roomInfo.getStarFacilities()));
		boolean  pilgrimageBedInfoEnable = utility.isExperimentOn(expData, ExperimentKeys.PILGR_IMAGE_BED_INFO.getKey());
		dayUseRoom.setRoomHighlights(getRoomHighlights(roomInfo, extraGuestDetail, isAltAccoHotel,pilgrimageBedInfoEnable));
		dayUseRoom.setImages(MapUtils.isNotEmpty(roomImageMap) ? roomImageMap.get(roomCode) : null);
		return dayUseRoom;
	}

	private void setDayUseSlotPlanList(List<SelectRoomRatePlan> tempRoomRates, DayUseRoomsResponse dayUseRoomsResponse, RoomTypeDetails roomTypeDetails, String roomCode, DayUseRoom dayUseRoom) {
		List<DayUseSlotPlan> dayUseSlotPlanList = new ArrayList<>();
		String priceKey = null;
		if(CollectionUtils.isNotEmpty(dayUseRoomsResponse.getSlotPlans()))
			dayUseSlotPlanList.addAll(dayUseRoomsResponse.getSlotPlans());
		if(CollectionUtils.isNotEmpty(tempRoomRates) && tempRoomRates.get(0) != null){
			//Set Slot
			DayUseSlotPlan slotPlan = new DayUseSlotPlan();
			if(tempRoomRates.get(0).getPayMode() != null){
				slotPlan.setPayMode(tempRoomRates.get(0).getPayMode());
			}
			if(CollectionUtils.isNotEmpty(tempRoomRates.get(0).getTariffs()) && tempRoomRates.get(0).getTariffs().get(0) != null){
				if(tempRoomRates.get(0).getTariffs().get(0).getDefaultPriceKey() != null){
					slotPlan.setDefaultPriceKey(tempRoomRates.get(0).getTariffs().get(0).getDefaultPriceKey());
				}
				if(MapUtils.isNotEmpty(tempRoomRates.get(0).getTariffs().get(0).getPriceMap()) && tempRoomRates.get(0).getTariffs().get(0).getPriceMap().get(slotPlan.getDefaultPriceKey()) != null){
					TotalPricing totalPricing = tempRoomRates.get(0).getTariffs().get(0).getPriceMap().get(slotPlan.getDefaultPriceKey());
					commonResponseTransformer.getPriceDetail(totalPricing, dayUseRoomsResponse, slotPlan, false);
					priceKey = totalPricing.getPricingKey();
				}
			}
			if(roomTypeDetails.getSlot() != null) {
				Slot slotHES = roomTypeDetails.getSlot();
				com.mmt.hotels.clientgateway.response.dayuse.Slot slotCG = new com.mmt.hotels.clientgateway.response.dayuse.Slot();
				slotCG.setDuration(slotHES.getDuration());
				slotCG.setTimeSlot(utility.calculateTimeSlot_Meridiem(roomTypeDetails.getSlot()));
				slotPlan.setSlot(slotCG);
			}

			List<DayUseRoomCriteria> roomCriteriaList = new ArrayList<>();
			SelectRoomRatePlan roomRatePlan = tempRoomRates.get(0);
			if(roomRatePlan != null && CollectionUtils.isNotEmpty(roomRatePlan.getTariffs())){
				DayUseRoomCriteria roomCriteria = new DayUseRoomCriteria();
				roomCriteria.setRatePlanCode(roomRatePlan.getTariffs().get(0).getTariffCode());
				roomCriteria.setMtKey(roomRatePlan.getTariffs().get(0).getMtKey());
				roomCriteria.setRoomCode(roomCode);
				roomCriteria.setSupplierCode(roomRatePlan.getSupplierCode());
				roomCriteria.setPricingKey(priceKey);
				List<DayUseRoomStayCandidate> dayUseRoomStayCandidateList = new ArrayList<>();
				if(CollectionUtils.isNotEmpty(roomRatePlan.getTariffs()) && roomRatePlan.getTariffs().get(0) != null){
					if(CollectionUtils.isNotEmpty(roomRatePlan.getTariffs().get(0).getRoomTariffs())){
						for(RoomTariff roomTariff : roomRatePlan.getTariffs().get(0).getRoomTariffs()) {
							DayUseRoomStayCandidate dayUseRoomStayCandidate = new DayUseRoomStayCandidate();
							dayUseRoomStayCandidate.setAdultCount(roomTariff.getNumberOfAdults());
							dayUseRoomStayCandidate.setChildAges(roomTariff.getChildAges());
							dayUseRoomStayCandidateList.add(dayUseRoomStayCandidate);
						}
					}
				}
				roomCriteria.setRoomStayCandidates(dayUseRoomStayCandidateList);
				roomCriteriaList.add(roomCriteria);
				populateOccupancyDetailsInDayuseRoom(dayUseRoom, roomRatePlan.getTariffs().get(0).getOccupancydetails());
			}
			slotPlan.setRoomCriteria(roomCriteriaList);
			dayUseSlotPlanList.add(slotPlan);
		}
		dayUseRoomsResponse.setSlotPlans(dayUseSlotPlanList);
	}

	private void populateOccupancyDetailsInDayuseRoom(DayUseRoom dayUseRoom, RoomTariff occupancydetails) {
		if (dayUseRoom != null && occupancydetails != null) {
			dayUseRoom.setOccupancydetails(occupancydetails);
		}
	}

	private List<DayUsePersuasion> buildDayUsePersuasion(List<com.mmt.hotels.model.response.dayuse.DayUsePersuasion> dayUsePersuasions,
														 com.mmt.hotels.clientgateway.request.FeatureFlags featureFlags, RequestDetails requestDetails) {
		List<DayUsePersuasion> dayUsePersuasionList = null;
		if(featureFlags != null && featureFlags.isDayUsePersuasion() && requestDetails != null && FUNNEL_DAYUSE.equalsIgnoreCase(requestDetails.getFunnelSource())){
			dayUsePersuasionList = new ArrayList<>();
			//utility.transformLateCheckout(dayUseFunnelPersuasions,dayUsePersuasionList);
			if (CollectionUtils.isNotEmpty(dayUsePersuasions)) {
				for (com.mmt.hotels.model.response.dayuse.DayUsePersuasion dayUsePersuasionHES : dayUsePersuasions) {
					DayUsePersuasion dayUsePersuasionCG = new DayUsePersuasion();
					dayUsePersuasionCG.setId(dayUsePersuasionHES.getId());
					dayUsePersuasionCG.setText(dayUsePersuasionHES.getText());
					dayUsePersuasionCG.setIcon(dayUsePersuasionHES.getIcon());
					dayUsePersuasionCG.setIconType(dayUsePersuasionHES.getIconType());
					dayUsePersuasionList.add(dayUsePersuasionCG);
				}
			}
		}
		return dayUsePersuasionList;
	}

	private Card buildPaymentCard(RequestDetails requestDetails, HotelRates hotelRates) {
		if (requestDetails != null && utility.isGroupBookingFunnel(requestDetails.getFunnelSource()) && hotelRates.getRecommendedRoomTypeDetails() != null
				&& hotelRates.getRecommendedRoomTypeDetails().getPaymentPlan() != null && StringUtils.isNotBlank(hotelRates.getRecommendedRoomTypeDetails().getPaymentPlan().getPaymentCardText())) {
			Card paymentCard = new Card();
			paymentCard.setTitle(hotelRates.getRecommendedRoomTypeDetails().getPaymentPlan().getPaymentCardText());
			paymentCard.setCta(polyglotService.getTranslatedData(KNOW_MORE));
			paymentCard.setIconUrl("https://promos.makemytrip.com/Hotels_product/group/ic_rupee_2x.png");
			return paymentCard;
		}
		return null;
	}



	/**
	 * This Function is used to add SellableLabel into SearchResponse on the basis of sellableType("Bed","Room") present in roomDetails
	 * applied into OccupancyRooms and ExactRooms
	 * This is part of A/B Experiment and applicable to propertyType "Hostel"
	 **/
	public Pair<Boolean, Boolean> addSellableLabelFromSellableType(SearchRoomsResponse searchRoomsResponse) {
		if (searchRoomsResponse == null) {
			return new ImmutablePair<>(false, false);
		}
		try {
			boolean bedAvailable = false, roomAvailable = false;
			Pair<Boolean, Boolean> isBedAndRoomAvailable = null;
			if (CollectionUtils.isNotEmpty(searchRoomsResponse.getRecommendedCombos())) {
				for (RecommendedCombo recommendedCombo : searchRoomsResponse.getRecommendedCombos()) {
					if (recommendedCombo != null) {
						isBedAndRoomAvailable = addSellableLabelAndRoomTypeFilterCode(recommendedCombo.getRooms());
						bedAvailable = bedAvailable || isBedAndRoomAvailable.getKey();
						roomAvailable = roomAvailable || isBedAndRoomAvailable.getValue();
					}
				}
			}
			isBedAndRoomAvailable = addSellableLabelAndRoomTypeFilterCode(searchRoomsResponse.getOccupancyRooms());
			bedAvailable = bedAvailable || isBedAndRoomAvailable.getKey();
			roomAvailable = roomAvailable || isBedAndRoomAvailable.getValue();
			isBedAndRoomAvailable = addSellableLabelAndRoomTypeFilterCode(searchRoomsResponse.getExactRooms());
			bedAvailable = bedAvailable || isBedAndRoomAvailable.getKey();
			roomAvailable = roomAvailable || isBedAndRoomAvailable.getValue();
			return new ImmutablePair<>(bedAvailable, roomAvailable);
		} catch (Exception ex) {
			LOGGER.error("Error while adding sellableLabel in Occupancy and exact Rooms", ex);
		}
		return new ImmutablePair<>(false, false);

	}


	/***
	 * This function add SellableLabel and RoomTypeFilterCode for each roomCode
	 * @param rooms
	 * @return boolean true/false basis of {sellableTypeBed,sellableTypeRoom} is available for list of roomDetails.
	 */
	private Pair<Boolean, Boolean> addSellableLabelAndRoomTypeFilterCode(List<RoomDetails> rooms) {
		boolean sellableTypeBed = false, sellableTypeRoom = false;
		if (CollectionUtils.isNotEmpty(rooms))
			for (RoomDetails room : rooms) {
				List<String> filterCode = new ArrayList<>();
				String sellableType = (CollectionUtils.isNotEmpty(room.getRatePlans()) ? room.getRatePlans().get(0).getSellableType() : null);
				if (Constants.SELLABLE_BED_TYPE.equalsIgnoreCase(sellableType)) {
					room.setSellableLabel(polyglotService.getTranslatedData(ConstantsTranslation.BEDS_SELLABLE_LABEL));
					filterCode.add(BEDS_SELLABLE_TYPE_FILTER_CODE);
					sellableTypeBed = true;
				} else if (Constants.SELLABLE_ROOM_TYPE.equalsIgnoreCase(sellableType)) {
					room.setSellableLabel(polyglotService.getTranslatedData(ConstantsTranslation.ROOMS_SELLABLE_LABEL));
					filterCode.add(ROOMS_SELLABLE_TYPE_FILTER_CODE);
					//Add private room filter code
					List<SelectRoomRatePlan> roomWiseRatePlans = room.getRatePlans();
					if (CollectionUtils.isNotEmpty(roomWiseRatePlans)) {
						roomWiseRatePlans.stream()
								.filter(Objects::nonNull)
								.forEach(ratePlan -> {
									if(ratePlan.getFilterCode() == null) {
										ratePlan.setFilterCode(new ArrayList<>());
									}
									if(!ratePlan.getFilterCode().contains(ROOMS_SELLABLE_TYPE_FILTER_CODE)) {
										ratePlan.getFilterCode().add(ROOMS_SELLABLE_TYPE_FILTER_CODE);
									}
								});
					}
					sellableTypeRoom = true;
				}
				room.setFilterCode(filterCode);
			}
		return new ImmutablePair<>(sellableTypeBed, sellableTypeRoom);
	}




	private void setSellableLabel(List<RoomDetails> rooms) {
		if(CollectionUtils.isNotEmpty(rooms))
			for (RoomDetails room : rooms) {
				String sellableType = (CollectionUtils.isNotEmpty(room.getRatePlans()) ? room.getRatePlans().get(0).getSellableType() : null);
				if (Constants.SELLABLE_BED_TYPE.equalsIgnoreCase(sellableType))
					room.setSellableLabel(polyglotService.getTranslatedData(ConstantsTranslation.BEDS_SELLABLE_LABEL));
				else if (Constants.SELLABLE_ROOM_TYPE.equalsIgnoreCase(sellableType))
					room.setSellableLabel(polyglotService.getTranslatedData(ConstantsTranslation.ROOMS_SELLABLE_LABEL));
			}
	}

	/**
	 * This function is used  sort the SearchResponse on the basis of sellableType and ratePlans
	 * This is part of A/B Experiment and applicable to propertyType "Hostel"
	 * SORTING LOGIC -> Sort Firstly on the Basis of SellableType i.e., beds and rooms having bed comes first
	 * And if both having same sellableType example{bed} then sort on the basis of price available in ratePlans[0]
	 **/
	public void sortBySellableType(SearchRoomsResponse searchRoomsResponse, String propertyType,Map<String,String>expDataMap) {
		try
		{
			/*CLEAN UP PART OF 15565*/
			if (MapUtils.isNotEmpty(expDataMap) && Constants.PROPERTY_TYPE_HOSTEL.equalsIgnoreCase(propertyType)) {
				sortRooms(searchRoomsResponse.getOccupancyRooms());
				sortRooms(searchRoomsResponse.getExactRooms());
			}

		}
		catch (Exception ex) {
			LOGGER.error("error while sorting SearchResponse on the basis of sellableType", ex);
		}


	}

	private void sortRooms(List<RoomDetails> rooms)
	{
		if(CollectionUtils.isNotEmpty(rooms))
			rooms.sort((room1, room2) -> {
				if (isSellableTypePresent(room1) && isSellableTypePresent(room2) && !room1.getRatePlans().get(0).getSellableType().equalsIgnoreCase(room2.getRatePlans().get(0).getSellableType()))
					return (room1.getRatePlans().get(0).getSellableType()).compareTo(room2.getRatePlans().get(0).getSellableType());
				return getAmountFromLabel(room1, Constants.TOTAL_AMOUNT_KEY) - getAmountFromLabel(room2, Constants.TOTAL_AMOUNT_KEY);
			});
	}

	private boolean isSellableTypePresent(RoomDetails room)
	{
		return CollectionUtils.isNotEmpty(room.getRatePlans()) && StringUtils.isNotBlank(room.getRatePlans().get(0).getSellableType());
	}

	private int getAmountFromLabel(RoomDetails roomDetails, String amountLabelKey) {
		try {
			String defaultPriceKey = roomDetails.getRatePlans().get(0).getTariffs().get(0).getDefaultPriceKey();
			return (int) roomDetails.getRatePlans().get(0).getTariffs().get(0).getPriceMap()
					.get(defaultPriceKey).getDetails().stream().filter(e -> amountLabelKey.equalsIgnoreCase(e.getKey())).findFirst().get().getAmount();
		} catch (Exception ex) {
			LOGGER.error("error while sorting SearchResponse on the basis of sellableType", ex);
		}
		return 0;
	}

	private HotelDetails buildHotelDetails(HotelRates hotelRates, String funnelSrc, String client, LocusData locusData, Map<String, String> expDataMap) {

		if(hotelRates!=null) {
			HotelDetails hotelDetails = new HotelDetails();
			if (!Constants.CLIENT_DESKTOP.equalsIgnoreCase(client)) {
				hotelDetails.setHotelName(hotelRates.getName());
				hotelDetails.setHotelIcon(hotelRates.getHotelIcon());
				String detailDeepLinkUrl = hotelRates.getDetailDeeplinkUrl();

				if(detailDeepLinkUrl!=null){

					String[] parts = detailDeepLinkUrl.split(Constants.URL_PARAM_BASE_SPLITTER);
					if (parts.length == 2) {
						String base = parts[0];
						String params = parts[1];

						detailDeepLinkUrl =  base + Constants.QUE_MARK + Stream.of(params.split(Constants.AMP))
								.map(p -> p.split(Constants.EQUI))
								.filter(p -> !p[0].equals(Constants.CHECK_AVAILBILITY_PARAM))
								.map(p -> String.join(Constants.EQUI, p))
								.collect(Collectors.joining(Constants.AMP));

						if(Constants.FUNNEL_SOURCE_HOMESTAY.equalsIgnoreCase(funnelSrc)){
							detailDeepLinkUrl+= Constants.AMP+ Constants.FUNNEL_SOURCE_HOMESTAY.toLowerCase()+  Constants.EQUI + "true";
						}
					}
				}
				hotelDetails.setUrl(detailDeepLinkUrl);
			}
			hotelDetails.setHotelId(hotelRates.getId());
			hotelDetails.setSupplierId("v15");
			hotelDetails.setVoyagerCityId(hotelRates.getVoyagerCityId());
			hotelDetails.setLmr(hotelRates.isLmr());
			hotelDetails.setMergedPropType(hotelRates.getPropertyTypeMerged());
			if (locusData != null){
				LocusData locusDataRes = new LocusData();
				locusDataRes.setLocusId(locusData.getLocusId());
				locusDataRes.setLocusType(locusData.getLocusType());
				locusDataRes.setLocusName(locusData.getLocusName());
				hotelDetails.setLocusData(locusDataRes);
			}
			hotelDetails.setCountryCode(hotelRates.getCountryCode());
			if (utility.isSPKGExperimentOn(expDataMap) && checkForPackageRoomRatePlan(hotelRates)) {
				hotelDetails.setPackageTagUrl(elitePackageIconUrl);
			}
			hotelDetails.setEntireProperty(LISTING_TYPE_ENTIRE.equalsIgnoreCase(hotelRates.getListingType()));
			hotelDetails.setAltAcco(hotelRates.isAltAcco());
			return hotelDetails;
		}
		return null;
	}

	boolean checkForPackageRoomRatePlan(HotelRates hotelRates) {
		if (hotelRates.getPackageRoomDetails() != null)
			return true;
		if (hotelRates.getOccupencyLessRoomTypeDetails() != null) {
			Map<String, RoomType> roomTypeMap = hotelRates.getOccupencyLessRoomTypeDetails().getRoomType();
			if (MapUtils.isNotEmpty(roomTypeMap)) {
				for (RoomType roomType : roomTypeMap.values()) {
					if (MapUtils.isNotEmpty(roomType.getRatePlanList())) {
						for (RatePlan ratePlan : roomType.getRatePlanList().values()) {
							if (ratePlan.getPackageRoomRatePlan()) {
								return true;
							}
						}
					}
				}
			}
		}
		return false;
	}
	public int getTotalSearchedPaxCount(List<RoomStayCandidate> roomStayCandidates) {
		if(roomStayCandidates == null)
			return 0;
		int totalSearchedPaxCount = 0;
		if(CollectionUtils.isNotEmpty(roomStayCandidates)) {
			for (RoomStayCandidate roomStayCandidate : roomStayCandidates) {
				totalSearchedPaxCount += roomStayCandidate.getAdultCount() + (roomStayCandidate.getChildAges() == null ? 0 : roomStayCandidate.getChildAges().size());
			}
		}
		return totalSearchedPaxCount;
	}

	protected abstract LoginPersuasion buildLoginPersuasion();

	protected abstract void buildLoyaltyCashbackPersuasions(BestCoupon coupon, Map<String, PersuasionResponse> persuasionMap);


	private ExtraGuestDetailPersuasion buildExtraGuestDetailPersuasion(RoomTypeDetails roomTypeDetails, boolean isAltAccoHotel){

		if(roomTypeDetails!= null &&  MapUtils.isNotEmpty(roomTypeDetails.getRoomType()) && !isAltAccoHotel) {
			RoomType roomType = roomTypeDetails.getRoomType().values().stream().findFirst().orElse(null);
			if (roomType != null && MapUtils.isNotEmpty(roomType.getRatePlanList())) {
				RatePlan ratePlan = roomType.getRatePlanList().values().stream().findFirst().orElse(null);
				if (ratePlan != null && ratePlan.getExtraGuestDetail() != null && StringUtils.isNotEmpty(ratePlan.getExtraGuestDetail().getHotelDetailExtraBedText())) {
					ExtraGuestDetailPersuasion extraGuestDetailPersuasion = new ExtraGuestDetailPersuasion();
					extraGuestDetailPersuasion.setIconUrl(polyglotService.getTranslatedData(DEAL_ICON_URL));
					String translatedText = polyglotService.getTranslatedData(EXTRA_GUEST_DEAL_TEXT_STYLE);
					if(StringUtils.isNotEmpty(translatedText)) {
						translatedText = MessageFormat.format(translatedText.toString(), ratePlan.getExtraGuestDetail().getHotelDetailExtraBedText());
						extraGuestDetailPersuasion.setText(translatedText);
						return extraGuestDetailPersuasion;
					}
				}
			}
		}
			return null;

	}
	private RtbPersuasionCard buildRtbPersuasionCard(HotelRates hotelRates) {
		RtbPersuasionCard rtbPersuasionCard;
		if (hotelRates.isRequestToBook() && hotelRates.isRTBRatePlanPreApproved()) {
			rtbPersuasionCard = new RtbPersuasionCard();
			rtbPersuasionCard.setTitle(polyglotService.getTranslatedData(RTB_PRE_APPROVED_TEXT));
			rtbPersuasionCard.setIconText(polyglotService.getTranslatedData(COMPLETE_YOUR_BOOKING));
			rtbPersuasionCard.setIconUrl(rtbCardConfigs.get("rtbPreApprovedIcon"));
			rtbPersuasionCard.setIsPreApproved(true);
			if (hotelRates.getRtbPreApprovedCard() != null) {
				rtbPersuasionCard.setApprovedOn(hotelRates.getRtbPreApprovedCard().getApprovedOn());
				rtbPersuasionCard.setExpiry(hotelRates.getRtbPreApprovedCard().getExpiry());
				rtbPersuasionCard.setIconDeepLink(hotelRates.getRtbPreApprovedCard().getReviewDeeplink());
			}
			return rtbPersuasionCard;
		}
		if (hotelRates.isRequestToBook() && !hotelRates.isRtbPreApproved()) {
			rtbPersuasionCard = new RtbPersuasionCard();
			rtbPersuasionCard.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.RTB_TITLE));
			rtbPersuasionCard.setText(polyglotService.getTranslatedData(ConstantsTranslation.RTB_CARD_TEXT));
			rtbPersuasionCard.setIconUrl(rtbCardConfigs.get("rtbCardIcon"));
			rtbPersuasionCard.setIconDeepLink(rtbCardConfigs.get("rtbDeepLink"));
			rtbPersuasionCard.setIconText(polyglotService.getTranslatedData(KNOW_MORE));
			rtbPersuasionCard.setRtbCard(buildRtbCard());
			return rtbPersuasionCard;
		}
		return null;
	}

	private SpaceData getSpaceData(com.mmt.hotels.model.response.staticdata.SpaceData hesSpaceData, RoomType roomType, Map<String, String> expData){
		if(hesSpaceData == null)
			return null;
		SpaceData cgSpaceData = new SpaceData();
		int searchedPaxCount = getSearchedPaxedCount(roomType);
		int extraBedCount = 0, totalBaseOccupancy = 0;
		if(hesSpaceData!=null){
			cgSpaceData.setDescriptive(hesSpaceData.getDescriptive());
			cgSpaceData.setSharedInfo(buildSharedInfo(hesSpaceData.getSharedInfo()));
			List<Space> spaceList = new ArrayList<>();
			for(com.mmt.hotels.model.response.staticdata.Space hesSpace: hesSpaceData.getSpaces()) {
				if(searchedPaxCount > 0 && hesSpace.getSpaceType() !=null && (hesSpace.getSpaceType().equalsIgnoreCase(Constants.BEDROOM) || hesSpace.getSpaceType().equalsIgnoreCase(Constants.LIVING_ROOM))) {
					hesSpace.setFinalOccupancy(min(hesSpace.getBaseOccupancy(), searchedPaxCount));
					searchedPaxCount -= hesSpace.getFinalOccupancy();
				}
			}
			for(com.mmt.hotels.model.response.staticdata.Space hesSpace: hesSpaceData.getSpaces()){
				Space cgSpace = new Space();
				cgSpace.setAreaText(hesSpace.getAreaText());
				cgSpace.setDescriptionText((hesSpace.getSleepingDetails()!=null && hesSpace.getSleepingDetails().getExtraBedInfo()!=null) ? appendExtraBedInfo(hesSpace.getDescriptionText(),hesSpace.getSleepingDetails().getExtraBedInfo()) : hesSpace.getDescriptionText());
				cgSpace.setName(hesSpace.getName());
				String subText = hesSpace.getSubText();
				cgSpace.setSpaceId(hesSpace.getSpaceId());
				cgSpace.setSpaceType(hesSpace.getSpaceType());
				if(hesSpace.getSpaceType() != null && (hesSpace.getSpaceType().equalsIgnoreCase(Constants.BEDROOM) || hesSpace.getSpaceType().equalsIgnoreCase(Constants.LIVING_ROOM))) {
					if (searchedPaxCount > 0) {
						int diff = min(hesSpace.getMaxOccupancy() - hesSpace.getFinalOccupancy(), searchedPaxCount);
						hesSpace.setFinalOccupancy(hesSpace.getFinalOccupancy() + diff);
						searchedPaxCount -= diff;
						extraBedCount += max(0, hesSpace.getFinalOccupancy() - hesSpace.getBaseOccupancy());
					}
					int finalOccupancy = hesSpace.getFinalOccupancy();
					int occupancy = max(finalOccupancy, hesSpace.getBaseOccupancy());
					if (occupancy > 0)
						subText = (occupancy > 1) ? polyglotService.getTranslatedData(ConstantsTranslation.SPACE_OCCUPANCY_TEXT) : polyglotService.getTranslatedData(ConstantsTranslation.SPACE_SINGLE_OCCUPANCY_TEXT);
					else
						subText = null;
					if (subText != null) {
						subText = subText.replace("{occupancy}", String.valueOf(occupancy));
					}
					totalBaseOccupancy += hesSpace.getBaseOccupancy();
				}
				cgSpace.setSubText(subText);
				cgSpace.setOpenCardText(buildOpenCardText(hesSpace, expData));
				if(CollectionUtils.isNotEmpty(hesSpace.getMedia())){
					List<MediaData> mediaDataList = new ArrayList<>();
					for(com.mmt.hotels.model.response.staticdata.MediaData hesMediaData: hesSpace.getMedia()){
						MediaData cgMediaData = new MediaData();
						cgMediaData.setMediaType(hesMediaData.getMediaType());
						cgMediaData.setUrl(hesMediaData.getUrl());
						mediaDataList.add(cgMediaData);
					}
					cgSpace.setMedia(mediaDataList);
				}
				spaceList.add(cgSpace);
			}
			cgSpaceData.setSpaces(spaceList);
		}
		cgSpaceData.setBaseGuests(totalBaseOccupancy);
		cgSpaceData.setExtraBeds(extraBedCount);
		return cgSpaceData;
	}

	private String appendExtraBedInfo(String description, List<SleepingBedInfo> extraBedInfo) {
		StringBuilder descriptionText = new StringBuilder();
		SleepingBedInfo mattressBedInfo = extraBedInfo.stream().filter(bedInfo -> bedInfo.getBedType().
				equalsIgnoreCase(Constants.MATTRESS)).findFirst().orElse(null);
		if(mattressBedInfo != null){
			descriptionText.append(EXTRA).append(SPACE).append(mattressBedInfo.getBedCount()).append(SPACE).append(mattressBedInfo.getBedType()).append(SPACE).append(AVAILABLE.toLowerCase())
					.append(COMMA_SPACE).append(description);
		} else {
			descriptionText.append(description);
		}
		return descriptionText.toString();

	}

	/**
	 * * The node is added to show the share property icon and url in share property section  layout, for reference (HTL-37205)
	 * @param hesSharedInfo
	 * @return
	 */
	private SharedInfo buildSharedInfo(com.mmt.hotels.model.response.staticdata.SharedInfo hesSharedInfo) {
		if(hesSharedInfo == null)
			return null;
		SharedInfo sharedInfo = new SharedInfo();
		sharedInfo.setIconUrl(hesSharedInfo.getIconUrl());
		sharedInfo.setInfoText(hesSharedInfo.getInfoText());
		return  sharedInfo;
	}


	private String buildOpenCardText(com.mmt.hotels.model.response.staticdata.Space hesSpace, Map<String, String> expData) {
		//GIHTL-15565 : CLEAN UP plcnew exp
//		if(MapUtils.isNotEmpty(expData) && expData.get("plcnew") != null && StringUtils.equalsIgnoreCase(expData.get("plcnew"),"true")) {
//			StringBuilder openCardText = new StringBuilder(hesSpace.getDescriptionText());
//			if(StringUtils.isNotBlank(hesSpace.getOpenCardText())) {
//				 openCardText.append(BREAK_AMENITIES_BOLD).append(hesSpace.getOpenCardText());
//			}
//			return openCardText.toString();
//		}
		return hesSpace.getOpenCardText();
	}

	private int getSearchedPaxedCount(RoomType roomType) {
		if(roomType != null) {
			Optional<String> rateplanCode = roomType.getRatePlanList().keySet().stream().findFirst();
			if(rateplanCode.isPresent()) {
				String ratePlanCodeStr = rateplanCode.get();
				if(roomType.getRatePlanList().get(ratePlanCodeStr).getAvailDetails() != null && roomType.getRatePlanList().get(ratePlanCodeStr).getAvailDetails().getOccupancyDetails() != null) {
					double totalSearchedPax = roomType.getRatePlanList().get(ratePlanCodeStr).getAvailDetails().getOccupancyDetails().getAdult() +
							roomType.getRatePlanList().get(ratePlanCodeStr).getAvailDetails().getOccupancyDetails().getChild();
					double noOfRooms = roomType.getRatePlanList().get(ratePlanCodeStr).getAvailDetails().getOccupancyDetails().getNumOfRooms();
					return (int) ceil(totalSearchedPax / noOfRooms);
				}
			}
		}
		return 0;
	}

	protected AllInclusiveCard buildAllInclusiveCard(HotelRates hotelRates){
		AllInclusiveCard card=null;
		try {
			 card= new AllInclusiveCard();
			card.setData(allInclusiveCard.getData());
			card.setDesc(allInclusiveCard.getDesc());
			card.setTitle(allInclusiveCard.getTitle());
			card.setPersuasionText(allInclusiveCard.getPersuasionText());
			card.setImageUrl(allInclusiveCard.getImageUrl());
			card.setRatePlanCode(hotelRates.getAllInclusiveRpc());
			card.setRoomCode(hotelRates.getAllInclusiveRoomCode());
			if (!hotelRates.isLowestRateAllInclusive()) {
				card.setAmount(hotelRates.getRoomTypeDetails().getRoomType().get(hotelRates.getAllInclusiveRoomCode())
						.getRatePlanList().get(hotelRates.getAllInclusiveRpc()).getDisplayFare().getDisplayPriceBreakDown().getDisplayPrice());
			}
		}catch(Exception e){
			LOGGER.error("Exception while getting allinclusivecard",e);
		}
		return card;
	}

	private List<RtbCard> buildRtbCard() {
		List<RtbCard> rtbCards = new ArrayList<>();
		int index = 1;
		while (index < 3) {
			RtbCard rtbCard = new RtbCard();
			rtbCard.setTitle(polyglotService.getTranslatedData(RTB_CARD_TITLE + index));
			rtbCard.setSubTitle(polyglotService.getTranslatedData(RTB_CARD_DESC + index));
			rtbCard.setIconUrl(rtbCardConfigs.get("rtbTickIcon"));
			rtbCards.add(rtbCard);
			index++;
		}
		return rtbCards;
	}

	private SleepingArrangementRoomInfo buildRoomInfo(HotelRates hotelRates, SearchRoomsResponse searchRoomsResponse, boolean isOHSExpEnable, Pair<Boolean, Boolean> bedAndRoomPresent, boolean isNewDetailPageTrue) {
		if (hotelRates == null) {
			return null;
		}
		SleepingArrangementRoomInfo roomInfo = new SleepingArrangementRoomInfo();
		if (isOHSExpEnable) {
			roomInfo.setTitle(polyglotService.getTranslatedData(HOSTEL_TITLE));
		} else {
			if (StringUtils.isNotEmpty(hotelRates.getStayTypeWithSizeBed()))
				roomInfo.setTitle(hotelRates.getStayTypeWithSizeBed());
			else
				roomInfo.setTitle(hotelRates.getStayTypeWithSize());
		}
		buildStayDetails(searchRoomsResponse, roomInfo, hotelRates.getSellableCombo(), hotelRates.getPropertyType(),hotelRates);
//		buildBedInfoText(searchRoomsResponse, roomInfo); // This is of No use, As we are buildingBedInfoText in Utility.applyRoundRobinOnGuestCountAndBedCount
		if (hotelRates.getLowestRate() != null && hotelRates.getLowestRate().getAvailDetails() != null) {
			Map<String, Integer> roomBedCount = new HashMap<>();
			roomBedCount.put(Constants.SELLABLE_ROOM_TYPE, hotelRates.getLowestRate().getAvailDetails().getNumOfRooms());
			roomBedCount.put(Constants.SELLABLE_BED_TYPE, hotelRates.getLowestRate().getAvailDetails().getOccupancyDetails().getBedCount());
			Tuple<String, String> guestRoomKeyValue = utility.getGuestRoomKeyValue(roomBedCount, hotelRates.getPropertyType(), hotelRates.getListingType());
			roomInfo.setGuestRoomKey(guestRoomKeyValue.getX());
			roomInfo.setGuestRoomValue(guestRoomKeyValue.getY());
		}
		String propertyInfoText = roomInfo.getTitle();
		try {
			// TODO : this hack is temporary, would be removed once we start getting property size from hotstore with space details
			roomInfo.setPropertyInfoText("");
			if(StringUtils.isNotBlank(propertyInfoText) && roomInfo.getStayDetail() != null && roomInfo.getStayDetail().getMaxGuests() != null){
				String[] splittedPropertyInfoText = propertyInfoText.split("\\(");
				if(splittedPropertyInfoText.length > 1) {
					propertyInfoText = "(" + splittedPropertyInfoText[1];
					propertyInfoText = propertyInfoText.replace(")", " | " + Constants.FITS + " " + roomInfo.getStayDetail().getMaxGuests() + ")");
					propertyInfoText = propertyInfoText.split("\\)")[0] + ")";
				} else {
					propertyInfoText = "";
				}
				roomInfo.setPropertyInfoText(propertyInfoText);
			}
		} catch (Exception e) {
			LOGGER.error("propertyInfoText could not be added due to : {} ", e.getMessage());
		}
		// baseOccupancy Logic given by product
		/*if(roomInfo.getStayDetail() != null && roomInfo.getStayDetail().getBaseGuests() > 0) {
			int totalSearchedPaxCount = getTotalSearchedPaxCount(roomStayCandidates);
			int totalBaseOccupancy = roomInfo.getStayDetail().getBaseGuests() == null ? 0 : roomInfo.getStayDetail().getBaseGuests();
			roomInfo.getStayDetail().setMaxGuests(Math.max(totalSearchedPaxCount, totalBaseOccupancy));
			// extraBeds count added to bed count
			if(roomInfo.getStayDetail().getExtraBeds() > 0) {
				roomInfo.getStayDetail().setBed(roomInfo.getStayDetail().getBed() + roomInfo.getStayDetail().getExtraBeds());
			}
		}*/

		// Get free child text from recommendedRoomTypeDetails in case of recommended combo and roomTypeDetails for exact match case
		String freeChildText = getFreeChildTextFromHotelRates(hotelRates);
		buildSleepInfoText(roomInfo.getStayDetail(),isOHSExpEnable,hotelRates.getSellableCombo(),bedAndRoomPresent, freeChildText,isNewDetailPageTrue);
		return roomInfo;
	}

	private String getFreeChildTextFromHotelRates(HotelRates hotelRates) {
		String freeChildText = null;
		if (hotelRates != null) {
			if (hotelRates.getRecommendedRoomTypeDetails() != null && hotelRates.getRecommendedRoomTypeDetails().getFreeChildCount() > 0 && StringUtils.isNotEmpty(hotelRates.getRecommendedRoomTypeDetails().getFreeChildText())) {
				freeChildText = hotelRates.getRecommendedRoomTypeDetails().getFreeChildText();
			} else if (hotelRates.getRoomTypeDetails() != null && MapUtils.isNotEmpty(hotelRates.getRoomTypeDetails().getRoomType())) {
				RoomType roomType = hotelRates.getRoomTypeDetails().getRoomType().values().stream().findFirst().orElse(null);
				if (roomType != null && MapUtils.isNotEmpty(roomType.getRatePlanList())) {
					RatePlan ratePlan = roomType.getRatePlanList().values().stream().findFirst().orElse(null);
					if (ratePlan != null && ratePlan.freeChildCount > 0 && StringUtils.isNotEmpty(ratePlan.getFreeChildText())) {
						freeChildText = ratePlan.getFreeChildText();
					}
				}
			}
		}
		return freeChildText;
	}

	/***
	 * to build sleepInfoText(&additionalSleepInfoText) from stayDetail(maxGuests & maxCapacity) node.
	 * maxCapacity: the maximum number of people that could be accommodated in all selected rooms
	 * maxGuests: max(base guests, requested guests) in hotel
	 * stayDetail must be built before calling this method.
	 * for desktop, two nodes are used sleepInfoText(sleeps x guests) & additionalSleepInfoText(can accommodate y more..)
	 * for all other clients, single node sleepInfoText contains entire data
	 * @param stayDetail : hotel stay detail node containing maxGuests & maxCapacity nodes
	 * @param sellableCombo
	 * @return
	 */

	/*** Possible combination value of sellableCombo, Also sellableCombo represent the combination of bedroom and bed possibility in the lowestRoomTypeCode
	 *  sellableType[Bed] | sellableType[Bedroom] | sellableComboValue
	 *  false             |   false               |  0
	 *  true              |   false               |  1
	 *  false             |   true                |  2
	 *  true              |   true                |  3
	 */
	private void buildSleepInfoText(StayDetail stayDetail, boolean isOHSExpEnable, int sellableCombo, Pair<Boolean, Boolean> bedAndRoomPresent, String freeChildText,boolean isNewDetailPageTrue) {
		String client = MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue());
		if (StringUtils.isNotBlank(client) && stayDetail != null && stayDetail.getMaxGuests() != null && stayDetail.getMaxGuests() > 0) {
			String polyglotSleepsText = stayDetail.getMaxGuests() == 1 ? polyglotService.getTranslatedData(SPACE_SINGLE_OCCUPANCY_TEXT) : polyglotService.getTranslatedData(SPACE_OCCUPANCY_TEXT);
			if (StringUtils.isNotBlank(polyglotSleepsText)) {
				String sleepsText = polyglotSleepsText.replace(OCCUPANCY_PARAMETER, String.valueOf(stayDetail.getMaxGuests()));
				if (Constants.CLIENT_DESKTOP.equalsIgnoreCase(client) || isNewDetailPageTrue) {
					stayDetail.setSleepInfoText(sleepsText);
				} else {
					stayDetail.setSleepInfoText(OPEN_BOLD_TAG + sleepsText + CLOSE_BOLD_TAG);
				}

				if ((stayDetail.getMaxCapacity() != null && stayDetail.getMaxCapacity() > stayDetail.getMaxGuests()) || StringUtils.isNotEmpty(freeChildText)) {
					int extraGuests = (stayDetail.getMaxCapacity()==null)?0:stayDetail.getMaxCapacity()  - stayDetail.getMaxGuests();
					String polyglotExtraGuestsText = extraGuests == 1 ? polyglotService.getTranslatedData(SPACE_SINGLE_OCCUPANCY_EXTRA_GUESTS) : polyglotService.getTranslatedData(SPACE_OCCUPANCY_EXTRA_GUESTS);
					if (StringUtils.isNotBlank(polyglotSleepsText)) {
						String extraGuestsText = StringUtils.isNotEmpty(freeChildText) ? freeChildText : polyglotExtraGuestsText.replace(EXTRA_PARAMETER, String.valueOf(extraGuests));
						if (Constants.CLIENT_DESKTOP.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue()))) {
							stayDetail.setSleepInfoText(sleepsText);
							stayDetail.setAdditionalSleepInfoText(extraGuestsText);
						} else {
							if(isNewDetailPageTrue && (Constants.ANDROID.equalsIgnoreCase(client) || Constants.DEVICE_IOS.equalsIgnoreCase(client))) {
								stayDetail.setSleepInfoText(sleepsText + COMMA + SPACE + extraGuestsText);
							} else{
								stayDetail.setSleepInfoText(OPEN_BOLD_TAG + sleepsText + CLOSE_BOLD_TAG + SPACE + BULLET_HTML + SPACE + extraGuestsText);
							}
						}
					}
				}
				if(isOHSExpEnable){
					if(sellableCombo==1){
						// sellableType bed available in lowestRoomTypeCode, So Suggest private rooms
						stayDetail.setBedInfoText(null);
						if (Constants.CLIENT_DESKTOP.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue()))) {
							stayDetail.setSleepInfoText(null);
							stayDetail.setAdditionalSleepInfoText(null);
							if(bedAndRoomPresent.getValue()){
								stayDetail.setBedAndRoomAvailabilityText(polyglotService.getTranslatedData(HOSTEL_ROOMS_AVAILABLE_TEXT));
							}
						}else{
							if(bedAndRoomPresent.getValue()){
								stayDetail.setSleepInfoText(polyglotService.getTranslatedData(HOSTEL_ROOMS_AVAILABLE_TEXT));
							}else{
								stayDetail.setSleepInfoText(null);
							}
						}
					}else if(sellableCombo==2){
						// sellableType bedRoom available in lowestRoomTypeCode so suggest shared dorm
						if (Constants.CLIENT_DESKTOP.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue()))){
							if(bedAndRoomPresent.getKey()){
								stayDetail.setBedAndRoomAvailabilityText(polyglotService.getTranslatedData(HOSTEL_BEDS_AVAILABLE_TEXT));
							}
						}else{
							stayDetail.setBedInfoText(sleepsText);
							if(bedAndRoomPresent.getKey()){
								stayDetail.setSleepInfoText(polyglotService.getTranslatedData(HOSTEL_BEDS_AVAILABLE_TEXT));
							}else{
								stayDetail.setSleepInfoText(null);
							}
						}
					}else{
						stayDetail.setSleepInfoText(null);
						stayDetail.setAdditionalSleepInfoText(null);
						stayDetail.setBedInfoText(null);
					}
				}
			}
		}
	}



	private String buildPropertySellableType(RoomDetailsResponse roomDetailsResponse) {
		String propertySellableType = "Stay";
		if (Utility.isPropertyHotelOrResort(roomDetailsResponse.getHotelRates())){
			propertySellableType = "Room";
		}
		return propertySellableType;
	}

	private SelectRoomImpInfo getImpInfo(List<RecommendedCombo> recommendedCombos, List<RoomStayCandidate> roomStayCandidates) {
		if (CollectionUtils.isEmpty(roomStayCandidates) || CollectionUtils.isEmpty(recommendedCombos)) {
			return null;
		}

		int requestedRooms = roomStayCandidates.size();
		StringBuilder sb = new StringBuilder();
		List<RoomTariff> requestedRoomTariff = new ArrayList<>();
		for(RoomStayCandidate rsc : roomStayCandidates){
			RoomTariff rt = new RoomTariff();
			rt.setNumberOfAdults(rsc.getAdultCount());
			rt.setNumberOfChildren(CollectionUtils.isNotEmpty(rsc.getChildAges()) ? rsc.getChildAges().size():0);
			requestedRoomTariff.add(rt);
		}

		boolean roomCountMatched = false;
		for(RecommendedCombo recommendedCombo: recommendedCombos){
			int roomSize = 0;
			requestedRoomTariff.forEach(a-> a.setRoomCount(null));
			for(RoomDetails roomDetail: recommendedCombo.getRooms()){
				for(SelectRoomRatePlan selectRoomRatePlan: roomDetail.getRatePlans()){
					for(Tariff tariff: selectRoomRatePlan.getTariffs()){
						if(CollectionUtils.isNotEmpty(tariff.getRoomTariffs())){
							roomSize += tariff.getRoomTariffs().size();
						}
						int index = 0;
						for(RoomTariff rt : tariff.getRoomTariffs()){
							//pick from recommended combo and fill in non null room count of requested occupancy, if all requested occupancy has a room count set, that means occupancy has matched, else one or more of them will remain null which means mismatch
							Optional<RoomTariff> ort =  requestedRoomTariff.stream().filter(a-> a.getNumberOfAdults() == rt.getNumberOfAdults() && a.getNumberOfChildren() == rt.getNumberOfChildren() && a.getRoomCount() == null ).findFirst();
							if(ort.isPresent()){
								ort.get().setRoomCount(index++);
							}
						}
					}
				}
			}

			if(requestedRooms == roomSize && !requestedRoomTariff.stream().filter(a-> a.getRoomCount() == null).findFirst().isPresent()){
				return null;
			}
			//if room has matched but occupancy hasnt then
			if(requestedRooms == roomSize) {
				roomCountMatched = true;
			}
		}
		SelectRoomImpInfo selectRoomImpInfo = new SelectRoomImpInfo();
		selectRoomImpInfo.setTitle(polyglotService.getTranslatedData("IMPORTANT_TITLE"));
		//if room count hasnt matched
		if(!roomCountMatched) {
			sb.append(requestedRooms).append(Constants.SPACE).append(requestedRooms > 1 ? polyglotService.getTranslatedData(ConstantsTranslation.ADDITIONAL_FEE_SUBTEXT_ROOMS) : polyglotService.getTranslatedData(ConstantsTranslation.ADDITIONAL_FEE_SUBTEXT_ROOM) );
			String message = polyglotService.getTranslatedData("ROOM_MISMATCH_TEXT").replace("{data}", sb.toString());
			selectRoomImpInfo.setMessage(message);
		} else // if none of the combo matches on room count and occuapancy details then different message
			selectRoomImpInfo.setMessage(polyglotService.getTranslatedData("OCCUPANCY_MISMATCH_TEXT"));
		return selectRoomImpInfo;
	}

	private List<RecommendedCombo> getRecommendedCombos(HotelRates hotelRates,
														HotelsRoomInfoResponseEntity hotelsRoomInfoResponseEntity,
														HotelImage hotelImage,
														String listingType,
														Map<String, String> expData, String askedCurrency, String funnelSource,
														int days, int ap, boolean isBlockPAH,
														Map<String, JsonNode> roomPersuasionMap,
														CommonModifierResponse commonModifierResponse, Map<String, String> ratePlanCodeAndNameMap, boolean isLuxeHotel, boolean isAltAccoHotel) {
		if (hotelRates.getRecommendedRoomTypeDetails()==null && hotelRates.getRoomTypeDetails() == null) {
			return null;
		}

		List<RecommendedCombo> recommendedCombos = new ArrayList<>();
		List<RoomDetails> roomDetails;

		if (hotelRates.getRecommendedRoomTypeDetails() != null) {
			/* Case 1 : Make Combo from RecommendedRoomTypeDetails */
			roomDetails = getRooms(hotelRates, hotelRates.getRecommendedRoomTypeDetails(), hotelsRoomInfoResponseEntity, hotelImage, listingType, expData, true, askedCurrency, funnelSource,
					hotelRates.getWalletSurge(), hotelRates.getSegments(), days, hotelRates.getStarRating(), ap, isBlockPAH, roomPersuasionMap, commonModifierResponse, false, ratePlanCodeAndNameMap, isLuxeHotel, isAltAccoHotel, hotelRates.isUserGCCAndMmtExclusive(), hotelRates.isGroupBookingPrice(), true, null, hotelRates.getPropertyType(), false);

			String sellableType = Constants.SELLABLE_ROOM_TYPE;
			if (CollectionUtils.isNotEmpty(roomDetails) && CollectionUtils.isNotEmpty(roomDetails.get(0).getRatePlans())) {
				sellableType = roomDetails.get(0).getRatePlans().get(0).getSellableType();
			}

			RecommendedCombo recommendedCombo = buildBasicRecommendedCombo(roomDetails,
					utility.getComboName(hotelRates.getRecommendedRoomTypeDetails().getComboMealPlan()),
					hotelRates.getRecommendedRoomTypeDetails().isStaycationDeal(), true, funnelSource,
					hotelRates.getRecommendedRoomTypeDetails().getOccupancyDetails(),
					hotelRates.getRecommendedRoomTypeDetails().getComboMealPlan());
			boolean myPartner = Objects.nonNull(commonModifierResponse) && Objects.nonNull(commonModifierResponse.getExtendedUser()) && Utility.isMyPartnerRequest(commonModifierResponse.getExtendedUser().getProfileType(), commonModifierResponse.getExtendedUser().getAffiliateId());
			if(null == recommendedCombo.getComboTariff())
				recommendedCombo.setComboTariff(new Tariff());
			recommendedCombo.setCorpApprovalInfo(commonResponseTransformer.buildCorpApprovalInfo(hotelRates.getRecommendedRoomTypeDetails().getTotalDisplayFare().getCorpMetaData()));
			recommendedCombo.getComboTariff().setPriceMap(commonResponseTransformer.getPriceMap(hotelRates.getRecommendedRoomTypeDetails().getTotalDisplayFare().getDisplayPriceBreakDown(),
					hotelRates.getRecommendedRoomTypeDetails().getTotalDisplayFare().getDisplayPriceBreakDownList(),
					expData,
					hotelRates.getRecommendedRoomTypeDetails().getOccupancyDetails()!=null?hotelRates.getRecommendedRoomTypeDetails().getOccupancyDetails().getNumOfRooms():null,
					askedCurrency,
					sellableType, days, false, "", utility.buildToolTip(funnelSource), Utility.isGroupBookingFunnel(funnelSource), hotelRates.isGroupBookingPrice(),myPartner, listingType));
			recommendedCombo.getComboTariff().setDefaultPriceKey(hotelRates.getRecommendedRoomTypeDetails().getTotalDisplayFare().getDisplayPriceBreakDown() != null ? (hotelRates.getRecommendedRoomTypeDetails().getTotalDisplayFare().getDisplayPriceBreakDown().getCouponInfo()!=null?
					hotelRates.getRecommendedRoomTypeDetails().getTotalDisplayFare().getDisplayPriceBreakDown().getCouponInfo().getCouponCode(): "DEFAULT"): null);
			if(hotelRates.getRecommendedRoomTypeDetails().getTotalDisplayFare().getDisplayPriceBreakDown() != null && hotelRates.getRecommendedRoomTypeDetails().getTotalDisplayFare().getDisplayPriceBreakDown().getCouponInfo()!=null &&
					CollectionUtils.isNotEmpty(hotelRates.getRecommendedRoomTypeDetails().getTotalDisplayFare().getDisplayPriceBreakDown().getCouponInfo().getVoucherList())) {
				List<Voucher> voucherList = hotelRates.getRecommendedRoomTypeDetails().getTotalDisplayFare().getDisplayPriceBreakDown().getCouponInfo().getVoucherList();
				recommendedCombo.getComboTariff().setVoucherCode(voucherList.get(0).getVoucherCode());
				recommendedCombo.getComboTariff().setVoucherType(voucherList.get(0).getVoucherType());
			}
			if (hotelRates.getRecommendedRoomTypeDetails().getOccupancyDetails()!=null) {
				recommendedCombo.getComboTariff().setOccupancydetails(new RoomTariff());
				recommendedCombo.getComboTariff().getOccupancydetails().setNumberOfAdults(hotelRates.getRecommendedRoomTypeDetails().getOccupancyDetails().getAdult());
				recommendedCombo.getComboTariff().getOccupancydetails().setNumberOfChildren(hotelRates.getRecommendedRoomTypeDetails().getOccupancyDetails().getChild());
				if (hotelRates.getRecommendedRoomTypeDetails().getOccupancyDetails().getChild()>0)
					recommendedCombo.getComboTariff().getOccupancydetails().setChildAges(hotelRates.getRecommendedRoomTypeDetails().getOccupancyDetails().getChildAges());
				recommendedCombo.getComboTariff().getOccupancydetails().setRoomCount(hotelRates.getRecommendedRoomTypeDetails().getOccupancyDetails().getNumOfRooms());
			}
			recommendedCombo.setPaymentPlan(commonResponseTransformer.buildPaymentPlan(hotelRates.getRecommendedRoomTypeDetails().getPaymentPlan()));

			double baseComboFare = 0.0;
			if (utility.isGroupBookingFunnel(funnelSource)) {
				recommendedCombo.setBaseCombo(true);
				baseComboFare = hotelRates.getRecommendedRoomTypeDetails().getTotalDisplayFare().getDisplayPriceBreakDown().getDisplayPrice();
				recommendedCombo.setComboText("<b>"+recommendedCombo.getComboName()+"</b>");
			}
			recommendedCombo.setComboOfferPersuasions(hotelRates.getRecommendedRoomTypeDetails().getComboOfferPersuasions());
			recommendedCombos.add(recommendedCombo);

			if (CollectionUtils.isNotEmpty(hotelRates.getOtherRecommendedRooms())) {
				/* Case 2 : Make Combo from OtherRecommendedRooms */
				for (RoomTypeDetails otherRecommendation: hotelRates.getOtherRecommendedRooms()) {
					roomDetails = getRooms(hotelRates, otherRecommendation, hotelsRoomInfoResponseEntity, hotelImage, listingType, expData, true, askedCurrency, funnelSource, null, null, days, hotelRates.getStarRating(),
							ap, isBlockPAH, roomPersuasionMap, commonModifierResponse, false, ratePlanCodeAndNameMap, isLuxeHotel, isAltAccoHotel, hotelRates.isUserGCCAndMmtExclusive(), hotelRates.isGroupBookingPrice(), true, null, hotelRates.getPropertyType(), false);

					if (CollectionUtils.isNotEmpty(roomDetails) && CollectionUtils.isNotEmpty(roomDetails.get(0).getRatePlans())) {
						sellableType = roomDetails.get(0).getRatePlans().get(0).getSellableType();
					} else {
						sellableType = Constants.SELLABLE_ROOM_TYPE;
					}
					recommendedCombo = buildBasicRecommendedCombo(roomDetails,
							utility.getComboName(otherRecommendation.getComboMealPlan()),
							otherRecommendation.isStaycationDeal(), false, funnelSource,
							otherRecommendation.getOccupancyDetails(), otherRecommendation.getComboMealPlan());
					if(null == recommendedCombo.getComboTariff())
						recommendedCombo.setComboTariff(new Tariff());
					recommendedCombo.setCorpApprovalInfo(commonResponseTransformer.buildCorpApprovalInfo(otherRecommendation.getTotalDisplayFare().getCorpMetaData()));
					recommendedCombo.getComboTariff().setPriceMap(commonResponseTransformer.getPriceMap(otherRecommendation.getTotalDisplayFare().getDisplayPriceBreakDown(),
							otherRecommendation.getTotalDisplayFare().getDisplayPriceBreakDownList(),
							expData,
							otherRecommendation.getOccupancyDetails()!=null?otherRecommendation.getOccupancyDetails().getNumOfRooms():null,
							askedCurrency,
							sellableType, days, false,"", utility.buildToolTip(funnelSource), utility.isGroupBookingFunnel(funnelSource), hotelRates.isGroupBookingPrice(),myPartner, listingType));
					recommendedCombo.getComboTariff().setDefaultPriceKey(otherRecommendation.getTotalDisplayFare().getDisplayPriceBreakDown() != null ? (otherRecommendation.getTotalDisplayFare().getDisplayPriceBreakDown().getCouponInfo()!=null?
							otherRecommendation.getTotalDisplayFare().getDisplayPriceBreakDown().getCouponInfo().getCouponCode(): "DEFAULT"): null);
					if(hotelRates.getRecommendedRoomTypeDetails().getTotalDisplayFare().getDisplayPriceBreakDown() != null && hotelRates.getRecommendedRoomTypeDetails().getTotalDisplayFare().getDisplayPriceBreakDown().getCouponInfo()!=null &&
							CollectionUtils.isNotEmpty(hotelRates.getRecommendedRoomTypeDetails().getTotalDisplayFare().getDisplayPriceBreakDown().getCouponInfo().getVoucherList())) {
						List<Voucher> voucherList = hotelRates.getRecommendedRoomTypeDetails().getTotalDisplayFare().getDisplayPriceBreakDown().getCouponInfo().getVoucherList();
						recommendedCombo.getComboTariff().setVoucherCode(voucherList.get(0).getVoucherCode());
						recommendedCombo.getComboTariff().setVoucherType(voucherList.get(0).getVoucherType());
					}
					if (otherRecommendation.getOccupancyDetails()!=null) {
						recommendedCombo.getComboTariff().setOccupancydetails(new RoomTariff());
						recommendedCombo.getComboTariff().getOccupancydetails().setNumberOfAdults(otherRecommendation.getOccupancyDetails().getAdult());
						recommendedCombo.getComboTariff().getOccupancydetails().setNumberOfChildren(otherRecommendation.getOccupancyDetails().getChild());
						if (otherRecommendation.getOccupancyDetails().getChild()>0)
							recommendedCombo.getComboTariff().getOccupancydetails().setChildAges(otherRecommendation.getOccupancyDetails().getChildAges());
						recommendedCombo.getComboTariff().getOccupancydetails().setRoomCount(otherRecommendation.getOccupancyDetails().getNumOfRooms());
					}
					recommendedCombo.setPaymentPlan(commonResponseTransformer.buildPaymentPlan(otherRecommendation.getPaymentPlan()));
					if (utility.isGroupBookingFunnel(funnelSource)) {
						double otherRecommendationDisplayPrice = otherRecommendation.getTotalDisplayFare().getDisplayPriceBreakDown().getDisplayPrice();
						long differenceInPriceFromBaseCombo = (long) (otherRecommendationDisplayPrice - baseComboFare);
						if(differenceInPriceFromBaseCombo != 0) {
							recommendedCombo.setComboText(getComboText(otherRecommendation.getComboMealPlan(), differenceInPriceFromBaseCombo));
						}
					}
					recommendedCombo.setComboOfferPersuasions(otherRecommendation.getComboOfferPersuasions());
					recommendedCombos.add(recommendedCombo);
				}
			}
		}
		if(recommendedCombos.size() > 1 || (hotelRates.getOccupencyLessRoomTypeDetails() != null && hotelRates.getOccupencyLessRoomTypeDetails().getRoomType() != null && hotelRates.getOccupencyLessRoomTypeDetails().getRoomType().size() > 1))
			commonModifierResponse.setSkipRoomSelectEnabled(false);

		//TODO: will enable this flow once unified GI review is 100% live
//		else if (hotelRates.getRoomTypeDetails() != null) {
//			/* Case 3 : Make recommended combos out of the exact matched Rooms */
//			roomDetails = createExactCombo(hotelRates.getRoomTypeDetails(), hotelsRoomInfoResponseEntity, hotelImage, listingType, expData,
//					askedCurrency,funnelSource, days,
//					hotelRates.getStarRating(), ap, isBlockPAH, commonModifierResponse,roomPersuasionMap, ratePlanCodeAndNameMap, isLuxeHotel, isAltAccoHotel, hotelRates.isUserGCCAndMmtExclusive(), hotelRates.isGroupBookingPrice());
//			for (RoomDetails roomDetail: roomDetails) {
//				List<RoomDetails> roomDetailList = Collections.singletonList(roomDetail);
//				RecommendedCombo recommendedCombo = buildBasicRecommendedCombo(roomDetailList, null, roomDetail.getRatePlans().get(0).isStaycationDeal(),
//						false, funnelSource, null, null);
//				if (null == recommendedCombo.getComboTariff())
//					recommendedCombo.setComboTariff(new Tariff());
//				recommendedCombo.getComboTariff().setPriceMap(roomDetail.getRatePlans().get(0).getTariffs().get(0).getPriceMap());
//				recommendedCombo.getComboTariff().setDefaultPriceKey(roomDetail.getRatePlans().get(0).getTariffs().get(0).getDefaultPriceKey());
//				if (roomDetail.getRatePlans().get(0).getTariffs().get(0).getOccupancydetails() != null) {
//					recommendedCombo.getComboTariff().setOccupancydetails(new RoomTariff());
//					recommendedCombo.getComboTariff().getOccupancydetails().setNumberOfAdults(roomDetail.getRatePlans().get(0).getTariffs().get(0).getOccupancydetails().getNumberOfAdults());
//					recommendedCombo.getComboTariff().getOccupancydetails().setNumberOfChildren(roomDetail.getRatePlans().get(0).getTariffs().get(0).getOccupancydetails().getNumberOfChildren());
//					if (roomDetail.getRatePlans().get(0).getTariffs().get(0).getOccupancydetails().getNumberOfChildren() > 0)
//						recommendedCombo.getComboTariff().getOccupancydetails().setChildAges(roomDetail.getRatePlans().get(0).getTariffs().get(0).getOccupancydetails().getChildAges());
//					recommendedCombo.getComboTariff().getOccupancydetails().setRoomCount(roomDetail.getRatePlans().get(0).getTariffs().get(0).getOccupancydetails().getRoomCount());
//				}
//				recommendedCombo.setCorpApprovalInfo(roomDetail.getRatePlans().get(0).getCorpApprovalInfo());
//				recommendedCombos.add(recommendedCombo);
//			}
//		}
		return recommendedCombos;
	}

	private RecommendedCombo buildBasicRecommendedCombo(List<RoomDetails> roomDetailsList, String comboName,
														boolean isStaycationDeal, boolean baseCombo,
														String funnelSource, OccupancyDetails occupancyDetails,
														String comboMealPlan) {
		RecommendedCombo recommendedCombo = new RecommendedCombo();
		recommendedCombo.setComboId(UUID.randomUUID().toString());
		recommendedCombo.setComboName(comboName);
		recommendedCombo.setRooms(roomDetailsList);
		recommendedCombo.setStaycationDeal(isStaycationDeal);
		recommendedCombo.setComboMealPlan(comboMealPlan);
		boolean isSameCancellationPolicyInAllRatePlans = true;
		String cancellationType = null;
		int totalTarrifs = 0;
		String mealTypeCode = null;
		String sellableType = null;
		String mealTypeCodeText = "";
		Set<String> mealInclusionCodeList = new HashSet<>();
		boolean index = false;
		//set campaign alert for recommended combo
		for (RoomDetails roomDetails : roomDetailsList) {
			Optional<SelectRoomRatePlan> selectRoomRatePlan = roomDetails.getRatePlans().stream().findFirst();
			if(selectRoomRatePlan.isPresent() && selectRoomRatePlan.get().getCancellationPolicy()!=null){
				// we will find the value of first element of cancellation Policy and Meal Inclusion
				cancellationType = selectRoomRatePlan.get().getCancellationPolicy().getType().name();
			}
			for (SelectRoomRatePlan ratePlan : roomDetails.getRatePlans()) {
				if (null != ratePlan.getCancellationPolicy()
						&& BookedCancellationPolicyType.FC == ratePlan.getCancellationPolicy().getType()
						&& CollectionUtils.isNotEmpty(ratePlan.getTariffs())){
					for(Tariff tariff : ratePlan.getTariffs()){
						if(null == tariff.getCampaignAlert()){
							break;
						}
					}
					if(null == recommendedCombo.getComboTariff())
						recommendedCombo.setComboTariff(new Tariff());
					recommendedCombo.getComboTariff().setCampaignAlert(ratePlan.getTariffs().get(0).getCampaignAlert());
				}
				if (ratePlan.getCancellationPolicy() != null) {
					// to check if all the values of cancellation policy are same for all rate plans
					isSameCancellationPolicyInAllRatePlans = isSameCancellationPolicyInAllRatePlans && cancellationType.equalsIgnoreCase(ratePlan.getCancellationPolicy().getType().name());
					cancellationType = ratePlan.getCancellationPolicy().getType().name();
				}
				if (CollectionUtils.isNotEmpty(ratePlan.getTariffs())) {
					totalTarrifs += getRoomTariffInThisRatePlanCount(ratePlan.getTariffs());
				}
				if (CollectionUtils.isNotEmpty(ratePlan.getInclusionsList())) {
//						this function is to check if all the rooms has same meal inclusion in all the rooms of combo
					for (BookedInclusion inclusion : ratePlan.getInclusionsList()) {
						if (inclusion != null && MEAL.equalsIgnoreCase(inclusion.getCategory()) && StringUtils.isNotEmpty(inclusion.getInclusionCode())) {
							mealInclusionCodeList.add(inclusion.getInclusionCode());
						}
					}
				}
				if(!index){
					sellableType = roomDetails.getRoomCategoryText()!=null?roomDetails.getRoomCategoryText():SELLABLE_ROOM_TYPE;
					index = true;
				}
				if(StringUtils.isNotEmpty(ratePlan.getName())){
					String[] arr = ratePlan.getName().split("\\|");
					if (arr != null && arr.length > 1) {
						mealTypeCodeText = arr[1];
					}
					if(StringUtils.isEmpty(mealTypeCodeText)){
						arr = ratePlan.getName().split(WITH);
						if (arr != null && arr.length > 1) {
							mealTypeCodeText = arr[1];
						}
					}
				}
			}
			buildGroupBookingComboText(roomDetails, recommendedCombo, baseCombo, funnelSource, occupancyDetails);
		}
		try {
			recommendedCombo.setComboTitle(getComboTitle(
					isSameCancellationPolicyInAllRatePlans, cancellationType, totalTarrifs,
					mealInclusionCodeList,mealTypeCode ,sellableType, mealTypeCodeText
			));
		} catch (Exception e){
			LOGGER.warn("Error occured in building Combo Title");
		}
		return recommendedCombo;
	}

	int getRoomTariffInThisRatePlanCount(List<Tariff> tariffList){
		for(Tariff tariff: tariffList){
			if(tariff!=null && CollectionUtils.isNotEmpty(tariff.getRoomTariffs())){
				return tariff.getRoomTariffs().size();
			}
		}
		return 0;
	}

	private String getComboTitle(boolean isSameCancellationPolicyInAllRatePlans, String cancellationType,
								 int totalTarrifs, Set<String> mealInclusionCodeList, String mealTypeCode,
								 String sellableType, String mealTypeCodeText){
		/* Creation of Polyglot key begins */
		String comboTitle = ROOMS_COMBO;
		if(totalTarrifs == 1){
			comboTitle = SINGLE_ + comboTitle;
		} else {
			comboTitle = PLURAL_ + comboTitle;
		}
		comboTitle += (isSameCancellationPolicyInAllRatePlans && CANCELLATION_TYPE_FC.equalsIgnoreCase(cancellationType))?_FC:_NR;
		String roomTextFromPolyglot = polyglotService.getTranslatedData(comboTitle);
		comboTitle = roomTextFromPolyglot!=null?roomTextFromPolyglot:"";
		comboTitle = comboTitle.replace("{total_tarrifs}", String.valueOf(totalTarrifs));
		comboTitle = comboTitle.replace("{sellable_type}", StringUtils.capitalize(sellableType));
		mealTypeCode = mealInclusionCodeList.size()==1?mealInclusionCodeList.stream().findFirst().get():"";
		mealTypeCode = (CollectionUtils.isNotEmpty(mealPlanCodeList) && mealPlanCodeList.contains(mealTypeCode))?COMBO_TITLE + mealTypeCode:mealTypeCode;
		String mealTextFromPolyglot =  polyglotService.getTranslatedData(mealTypeCode);
		comboTitle += (StringUtils.isNotEmpty(mealTextFromPolyglot))?PIPE_SEPARATOR + mealTextFromPolyglot:"";
		if(!Constants.DEVICE_OS_DESKTOP.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue()))) {
			comboTitle += (StringUtils.isNotEmpty(mealTypeCodeText)) ? PIPE_SEPARATOR + mealTypeCodeText : "";
		}
		/* Creation of polyglot and getting data from polyglot*/
		return comboTitle;
	}

	private QuickBook getQuickBook(com.mmt.hotels.model.response.pricing.QuickBook quickBookHes){
		if(quickBookHes!=null && quickBookHes.getRoomType()!=null){
			QuickBook quickBookCg = new QuickBook();
			quickBookCg.setPreviousBookingRating(quickBookHes.getPreviousBookingRating());
			quickBookCg.setRoomType(quickBookHes.getRoomType().name());
			return quickBookCg;
		}
		return null;
	}
	private ResponseContextDetail getContextDetails(HotelRates hotelRates) {
		ResponseContextDetail responseContextDetail = new ResponseContextDetail();
		responseContextDetail.setCurrency(hotelRates.getCurrencyCode());
		responseContextDetail.setMmtHotelCategory(hotelRates.getMmtHotelCategory());
		return responseContextDetail;
	}

	private FeatureFlags getFeatureFlags(HotelRates hotelRates, List<RoomDetails> rooms,LinkedHashMap<String, String> expDataMap, CommonModifierResponse commonModifierResponse) {
		if (hotelRates == null)
			return null;
		FeatureFlags featureFlags = new FeatureFlags();
		featureFlags.setBestPriceGuaranteed(hotelRates.isBestPriceGuaranteed());
		featureFlags.setBnpl(hotelRates.getIsBNPLAvailable());
		featureFlags.setBnplBaseAmount(hotelRates.getBnplBaseAmount());
		featureFlags.setFirstTimeUser(hotelRates.isFirstTimeUser());
		featureFlags.setFreeCancellation(hotelRates.isFreeCancellationAvailable());
		featureFlags.setPahAvailable(hotelRates.getIsPAHAvailable() != null ? hotelRates.getIsPAHAvailable() : false);
		featureFlags.setPahTariffAvailable(hotelRates.getIsPAHTariffAvailable());
		if(Constants.DEVICE_OS_PWA.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue()))){
			featureFlags.setPwaDetailSelectMerge(hotelRates.isDetailSelectMerge());
		}
		featureFlags.setPahWalletApplicable(hotelRates.isPahWalletApplicable());
		featureFlags.setRequestToBook(hotelRates.isRequestToBook());
		featureFlags.setRtbPreApproved(hotelRates.isRtbPreApproved());
		featureFlags.setRtbAutoCharge(hotelRates.isRtbAutoCharge());
		if(hotelRates.getBlackInfo() != null)
			featureFlags.setIsGoTribe3_0(hotelRates.getBlackInfo().getIsNewLoyaltyProgramForGI());
		Set<String> payModes = new HashSet<>();
		if (CollectionUtils.isNotEmpty(rooms)) {
			for (RoomDetails roomDetail : rooms) {
				if (CollectionUtils.isNotEmpty(roomDetail.getRatePlans())) {
					for (SelectRoomRatePlan ratePlan : roomDetail.getRatePlans()) {
						payModes.add(ratePlan.getPayMode());
					}
				}
			}
			if (CollectionUtils.isNotEmpty(payModes))
				featureFlags.setPayModes(payModes);
		}
		if(commonModifierResponse != null)
			featureFlags.setSkipSelectRoom(commonModifierResponse.isSkipRoomSelectEnabled());
		featureFlags.setGroupBookingPrice(hotelRates.isGroupBookingPrice());
		featureFlags.setMaskedPrice(hotelRates.isMaskedPrice());
		featureFlags.setOptimisedSelection(MapUtils.isNotEmpty(expDataMap) && Constants.PROPERTY_TYPE_HOSTEL.equalsIgnoreCase(hotelRates.getPropertyType()) && Constants.EXP_TRUE_VALUE.equalsIgnoreCase(expDataMap.get(Constants.OPTIMIZE_HOSTEL_SELECTION_EXP)));
		if(hotelRates.getMpHotelFareHoldAmount()!=null) {
			featureFlags.setHotelFareHold(true);
		}
		return featureFlags;
	}

	private List<GroupRatePlanFilter> getFilters(List<RoomDetails> exactRooms, List<RoomDetails> occupancyRooms,
												 List<Filter> filterCriteria, String funnelSource, int ap, boolean isblockPAH,
												 BNPLVariant bnplVariant, CommonModifierResponse commonModifierResponse,
												 boolean isLuxeHotel, Map<String, GroupRatePlanFilter> groupRatePlanFilterConfMap,
												 String propertyType) {
		if (CollectionUtils.isEmpty(exactRooms) && CollectionUtils.isEmpty(occupancyRooms)) {
			return null;
		}
		List<RoomDetails> rooms = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(exactRooms)) {
			rooms.addAll(exactRooms);
		}
		if (CollectionUtils.isNotEmpty(occupancyRooms)) {
			rooms.addAll(occupancyRooms);
		}
		List<RatePlanFilter> ratePlanFilters = new ArrayList<>();
		boolean pahFilter = false, freeCancellationFilter = false, freeBreakfastFilter = false, fczpnFilter = false, specialDeals = false, staycation = false;
		boolean twomealsFilter = false, allmealsFilter = false, partnerExclusiveFilter = false, packageRoomFilter = false, nonLuxePackageRoomFilter = false;
		boolean mpBookNowFilterFor0 = false;
		boolean mpBookNowFilterFor1 = false;
		boolean allInclusiveFilter = false;
		int packageRatePlans = 0;
		boolean myPartner = Objects.nonNull(commonModifierResponse) && Objects.nonNull(commonModifierResponse.getExtendedUser()) && Utility.isMyPartnerRequest(commonModifierResponse.getExtendedUser().getProfileType(), commonModifierResponse.getExtendedUser().getAffiliateId());
		boolean isSellableTypeBedPresent = false, isSellableTypeRoomPresent = false;
		for (RoomDetails roomDetails : rooms) {
			for (SelectRoomRatePlan ratePlan : roomDetails.getRatePlans()) {
				if (CollectionUtils.isNotEmpty(ratePlan.getFilterCode())) {
					if (ratePlan.getFilterCode().contains("FREE_CANCELLATION")) {
						freeCancellationFilter = true;
					}
					if (ratePlan.getFilterCode().contains(BOOK_NOW_AT_0)) {
						mpBookNowFilterFor0 = true;
					}
					if (ratePlan.getFilterCode().contains(BOOK_NOW_AT_1)) {
						mpBookNowFilterFor1 = true;
					}
					if (ratePlan.getFilterCode().contains("FREE_BREAKFAST"))
						freeBreakfastFilter = true;
					if (!myPartner) {
						if (ratePlan.getFilterCode().contains("PAH")) {
							if (isblockPAH && ap < 5) {
								fczpnFilter = true;
							} else {
								pahFilter = true;
							}
						}
						if (ratePlan.getFilterCode().contains("FCZPN"))
							fczpnFilter = true;
						if (ratePlan.getFilterCode().contains("SPECIALDEALS"))
							specialDeals = true;
						if (ratePlan.getFilterCode().contains("STAYCATION"))
							staycation = true;
					}
					if (ratePlan.getFilterCode().contains("TWO_MEAL_AVAIL"))
						twomealsFilter = true;
					if (ratePlan.getFilterCode().contains("ALL_MEAL_AVAIL"))
						allmealsFilter = true;
					if (ratePlan.getFilterCode().contains(PACKAGE_RATE)) {
						packageRoomFilter = true;
						packageRatePlans++;
					}
					if (ratePlan.getFilterCode().contains("CTA_RATES_AVAIL"))
						partnerExclusiveFilter = true;
					if (ratePlan.getFilterCode().contains(ALL_INCLUSIVE))
						allInclusiveFilter = true;
				}
				if (Constants.PROPERTY_TYPE_HOSTEL.equalsIgnoreCase(propertyType) && SELLABLE_ROOM_TYPE.equalsIgnoreCase(ratePlan.getSellableType())) {
					isSellableTypeRoomPresent = true;
				}
				if (PROPERTY_TYPE_HOSTEL.equalsIgnoreCase(propertyType) && SELLABLE_BED_TYPE.equalsIgnoreCase(ratePlan.getSellableType())) {
					isSellableTypeBedPresent = true;
				}
			}
		}

		List<GroupRatePlanFilter> groupRatePlanFilterList = new ArrayList<>();

		if (isSellableTypeRoomPresent && isSellableTypeBedPresent && MapUtils.isNotEmpty(groupRatePlanFilterConfMap)) {
			GroupRatePlanFilter groupFCRatePlanFilter = getGroupRatePlanFilter(groupRatePlanFilterConfMap, "PrivateRoom");
			//Dynamic Private Room filter is available in listing page and will be a continuity filter.
			boolean selected = filterCriteria != null && filterCriteria.stream().anyMatch(a -> PRIVATE_ROOM.equalsIgnoreCase(a.getFilterValue()));
			RatePlanFilter ratePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData("PRIVATE_ROOM_FILTER"), "PRIVATE_ROOMS", "", selected);
			groupFCRatePlanFilter.getRatePlanFilterList().add(ratePlanFilter);
			groupRatePlanFilterList.add(groupFCRatePlanFilter);
		}

		if (utility.isSPKGExperimentOn(commonModifierResponse.getExpDataMap()) && packageRatePlans > 1 && MapUtils.isNotEmpty(groupRatePlanFilterConfMap)) {
			GroupRatePlanFilter groupElitePackageRatePlanFilter = getGroupRatePlanFilter(groupRatePlanFilterConfMap, "ElitePackage");
			boolean selected = filterCriteria != null && filterCriteria.stream().anyMatch(a -> Constants.PACKAGE_RATE.equalsIgnoreCase(a.getFilterValue()));
			RatePlanFilter ratePlanFilter = new RatePlanFilter("Elite Package", Constants.PACKAGE_RATE, "", selected);
			groupElitePackageRatePlanFilter.getRatePlanFilterList().add(ratePlanFilter);
			groupRatePlanFilterList.add(groupElitePackageRatePlanFilter);

		}

		if (partnerExclusiveFilter) {
			boolean selected = filterCriteria!=null && filterCriteria.stream().anyMatch(a -> "CTA_RATES_AVAIL".equalsIgnoreCase(a.getFilterValue()) );
			RatePlanFilter ratePlanFilter = new RatePlanFilter("Partner Exclusive Rate", "CTA_RATES_AVAIL", "", selected);
			ratePlanFilters.add(ratePlanFilter);
		}

		if (allInclusiveFilter && MapUtils.isNotEmpty(groupRatePlanFilterConfMap)) {
			GroupRatePlanFilter groupFCRatePlanFilter = getGroupRatePlanFilter(groupRatePlanFilterConfMap, "AllInclusive");
			boolean selected = filterCriteria!=null && filterCriteria.stream().anyMatch(a ->"ALL_INCLUSIVE".equalsIgnoreCase(a.getFilterValue()));
			RatePlanFilter ratePlanFilter = new RatePlanFilter("All Inclusive Rates", "ALL_INCLUSIVE", "", selected);
			groupFCRatePlanFilter.getRatePlanFilterList().add(ratePlanFilter);
			groupRatePlanFilterList.add(groupFCRatePlanFilter);
		}

		if (freeCancellationFilter && MapUtils.isNotEmpty(groupRatePlanFilterConfMap)) {
			GroupRatePlanFilter groupFCRatePlanFilter = getGroupRatePlanFilter(groupRatePlanFilterConfMap, "FreeCancellation");
			boolean selected = filterCriteria!=null && filterCriteria.stream().anyMatch(a ->"CANCELLATION_AVAIL".equalsIgnoreCase(a.getFilterValue()));
			RatePlanFilter ratePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData("FREE_CANCELLATION_FILTER"), "FREE_CANCELLATION", "", selected);
			groupFCRatePlanFilter.getRatePlanFilterList().add(ratePlanFilter);
			groupRatePlanFilterList.add(groupFCRatePlanFilter);
		}
		if(mpBookNowFilterFor0) {
			boolean selected = filterCriteria!=null && filterCriteria.stream().anyMatch(a ->"CANCELLATION_AVAIL".equalsIgnoreCase(a.getFilterValue()));
			RatePlanFilter mpBookNowRatePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData(BOOK_NOW_AT_0), BOOK_NOW_AT_0, "", selected);
			ratePlanFilters.add(mpBookNowRatePlanFilter);
		}
		if(mpBookNowFilterFor1) {
			boolean selected = filterCriteria!=null && filterCriteria.stream().anyMatch(a ->"CANCELLATION_AVAIL".equalsIgnoreCase(a.getFilterValue()));
			RatePlanFilter mpBookNowRatePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData(BOOK_NOW_AT_1), BOOK_NOW_AT_1, "", selected);
			ratePlanFilters.add(mpBookNowRatePlanFilter);
		}

		if ((freeBreakfastFilter || twomealsFilter || allmealsFilter) && MapUtils.isNotEmpty(groupRatePlanFilterConfMap)) {
			GroupRatePlanFilter groupMealRatePlanFilter = getGroupRatePlanFilter(groupRatePlanFilterConfMap, "MealOptions");
			if (freeBreakfastFilter) {
				boolean selected = filterCriteria != null && filterCriteria.stream().anyMatch(a -> "BREAKFAST_AVAIL".equalsIgnoreCase(a.getFilterValue()) || "BREAKFAST".equalsIgnoreCase(a.getFilterValue()));
				RatePlanFilter ratePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData("BREAKFAST_INCLUDED_FILTER_GI"), "FREE_BREAKFAST", "", selected);
				groupMealRatePlanFilter.getRatePlanFilterList().add(ratePlanFilter);
			}
			if (twomealsFilter) {
				boolean selected = filterCriteria != null && filterCriteria.stream().anyMatch(a -> "TWO_MEAL_AVAIL".equalsIgnoreCase(a.getFilterValue()));
				RatePlanFilter ratePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData((myPartner ? "BUSINESS_TWO_MEAL_AVAIL_TITLE_MYPARTNER" : "BREAKFAST_LUNCH_DINNER_INCLUDED")), "TWO_MEAL_AVAIL", "", selected);
				groupMealRatePlanFilter.getRatePlanFilterList().add(ratePlanFilter);
			}
			if (allmealsFilter) {
				boolean selected = filterCriteria != null && filterCriteria.stream().anyMatch(a -> "ALL_MEAL_AVAIL".equalsIgnoreCase(a.getFilterValue()));
				RatePlanFilter ratePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData((myPartner ? "BUSINESS_ALL_MEAL_AVAIL_TITLE_MYPARTNER" : "ALL_MEALS_INCLUDED")), "ALL_MEAL_AVAIL", "", selected);
				groupMealRatePlanFilter.getRatePlanFilterList().add(ratePlanFilter);
			}
			groupRatePlanFilterList.add(groupMealRatePlanFilter);
		}
		String region = MDC.get(MDCHelper.MDCKeys.REGION.getStringValue());
		if(!myPartner) {
			if ((pahFilter || fczpnFilter) && MapUtils.isNotEmpty(groupRatePlanFilterConfMap)) {
				GroupRatePlanFilter groupPayModeRatePlanFilter = getGroupRatePlanFilter(groupRatePlanFilterConfMap, "PayOptions");
				if (pahFilter) {
					boolean selected = filterCriteria != null && filterCriteria.stream().anyMatch(a -> "PAH".equalsIgnoreCase(a.getFilterValue()) || "PAH_AVAIL".equalsIgnoreCase(a.getFilterValue()));
					RatePlanFilter ratePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData("PAY_AT_HOTEL_FILTER"), "PAH", "", selected);
					groupPayModeRatePlanFilter.getRatePlanFilterList().add(ratePlanFilter);
				}
				if (fczpnFilter) {
					boolean selected = filterCriteria != null && filterCriteria.stream().anyMatch(a -> "PAY_LATER".equalsIgnoreCase(a.getFilterValue()));
					RatePlanFilter ratePlanFilter;
					if (isblockPAH) {
						ratePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData("PAY_LATER_FILTER"), "FCZPN", "", selected);
					} else {
						//GCC -> Free Cancellation Zero Payment Now
						if (AE.equalsIgnoreCase(region)) {
							ratePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData("FCZPN_FILTER"), "FCZPN", "", selected);
						}
						//BNPL_AT_1 -> Free Cancellation - Book @ ₹ 1
						else if (BNPLVariant.BNPL_AT_1.equals(bnplVariant)) {
							ratePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData("FCZPN_FILTER_BNPL_NEW_VARIANT"), "FCZPN", "", selected);
						}
						//BNPL_AT_0 -> Book with 0 Payment
						else if (BNPLVariant.BNPL_AT_0.equals(bnplVariant)) {
							ratePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData("FCZPN_FILTER_BNPL_ZERO_VARIANT_GI"), "FCZPN", "", selected);
						} else {
							ratePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData("FCZPN_FILTER"), "FCZPN", "", selected);
						}
					}
					groupPayModeRatePlanFilter.getRatePlanFilterList().add(ratePlanFilter);
				}
				groupRatePlanFilterList.add(groupPayModeRatePlanFilter);
			}
			if (specialDeals) {
				RatePlanFilter ratePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData("SPECIAL_DEALS_FILTER"), "SPECIALDEALS", "", false);
				ratePlanFilters.add(ratePlanFilter);
			}

			GroupRatePlanFilter filterForDevice = buildGroupFilterForDevice(groupRatePlanFilterConfMap, filterCriteria, staycation);
			if (staycation && filterForDevice != null) {
				groupRatePlanFilterList.add(filterForDevice);
			}
		}
//Remove MMT PACKAGE and MMT LUXE PACKAGE node value and set to PACKAGE_RATE, confirm with the client LUXE_PACKAGE and NON_LUXE_PACKAGE not required
		if (packageRoomFilter) {
			boolean selected = filterCriteria != null && filterCriteria.stream().anyMatch(
					a -> Constants.PACKAGE_RATE.equalsIgnoreCase(a.getFilterValue()));
			RatePlanFilter ratePlanFilter = null;
			if (isLuxeHotel) {
				ratePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData(FILTER_LUXE_PACKAGE_TEXT),
						Constants.PACKAGE_RATE, "", selected);
			} else {
				ratePlanFilter = new RatePlanFilter(polyglotService.getTranslatedData(FILTER_NON_LUXE_PACKAGE_TEXT),
						Constants.PACKAGE_RATE, "", selected);
			}
			ratePlanFilter.setOrder(1);
			ratePlanFilters.add(ratePlanFilter);
		}
		if (CollectionUtils.isEmpty(groupRatePlanFilterList))
			return null;
		for (GroupRatePlanFilter groupRatePlanFilter : groupRatePlanFilterList) {
			for (RatePlanFilter filter : groupRatePlanFilter.getRatePlanFilterList()) {
				if(!"PRIVATE_ROOMS".equalsIgnoreCase(filter.getCode())) {
					if (occupancyRooms != null && CollectionUtils.isNotEmpty(occupancyRooms)) {
						filter.setSelected(false);
					}
				}
			}
			// If there is only a single ratePlan in group, don't show it as a group
			// rather than show it as a single filter.
			if (CollectionUtils.isNotEmpty(groupRatePlanFilter.getRatePlanFilterList())
					&& groupRatePlanFilter.getRatePlanFilterList().size() == 1) {
				groupRatePlanFilter.setGroupApplicable(false);
				groupRatePlanFilter.setText(groupRatePlanFilter.getRatePlanFilterList().get(0).getTitle());
			}
		}

		return groupRatePlanFilterList;
	}
	private List<GroupRatePlanFilter> getCardFilters(List<RoomDetails> exactRooms, List<RoomDetails> occupancyRooms,
												 List<Filter> filterCriteria, String funnelSource, int ap, boolean isblockPAH,
												 BNPLVariant bnplVariant, CommonModifierResponse commonModifierResponse,
												 boolean isLuxeHotel, Map<String, GroupRatePlanFilter> groupRatePlanFilterConfMap,
												 String propertyType) {
		List<RoomDetails> rooms = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(exactRooms)) {
			rooms.addAll(exactRooms);
		}
		List<GroupRatePlanFilter> groupRatePlanFilters = new ArrayList<>();
		RoomUpsellConfig roomUpsellConfig = mobConfigHelper.getRoomUpsellConfig();

		if (roomUpsellConfig != null && roomUpsellConfig.roomViewsFilterToggle != null) {
			for(RoomViewFilterConfig roomViewFilterConfig: roomUpsellConfig.getRoomViewsFilterToggle()) {

				if (SelectRoomCardFilterType.AMENITIES.getName().equalsIgnoreCase(roomViewFilterConfig.getType())) {
					Optional<RoomDetails> filteredRoomOptional = rooms.stream().filter( roomItemDetail -> isAmenityAvailable(roomItemDetail, roomViewFilterConfig)).findFirst();
					if (filteredRoomOptional.isPresent()) {
						RoomDetails roomDetails = filteredRoomOptional.get();
						groupRatePlanFilters.add(getRoomUpsellGroupFilter(roomDetails, roomViewFilterConfig));
						break;
					}

				} else if (SelectRoomCardFilterType.ROOM_VIEW.getName().equalsIgnoreCase(roomViewFilterConfig.getType())) {
					Optional<RoomDetails> filteredRoomOptional = rooms.stream().filter( roomItemDetail -> Utility.convertToUnderscoreCaps(roomItemDetail.getRoomViewName()).equalsIgnoreCase(roomViewFilterConfig.getFilterCode())).findFirst();
					if (filteredRoomOptional.isPresent()) {
						RoomDetails roomDetails = filteredRoomOptional.get();
						groupRatePlanFilters.add(getRoomUpsellGroupFilter(roomDetails, roomViewFilterConfig));
						break;
					}
				}
			}
		}


		return groupRatePlanFilters;
	}

	private GroupRatePlanFilter getRoomUpsellGroupFilter(RoomDetails roomDetails, RoomViewFilterConfig roomViewFilterConfig) {
		GroupRatePlanFilter roomUpsellGroupFilter = new GroupRatePlanFilter();
		RatePlanFilter ratePlanFilter = new RatePlanFilter(roomDetails.getRoomViewName(), roomViewFilterConfig.getFilterCode(), roomViewFilterConfig.getIcon_URL(), false);
		List<RatePlanFilter> ratePlanFilterList = new ArrayList<>();
		ratePlanFilterList.add(ratePlanFilter);
		roomUpsellGroupFilter.setRatePlanFilterList(ratePlanFilterList);
		roomUpsellGroupFilter.setText(roomViewFilterConfig.getTitle());//roomViewsFilterToggle.getFilterText()
		roomUpsellGroupFilter.setImage(roomViewFilterConfig.getIcon_URL());
		roomUpsellGroupFilter.setGroupApplicable(false);
		return roomUpsellGroupFilter;
	}

	private Boolean isAmenityAvailable(RoomDetails roomDetails, RoomViewFilterConfig roomViewFilterConfig) {
		if (roomDetails.getAmenities() == null) {
			return false;
		}
		Optional<FacilityGroup> facilityGroupOptional = roomDetails.getAmenities().stream().filter(amenityItem -> {
            return Objects.equals(amenityItem.getId(), "SIGNATURE_AMENITIES");
		}).findFirst();
		if (facilityGroupOptional.isPresent()) {
			FacilityGroup facilityGroup = facilityGroupOptional.get();
			return facilityGroup.getFacilities().stream().anyMatch(facilityItem -> Utility.convertToUnderscoreCaps(facilityItem.getAttributeName()).equalsIgnoreCase(roomViewFilterConfig.getFilterCode()));
		}

		return false;
	}

	private List<String> amenitiesFilter(com.mmt.model.FacilityGroup facilityGroup) {
		RoomUpsellConfig roomUpsellConfig = mobConfigHelper.getRoomUpsellConfig();
		List<String> amenitiesFilter = new ArrayList<>();
		if (facilityGroup != null && roomUpsellConfig != null && roomUpsellConfig.roomViewsFilterToggle != null) {
			for (com.mmt.model.Facility facilityItem : facilityGroup.getFacilities()) {
				if (facilityItem.getAttributeName() != null) {
					if (roomUpsellConfig.roomViewsFilterToggle.stream().anyMatch(roomViewFilterConfig -> {
						return roomViewFilterConfig.getFilterCode().equalsIgnoreCase(Utility.convertToUnderscoreCaps(facilityItem.getAttributeName()));
					})) {
						amenitiesFilter.add(Utility.convertToUnderscoreCaps(facilityItem.getAttributeName()));
					}
				}
			}
		}
		return amenitiesFilter;
	}



	protected abstract GroupRatePlanFilter buildGroupFilterForDevice(Map<String, GroupRatePlanFilter> groupRatePlanFilterConfMap, List<Filter> filterCriteria, boolean staycation);

	public GroupRatePlanFilter getGroupRatePlanFilter(Map<String, GroupRatePlanFilter> groupRatePlanFilterConfMap, String key) {
		GroupRatePlanFilter groupMealRatePlanFilter = new GroupRatePlanFilter();
		groupMealRatePlanFilter.setRatePlanFilterList(new ArrayList<>());
		groupMealRatePlanFilter.setText(groupRatePlanFilterConfMap.get(key).getText());
		groupMealRatePlanFilter.setImage(groupRatePlanFilterConfMap.get(key).getImage());
		groupMealRatePlanFilter.setGroupApplicable(groupRatePlanFilterConfMap.get(key).isGroupApplicable());
		return groupMealRatePlanFilter;
	}

	public GroupRatePlanFilter buildStaycationFilter(Map<String, GroupRatePlanFilter> groupRatePlanFilterConfMap, List<Filter> filterCriteria, boolean staycation) {
		if (staycation) {
			GroupRatePlanFilter groupStaycationRatePlanFilter = getGroupRatePlanFilter(groupRatePlanFilterConfMap, "GetawayDeals");
			boolean selected = filterCriteria != null && filterCriteria.stream().anyMatch(a -> "STAYCATION_DEALS".equalsIgnoreCase(a.getFilterValue()));
			RatePlanFilter ratePlanFilter = new RatePlanFilter(groupStaycationRatePlanFilter.getText(), "STAYCATION", "", selected);
			groupStaycationRatePlanFilter.getRatePlanFilterList().add(ratePlanFilter);
			return groupStaycationRatePlanFilter;
		}
		return null;
	}

	private void updatePackageInclusionBaseRatePlanName(Map<String, String> ratePlanCodeAndNameMap, List<RoomDetails> packageRooms){
		if(CollectionUtils.isEmpty(packageRooms)){
			return;
		}
		for(RoomDetails roomDetails: packageRooms){
			if(CollectionUtils.isEmpty(roomDetails.getRatePlans())){
				continue;
			}
			for(SelectRoomRatePlan roomRatePlan: roomDetails.getRatePlans()){
				PackageSelectRoomRatePlan packageSelectRoomRatePlan =  (PackageSelectRoomRatePlan) roomRatePlan;
				if(packageSelectRoomRatePlan.getPackageInclusionDetails() != null){
					String name = ratePlanCodeAndNameMap.get(packageSelectRoomRatePlan.getPackageInclusionDetails().getBaseRatePlanCode());
					if(StringUtils.isNotBlank(name)){
						packageSelectRoomRatePlan.getPackageInclusionDetails().setBaseRatePlanName(name);
					}

				}
			}
		}
	}

	private List<RoomDetails> getRooms(HotelRates hotelRates, RoomTypeDetails roomTypeDetails, HotelsRoomInfoResponseEntity hotelsRoomInfoResponseEntity,
									   HotelImage hotelImage, String listingType, Map<String, String> expData,
									   boolean ratePlanGroup, String askedCurrency, String funnelSource,
									   WalletSurge walletSurge, com.mmt.hotels.model.response.searchwrapper.Segments segments,
									   int days, Integer hotelStarRating, int ap, boolean isBlockPAH, Map<String, JsonNode> roomPersuasionMap,
									   CommonModifierResponse commonModifierResponse, boolean isPackageRoom, Map<String, String> ratePlanCodeAndNameMap,
									   boolean isLuxeHotel, boolean isAltAccoHotel, boolean isUserGCCAndMmtExclusive, boolean groupBookingPrice,
									   boolean isRecommendation, BlackInfo blackInfo, String propertyType, boolean isOccassion) {

		if (roomTypeDetails == null || roomTypeDetails.getRoomType() == null)
			return null;

		String linkedRateExperimentValue = getExperimentValue(commonModifierResponse, Constants.LINKED_RATE_EXPERIMENT_NR);

		/* START <RoomCode,ImageURLsList> map */
		Map<String, List<String>> roomImageMap = new HashMap<>();
		if (hotelImage != null && hotelImage.getImageDetails() != null && MapUtils.isNotEmpty(hotelImage.getImageDetails().getProfessional())
			&& hotelImage.getImageDetails().getProfessional().containsKey("R")) {
			List<ProfessionalImageEntity> images = hotelImage.getImageDetails().getProfessional().get("R");
			if (CollectionUtils.isNotEmpty(images)) {
				images.forEach( image -> {
					if (StringUtils.isNotBlank(image.getUrl())) {
						roomImageMap.computeIfAbsent(image.getCatCode(), k -> new ArrayList<>());
						roomImageMap.get(image.getCatCode()).add(image.getUrl().startsWith("http") ? image.getUrl() : "https:" + image.getUrl());
					}
				});
			}
		}
		/* END <RoomCode,ImageURLsList> map */

		List<RoomDetails> roomDetail = new ArrayList<>();
		for (String roomCode : roomTypeDetails.getRoomType().keySet()) {
			RoomDetails roomDetails = new RoomDetails();
			if(isPackageRoom){
				roomDetails = new PackageRoomDetails();
			}
			if(isOccassion) {
				roomDetails = new PackageRoomDetails();
				if (roomTypeDetails.getOccassionDetails() != null) {
					((PackageRoomDetails) roomDetails).setType(roomTypeDetails.getOccassionDetails().getOccassionType());
				}
			}
			roomDetails.setRoomCode(roomCode);
			roomDetails.setRoomName(roomTypeDetails.getRoomType().get(roomCode).getRoomTypeName());
			roomDetails.setRoomReviews(roomTypeDetails.getRoomType().get(roomCode).getRoomReviews());

			if(MapUtils.isNotEmpty(roomPersuasionMap)){
				roomDetails.setRoomPersuasions(roomPersuasionMap.get(roomCode));
			}
			Map<String, RoomInfo> staticRoomInfoMap = new HashMap<>();
			if (null!=hotelsRoomInfoResponseEntity && hotelsRoomInfoResponseEntity.getHtlRmInfo()!=null && CollectionUtils.isNotEmpty(hotelsRoomInfoResponseEntity.getHtlRmInfo()))
				staticRoomInfoMap = hotelsRoomInfoResponseEntity.getHtlRmInfo().get(0).getHotelRoomInfo();
			RoomInfo roomInfo = MapUtils.isNotEmpty(staticRoomInfoMap) ? staticRoomInfoMap.get(roomCode) : null;
			String sellableType = null;
			if (roomInfo != null &&  null != roomInfo.getRoomAttributes())
				sellableType = roomInfo.getRoomAttributes().getSellableType();
			String roomViewType = null;
			if (roomInfo != null &&  null != roomInfo.getRoomViewName())
				roomViewType = roomInfo.getRoomViewName();
			List<String> amenitiesFilterCodes = new ArrayList<>();
			if (roomInfo != null &&  null != roomInfo.getHighlightedFacilities() && roomInfo.getHighlightedFacilities().size() > 0) {
				amenitiesFilterCodes = amenitiesFilter(roomInfo.getHighlightedFacilities().get(0));
			}


			/*
			 * myPartner change log : commonModifierResponse floated down
			 * */
			List<com.mmt.hotels.clientgateway.response.rooms.SelectRoomRatePlan> ratePlans = getRatePlans(hotelRates,roomTypeDetails.getRoomType().get(roomCode), listingType,
									expData, ratePlanGroup, askedCurrency, sellableType,funnelSource,
									days, ap , isBlockPAH,commonModifierResponse, isPackageRoom, ratePlanCodeAndNameMap, isLuxeHotel, isUserGCCAndMmtExclusive, groupBookingPrice, blackInfo, isOccassion,roomViewType, amenitiesFilterCodes);

			if(LINKED_RATE_EXP_ONE_VARIANT.equalsIgnoreCase(linkedRateExperimentValue)){
				linkRatePlans(ratePlans);
			}
			roomDetails.setRatePlans(ratePlans);
			roomDetails.setClientViewType(getTariffViewType(roomDetails,hotelStarRating,roomDetail.size()<1,expData));
			roomDetails.setImages(roomImageMap.get(roomCode));
			roomDetails.setRoomCategoryText(LISTING_TYPE_ENTIRE.equalsIgnoreCase(listingType)
					?(propertyType != null ? propertyType : SELLABLE_ROOM_TYPE)
					:(sellableType != null ? sellableType : SELLABLE_ROOM_TYPE));
			roomDetails.setMedia(populateMedia(staticRoomInfoMap, roomDetails.getImages() , roomCode));

			ExtraGuestDetail extraGuestDetail = null;
			if(  MapUtils.isNotEmpty(roomTypeDetails.getRoomType().get(roomCode).getRatePlanList()))
			{
				 RatePlan ratePlan = roomTypeDetails.getRoomType().get(roomCode).getRatePlanList().values().stream().findFirst().orElse(null);
				 if(ratePlan!=null && ratePlan.getExtraGuestDetail()!=null){
					 extraGuestDetail = ratePlan.getExtraGuestDetail();
				 }

				if(utility.isSPKGExperimentOn(expData) && isPackageRoom && ratePlan!=null) {
					roomDetails.setIconUrl(elitePackageIconUrlUpdated);
					roomDetails.setBorderGradient(getBorderGradientForOccasion());
					roomDetails.setPackageBenefits(buildPackageBenefitsText(((PackageRoomRatePlan) ratePlan).getPackageInclusionDetails(), StringUtils.EMPTY));
					if (roomTypeDetails.getOccassionDetails() != null) {
						roomDetails.setBgStyle(getBgStyleforOccassion(roomTypeDetails.getOccassionDetails().getBgGradient()));
					}
				}

				if(utility.isShowOccassionPackagesPlanExperimentOn(expData) && isOccassion
						&& ratePlan != null && roomTypeDetails.getOccassionDetails() != null) {
					roomDetails.setIconUrl(roomTypeDetails.getOccassionDetails().getRecommendedRoomsIconUrl());
					roomDetails.setPackageImageUrl(roomTypeDetails.getOccassionDetails().getPackagingImageUrl());
					roomDetails.setPackageBenefits(buildPackageBenefitsText(((PackageRoomRatePlan) ratePlan).getPackageInclusionDetails(), roomTypeDetails.getOccassionDetails().getOccassionType()));
					roomDetails.setBorderGradient(getBorderGradientForOccasion());
					roomDetails.setBgStyle(getBgStyleforOccassion(roomTypeDetails.getOccassionDetails().getBgGradient()));
				}

			}


			if (roomInfo != null) {
				roomDetails.setAmenities(commonResponseTransformer.buildAmenities(
						roomInfo.getFacilityWithGrp(), roomInfo.getStarFacilities()));
				roomDetails.setHighlightedAmenities(commonResponseTransformer.buildHighlightedAmenities(roomInfo.getFacilityHighlights()));
				roomDetails.setMaster(roomInfo.isMaster());
				roomDetails.setMaxAdult(roomInfo.getMaxAdultCount());
				roomDetails.setMaxChild(roomInfo.getMaxChildCount());
				roomDetails.setMaxGuest(roomInfo.getMaxGuestCount());
				/* Below three nodes are duplicate & need to be removed after next client release */
				/* Only Temp addition to fix live bug */
				roomDetails.setMaxGuestCount(roomInfo.getMaxGuestCount());
				roomDetails.setMaxAdultCount(roomInfo.getMaxAdultCount());
				roomDetails.setMaxChildCount(roomInfo.getMaxChildCount());
				roomDetails.setBathroomCount(roomInfo.getBathroomCount());
				/* Above three nodes are duplicate & need to be removed after next client release */
				roomDetails.setBedCount(roomInfo.getBedCount());
				if (StringUtils.isNotBlank(roomInfo.getBedRoomCount()))
					roomDetails.setBedroomCount(NumberUtils.toInt(roomInfo.getBedRoomCount(),0));
				roomDetails.setParentRoomCode(roomInfo.getParentRoomCode());
				roomDetails.setRoomSize(roomInfo.getRoomSize());
				roomDetails.setRoomViewName(roomInfo.getRoomViewName());
				roomDetails.setRoomHighlights(getRoomHighlights(roomInfo, extraGuestDetail, isAltAccoHotel,utility.isExperimentTrue(expData, ExperimentKeys.PILGR_IMAGE_BED_INFO.getKey())));
				roomDetails.setBeds(roomInfo.getBeds());
				roomDetails.setBedInfoText(roomInfo.getBedInfoText());
				roomDetails.setRoomName(roomInfo.getRoomName());
				roomDetails.setWalletSurge(getWalletSurge(walletSurge));
				roomDetails.setSegments(getSegments(segments));
				roomDetails.setExtraGuestInfo(getExtraGuestInfo(extraGuestDetail));
				if(roomDetails.getMaster()!= null && roomDetails.getMaster()){
					roomDetails.setSubRoomDetails(populateSubRooms(roomCode, staticRoomInfoMap));
				}
				roomDetails.setUsp(roomInfo.getUsp());
				roomDetails.setDescription(roomInfo.getDescription());
				roomDetails.setRoomSummary(roomInfo.getRoomSummary());
				if (roomDetails.getRoomSummary() != null && roomDetails.getRoomSummary().isTopRated()) {
					PersuasionObject persuasionObject =  createTopRatedPersuasion();
					addPersuasion(roomDetails, persuasionObject);
				}
				roomDetails.setPrivateSpaces(getSpaceData(roomInfo.getPrivateSpaces(), roomTypeDetails.getRoomType().get(roomCode), expData));
				roomDetails.setExtraBedCount(roomDetails.getPrivateSpaces() != null ? roomDetails.getPrivateSpaces().getExtraBeds() : 0);
				roomDetails.setBaseGuest(roomDetails.getPrivateSpaces() != null ? roomDetails.getPrivateSpaces().getBaseGuests() : 0);
				buildStayDetails(roomDetails);
				roomDetails.setBaseRoom(roomTypeDetails.getRoomType().get(roomCode).isBaseRoom());
			}
			if (isPackageRoom || isOccassion) {
				setPackageRoomSpecificInfo((PackageRoomDetails) roomDetails,
						(PackageRoomType) roomTypeDetails.getRoomType().get(roomCode));
			}
			if (utility.isExperimentOn(expData, SELECT_ROOM_REVAMP_EXP_KEY)) {
				selectRoomV2ResponseTransformer.alterRoomDetailsForSelectRoomRevamp(roomDetails, propertyType);
			}
			roomDetail.add(roomDetails);
		}
		// Conditions for enabling upsellDetails are mentioned below:
		// 1. Should be Alt Acco property
		// 2. Experiment must be on.
		// 3. For Regular Pax :: 1 room, 3 rateplans only. More than 3 then we should show select room page.
		// 4. Multi Pax :: Only for 1 room type, 1 rateplan. Then only skip select room should be enaled.
		// 5. All null pointers
		boolean isUpSellExperimentEnalbed = MapUtils.isNotEmpty(expData) && expData.get(ALT_ACCO_SELECT_ROOM_EXP) != null && StringUtils.equalsIgnoreCase(expData.get(ALT_ACCO_SELECT_ROOM_EXP),"true");
		boolean doesPropertyContainOnlyOneRoomType = roomDetail.size() == 1;
		boolean doesNumOfRatePlansExceedProvidedThreshold = roomDetail.get(0).getRatePlans().size()-1 > SKIP_SELECT_ROOM_THRESHOLD;
		// Possible rooms, rateplan combinations below:
		// 1. 1 room, x rateplans = skip select rooms can be true if x <= 3.
		// 2. n rooms, x rateplans = skip select room is false.
		// Combo cases
		// 3. 1 room, made into n rooms, 1 rateplan = skip select room can be true.
		// 4. 1 room, made into n rooms, x rateplans = skip select room is false.
		// 5. x rooms, made into n rooms, x rateplans = skip select room is false.
		if (isRecommendation && roomDetail.get(0).getRatePlans().size() == 1 && isUpSellExperimentEnalbed && isAltAccoHotel) {
			// In this case only flag is set. UpSellDetails node is not sent from BE.
			commonModifierResponse.setSkipRoomSelectEnabled(true);
		} else {
			commonModifierResponse.setSkipRoomSelectEnabled(false);
		}
		if(isUpSellExperimentEnalbed && isAltAccoHotel && doesPropertyContainOnlyOneRoomType && !doesNumOfRatePlansExceedProvidedThreshold && !isRecommendation) {
			updateUpSellOptions(roomDetail.get(0).getRatePlans(), commonModifierResponse);
		}
		if(roomDetail.get(0).getRatePlans().size() == 1 && !isPackageRoom && !isOccassion && doesPropertyContainOnlyOneRoomType && !isRecommendation) {
			commonModifierResponse.setSkipRoomSelectEnabled(true);
		}

		// As a part of select room revamp, skip select room logic is changed.
		if (utility.isExperimentOn(expData, SELECT_ROOM_V2_SKIP_EXP_KEY)) {
			commonModifierResponse.setSkipRoomSelectEnabled(selectRoomV2ResponseTransformer.shouldSkipSelectRoom(
					roomDetail, listingType, isRecommendation, expData
			));
		}
		// In case of entire properties with a single room type, we need to hide the room details.
		if (utility.isExperimentOn(expData, SELECT_ROOM_REVAMP_EXP_KEY)) {
			// In case of entire properties having exact rooms cases, hide room details if roomSize is 1
			// and ratePlans are multiple
			if (LISTING_TYPE_ENTIRE.equalsIgnoreCase(listingType) && !isRecommendation
					&& roomDetail.size() == 1 && roomDetail.get(0).getRatePlans().size() > 1) {
				roomDetail.get(0).setHideRoomDetails(true);
			}
		}
		return roomDetail;
	}

	private String buildPackageBenefitsText(com.mmt.hotels.model.response.pricing.PackageInclusionDetails packageInclusionDetails, String occassionType) {
		String packageBenefitsText = polyglotService.getTranslatedData(ConstantsTranslation.PACKAGE_BENEFITS_TEXT_GI);
		if(packageInclusionDetails!= null && StringUtils.isNotEmpty(packageInclusionDetails.getPackageBenefitsSlashedPrice()) && StringUtils.isNotEmpty(packageInclusionDetails.getPackageBenefitsPrice())) {
			packageBenefitsText = packageBenefitsText.replace("{1}", String.valueOf((int)Double.parseDouble(packageInclusionDetails.getPackageBenefitsSlashedPrice())));
			packageBenefitsText = packageBenefitsText.replace("{2}", String.valueOf((int)Double.parseDouble(packageInclusionDetails.getPackageBenefitsPrice())));
			return packageBenefitsText;
		}
		if(StringUtils.isNotBlank(occassionType)) {
			return polyglotService.getTranslatedData(occassionType.concat(UNDERSCORE).concat(ConstantsTranslation.OCCASION_PACKAGE_BENEFITS_TEXT));
		}
		return polyglotService.getTranslatedData(ConstantsTranslation.PACKAGE_BENEFITS_DEFAULT_TEXT_GI);
	}

	private BgStyle getBgStyleforOccassion(com.mmt.hotels.model.persuasion.response.BgGradient bgGradient) {
		if(Objects.nonNull(bgGradient)) {
			BgStyle bgStyle = new BgStyle();
			bgStyle.setStart(bgGradient.getStart());
			bgStyle.setCenter(bgGradient.getCenter());
			bgStyle.setEnd(bgGradient.getEnd());
			bgStyle.setDirection("diagonalBottom");
			bgStyle.setAngle("210deg");
			return bgStyle;
		}
		return null;
	}

	private BorderGradient getBorderGradientForOccasion() {
		BorderGradient borderGradient = new BorderGradient();
		borderGradient.setStart("#d8d8d8");
		borderGradient.setEnd("#d8d8d8");
		borderGradient.setDirection("horizontal");
		return borderGradient;
	}

	private void updateUpSellOptions(List<SelectRoomRatePlan> ratePlans, CommonModifierResponse commonModifierResponse) {
		SelectRoomRatePlan lowestRate = null;
		double baseRatePlanSellingPrice = 0;
		for (SelectRoomRatePlan ratePlan : ratePlans) {
			if(lowestRate == null) {
				lowestRate = ratePlan;
				if(CollectionUtils.isNotEmpty(ratePlan.getTariffs()) && ratePlan.getTariffs().get(0).getPriceMap() != null) {
					for (Map.Entry<String, TotalPricing> entry : ratePlan.getTariffs().get(0).getPriceMap().entrySet()) {
						// 2 cases are possible here.
						// 1. When no coupon is present. We pick the price of the default.
						// 2. When one or more coupons are there, we pick the auto applicable coupon which Manthan sends.
						if (CollectionUtils.isNotEmpty(entry.getValue().getDetails()) && (entry.getValue().getCoupons() == null || entry.getValue().getCoupons().get(0).isAutoApplicable())) {
							Optional<com.mmt.hotels.clientgateway.response.PricingDetails> totalPrice = entry.getValue().getDetails().stream().filter(obj -> StringUtils.equalsIgnoreCase(obj.getKey(), TOTAL_AMOUNT_KEY)).findFirst();
							Optional<com.mmt.hotels.clientgateway.response.PricingDetails> taxesAndFees = entry.getValue().getDetails().stream().filter(obj -> StringUtils.equalsIgnoreCase(obj.getKey(), TAXES_KEY)).findFirst();
							if(totalPrice.isPresent() && taxesAndFees.isPresent())
								baseRatePlanSellingPrice = totalPrice.get().getAmount() + taxesAndFees.get().getAmount();
						}
					}
				}
				continue;
			}

			if(CollectionUtils.isNotEmpty(ratePlan.getTariffs()) && ratePlan.getTariffs().get(0).getPriceMap() != null) {
				for (Map.Entry<String, TotalPricing> entry : ratePlan.getTariffs().get(0).getPriceMap().entrySet()) {
					if (CollectionUtils.isNotEmpty(entry.getValue().getDetails()) && (CollectionUtils.isEmpty(entry.getValue().getCoupons()) || entry.getValue().getCoupons().get(0).isAutoApplicable())) {
						Optional<com.mmt.hotels.clientgateway.response.PricingDetails> totalPrice = entry.getValue().getDetails().stream().filter(obj -> StringUtils.equalsIgnoreCase(obj.getKey(), TOTAL_AMOUNT_KEY)).findFirst();
						Optional<com.mmt.hotels.clientgateway.response.PricingDetails> taxesAndFees = entry.getValue().getDetails().stream().filter(obj -> StringUtils.equalsIgnoreCase(obj.getKey(), TAXES_KEY)).findFirst();
						double currentRatePlanSellingPrice = 0;
						if(totalPrice.isPresent() && taxesAndFees.isPresent())
							currentRatePlanSellingPrice = totalPrice.get().getAmount() + taxesAndFees.get().getAmount();

						String mealPlanCheapest = lowestRate.getMealPlanCode();
						boolean cancellationIncludedCheapest = Constants.CANCELLATION_TYPE_FC.equalsIgnoreCase(lowestRate.getCancellationPolicy().getType().name()) || Constants.CANCELLATION_TYPE_FCZPN.equalsIgnoreCase(lowestRate.getCancellationPolicy().getType().name());
						String mealPlanCurrent = ratePlan.getMealPlanCode();
						boolean cancellationIncludedCurrent = Constants.CANCELLATION_TYPE_FC.equalsIgnoreCase(ratePlan.getCancellationPolicy().getType().name()) || Constants.CANCELLATION_TYPE_FCZPN.equalsIgnoreCase(ratePlan.getCancellationPolicy().getType().name());
						if(StringUtils.isNotBlank(mealPlanCheapest) && StringUtils.isNotBlank(mealPlanCurrent)) {
							UpSellDetails upSellData = buildUpSellDetails(mealPlanCheapest, cancellationIncludedCheapest, mealPlanCurrent, cancellationIncludedCurrent, (currentRatePlanSellingPrice - baseRatePlanSellingPrice));
							if(upSellData != null && StringUtils.isNotEmpty(upSellData.getTitle())) {
								ratePlan.getTariffs().get(0).setUpSellDetails(upSellData);
								commonModifierResponse.setSkipRoomSelectEnabled(true);
							}
						}
					}
				}
			}
		}
	}

	public UpSellDetails buildUpSellDetails(String cheapestRatePlanMealPlanCode, boolean cheapestRatePlanFreeCancellationIncluded, String currentRatePlanMealPlanCode, boolean currentRatePlanFreeCancellationIncluded, double diffAmount) {
		if(diffAmount == 0 || StringUtils.isEmpty(cheapestRatePlanMealPlanCode) || StringUtils.isEmpty(currentRatePlanMealPlanCode) )
			return null;
		UpSellDetails upSellData = new UpSellDetails();
		int amount = (int)Math.round(diffAmount);
		if(cheapestRatePlanFreeCancellationIncluded) { // No need to upsell free cancellation in this case.
			if(cheapestRatePlanMealPlanCode.equalsIgnoreCase("EP") || cheapestRatePlanMealPlanCode.equalsIgnoreCase("AO") || cheapestRatePlanMealPlanCode.equalsIgnoreCase("BD") ) {
				if (currentRatePlanMealPlanCode.equalsIgnoreCase("CP") || currentRatePlanMealPlanCode.equalsIgnoreCase("CB")) {
					upSellData.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_BREAKFAST_DETAILS_PAGE_TITLE).replace("{amount}", String.valueOf(amount)));
					upSellData.setSubTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_DETAILS_PAGE_SUBTITLE));
					upSellData.setSelectedTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_BREAKFAST_ADDED_DETAILS_PAGE_TITLE));
				} else if (currentRatePlanMealPlanCode.equalsIgnoreCase("MAP") || currentRatePlanMealPlanCode.equalsIgnoreCase("SMAP")  || currentRatePlanMealPlanCode.equalsIgnoreCase("TMAP") ) {
					upSellData.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_BREAKFAST_AND_LUNCH_OR_DINNER_DETAILS_PAGE_TITLE).replace("{amount}", String.valueOf(amount)));
					upSellData.setSubTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_DETAILS_PAGE_SUBTITLE));
					upSellData.setSelectedTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_BREAKFAST_AND_LUNCH_OR_DINNER_ADDED_DETAILS_PAGE_TITLE));
				} else if (currentRatePlanMealPlanCode.equalsIgnoreCase("AP")){
					upSellData.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_ALL_MEALS_DETAILS_PAGE_TITLE).replace("{amount}", String.valueOf(amount)));
					upSellData.setSubTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_DETAILS_PAGE_SUBTITLE));
					upSellData.setSelectedTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_ALL_MEALS_ADDED_DETAILS_PAGE_TITLE));
				}
			} else if (cheapestRatePlanMealPlanCode.equalsIgnoreCase("CP") || cheapestRatePlanMealPlanCode.equalsIgnoreCase("CB")) {
				if (currentRatePlanMealPlanCode.equalsIgnoreCase("MAP") || currentRatePlanMealPlanCode.equalsIgnoreCase("SMAP") || currentRatePlanMealPlanCode.equalsIgnoreCase("TMAP")) {
					upSellData.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_LUNCH_OR_DINNER_DETAILS_PAGE_TITLE).replace("{amount}", String.valueOf(amount)));
					upSellData.setSubTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_DETAILS_PAGE_SUBTITLE));
					upSellData.setSelectedTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_LUNCH_OR_DINNER_ADDED_DETAILS_PAGE_TITLE));
				} else if (currentRatePlanMealPlanCode.equalsIgnoreCase("AP")){
					upSellData.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_ALL_MEALS_DETAILS_PAGE_TITLE).replace("{amount}", String.valueOf(amount)));
					upSellData.setSubTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_DETAILS_PAGE_SUBTITLE));
					upSellData.setSelectedTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_ALL_MEALS_ADDED_DETAILS_PAGE_TITLE));
				}
			} else if (cheapestRatePlanMealPlanCode.equalsIgnoreCase("MAP") || cheapestRatePlanMealPlanCode.equalsIgnoreCase("SMAP") || cheapestRatePlanMealPlanCode.equalsIgnoreCase("TMAP")) {
				if (currentRatePlanMealPlanCode.equalsIgnoreCase("AP")){
					upSellData.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_ALL_MEALS_DETAILS_PAGE_TITLE).replace("{amount}", String.valueOf(amount)));
					upSellData.setSubTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_DETAILS_PAGE_SUBTITLE));
					upSellData.setSelectedTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_ALL_MEALS_ADDED_DETAILS_PAGE_TITLE));
				}
			} else if (cheapestRatePlanMealPlanCode.equalsIgnoreCase("AP")){
				// Nothing to upsell.
			}
		} else { // Need to upsell free cancellation as well in this case.
			if(cheapestRatePlanMealPlanCode.equalsIgnoreCase("EP") || cheapestRatePlanMealPlanCode.equalsIgnoreCase("AO") || cheapestRatePlanMealPlanCode.equalsIgnoreCase("BD") ) {
				if ((currentRatePlanMealPlanCode.equalsIgnoreCase("EP") || currentRatePlanMealPlanCode.equalsIgnoreCase("AO") || currentRatePlanMealPlanCode.equalsIgnoreCase("BD") ) && currentRatePlanFreeCancellationIncluded) {
					upSellData.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_FREE_CANCELLATION_DETAILS_PAGE_TITLE).replace("{amount}", String.valueOf(amount)));
					upSellData.setSubTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_DETAILS_PAGE_SUBTITLE));
					upSellData.setSelectedTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_FREE_CANCELLATION_ADDED_DETAILS_PAGE_TITLE));
				} else if (currentRatePlanMealPlanCode.equalsIgnoreCase("CP") || currentRatePlanMealPlanCode.equalsIgnoreCase("CB")) {
					if(currentRatePlanFreeCancellationIncluded) {
						upSellData.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_BREAKFAST_AND_FREE_CANCELLATION_DETAILS_PAGE_TITLE).replace("{amount}", String.valueOf(amount)));
						upSellData.setSubTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_DETAILS_PAGE_SUBTITLE));
						upSellData.setSelectedTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_BREAKFAST_AND_FREE_CANCELLATION_ADDED_DETAILS_PAGE_TITLE));
					} else {
						upSellData.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_BREAKFAST_DETAILS_PAGE_TITLE).replace("{amount}", String.valueOf(amount)));
						upSellData.setSubTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_DETAILS_PAGE_SUBTITLE));
						upSellData.setSelectedTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_BREAKFAST_ADDED_DETAILS_PAGE_TITLE));
					}
				} else if (currentRatePlanMealPlanCode.equalsIgnoreCase("MAP") || currentRatePlanMealPlanCode.equalsIgnoreCase("SMAP") || currentRatePlanMealPlanCode.equalsIgnoreCase("TMAP")) {
					if(currentRatePlanFreeCancellationIncluded) {
						upSellData.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_BREAKFAST_AND_LUNCH_OR_DINNER_AND_FREE_CANCELLATION_DETAILS_PAGE_TITLE).replace("{amount}", String.valueOf(amount)));
						upSellData.setSubTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_DETAILS_PAGE_SUBTITLE));
						upSellData.setSelectedTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_BREAKFAST_AND_LUNCH_OR_DINNER_AND_FREE_CANCELLATION_ADDED_DETAILS_PAGE_TITLE));
					} else {
						upSellData.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_BREAKFAST_AND_LUNCH_OR_DINNER_DETAILS_PAGE_TITLE).replace("{amount}", String.valueOf(amount)));
						upSellData.setSubTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_DETAILS_PAGE_SUBTITLE));
						upSellData.setSelectedTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_BREAKFAST_AND_LUNCH_OR_DINNER_ADDED_DETAILS_PAGE_TITLE));
					}
				} else if (currentRatePlanMealPlanCode.equalsIgnoreCase("AP")){
					if(currentRatePlanFreeCancellationIncluded) {
						upSellData.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_ALL_MEALS_AND_FREE_CANCELLATION_DETAILS_PAGE_TITLE).replace("{amount}", String.valueOf(amount)));
						upSellData.setSubTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_DETAILS_PAGE_SUBTITLE));
						upSellData.setSelectedTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_ALL_MEALS_AND_FREE_CANCELLATION_ADDED_DETAILS_PAGE_TITLE));
					} else {
						upSellData.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_ALL_MEALS_DETAILS_PAGE_TITLE).replace("{amount}", String.valueOf(amount)));
						upSellData.setSubTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_DETAILS_PAGE_SUBTITLE));
						upSellData.setSelectedTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_ALL_MEALS_ADDED_DETAILS_PAGE_TITLE));
					}
				}
			} else if (cheapestRatePlanMealPlanCode.equalsIgnoreCase("CP") || cheapestRatePlanMealPlanCode.equalsIgnoreCase("CB")) {
				if ((currentRatePlanMealPlanCode.equalsIgnoreCase("CP") || currentRatePlanMealPlanCode.equalsIgnoreCase("CB")) && currentRatePlanFreeCancellationIncluded) {
					upSellData.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_FREE_CANCELLATION_DETAILS_PAGE_TITLE).replace("{amount}", String.valueOf(amount)));
					upSellData.setSubTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_DETAILS_PAGE_SUBTITLE));
					upSellData.setSelectedTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_FREE_CANCELLATION_ADDED_DETAILS_PAGE_TITLE));
				} else if (currentRatePlanMealPlanCode.equalsIgnoreCase("MAP") || currentRatePlanMealPlanCode.equalsIgnoreCase("SMAP") || currentRatePlanMealPlanCode.equalsIgnoreCase("TMAP")) {
					if(currentRatePlanFreeCancellationIncluded) {
						upSellData.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_LUNCH_OR_DINNER_AND_FREE_CANCELLATION_DETAILS_PAGE_TITLE).replace("{amount}", String.valueOf(amount)));
						upSellData.setSubTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_DETAILS_PAGE_SUBTITLE));
						upSellData.setSelectedTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_LUNCH_OR_DINNER_AND_FREE_CANCELLATION_ADDED_DETAILS_PAGE_TITLE));
					} else {
						upSellData.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_LUNCH_OR_DINNER_DETAILS_PAGE_TITLE).replace("{amount}", String.valueOf(amount)));
						upSellData.setSubTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_DETAILS_PAGE_SUBTITLE));
						upSellData.setSelectedTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_LUNCH_OR_DINNER_ADDED_DETAILS_PAGE_TITLE));
					}
				} else if (currentRatePlanMealPlanCode.equalsIgnoreCase("AP")){
					if(currentRatePlanFreeCancellationIncluded) {
						upSellData.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_ALL_MEALS_AND_FREE_CANCELLATION_DETAILS_PAGE_TITLE).replace("{amount}", String.valueOf(amount)));
						upSellData.setSubTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_DETAILS_PAGE_SUBTITLE));
						upSellData.setSelectedTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_ALL_MEALS_AND_FREE_CANCELLATION_ADDED_DETAILS_PAGE_TITLE));
					} else {
						upSellData.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_ALL_MEALS_DETAILS_PAGE_TITLE).replace("{amount}", String.valueOf(amount)));
						upSellData.setSubTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_DETAILS_PAGE_SUBTITLE));
						upSellData.setSelectedTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_ALL_MEALS_ADDED_DETAILS_PAGE_TITLE));
					}
				}
			} else if (cheapestRatePlanMealPlanCode.equalsIgnoreCase("MAP") || cheapestRatePlanMealPlanCode.equalsIgnoreCase("SMAP") || cheapestRatePlanMealPlanCode.equalsIgnoreCase("TMAP")) {
				if (currentRatePlanMealPlanCode.equalsIgnoreCase("MAP") && currentRatePlanFreeCancellationIncluded) {
					upSellData.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_FREE_CANCELLATION_DETAILS_PAGE_TITLE).replace("{amount}", String.valueOf(amount)));
					upSellData.setSubTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_DETAILS_PAGE_SUBTITLE));
					upSellData.setSelectedTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_FREE_CANCELLATION_ADDED_DETAILS_PAGE_TITLE));
				}else if (currentRatePlanMealPlanCode.equalsIgnoreCase("AP")){
					if(currentRatePlanFreeCancellationIncluded) {
						upSellData.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_ALL_MEALS_AND_FREE_CANCELLATION_DETAILS_PAGE_TITLE).replace("{amount}", String.valueOf(amount)));
						upSellData.setSubTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_DETAILS_PAGE_SUBTITLE));
						upSellData.setSelectedTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_ALL_MEALS_AND_FREE_CANCELLATION_ADDED_DETAILS_PAGE_TITLE));
					} else {
						upSellData.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_ALL_MEALS_DETAILS_PAGE_TITLE).replace("{amount}", String.valueOf(amount)));
						upSellData.setSubTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_DETAILS_PAGE_SUBTITLE));
						upSellData.setSelectedTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_ALL_MEALS_ADDED_DETAILS_PAGE_TITLE));
					}
				}
			} else if (cheapestRatePlanMealPlanCode.equalsIgnoreCase("AP")){
				if (currentRatePlanMealPlanCode.equalsIgnoreCase("AP") && currentRatePlanFreeCancellationIncluded) {
					upSellData.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_FREE_CANCELLATION_DETAILS_PAGE_TITLE).replace("{amount}", String.valueOf(amount)));
					upSellData.setSubTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_DETAILS_PAGE_SUBTITLE));
					upSellData.setSelectedTitle(polyglotService.getTranslatedData(ConstantsTranslation.UPSELL_FREE_CANCELLATION_ADDED_DETAILS_PAGE_TITLE));
				}
			}
		}
		return upSellData;
	}

	private void buildGroupBookingComboText(RoomDetails roomDetails, RecommendedCombo recommendedCombo, boolean baseCombo, String funnelSource, OccupancyDetails occupancyDetails) {
		if (!Constants.CLIENT_DESKTOP.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue())) && Utility.isGroupBookingFunnel(funnelSource) && baseCombo && recommendedCombo != null && roomDetails.getBaseRoom() != null &&
				roomDetails.getBaseRoom() && occupancyDetails != null) {
			int roomCount = occupancyDetails.getNumOfRooms();
			String comboDisplayText = roomCount + SPACE_X_SPACE + roomDetails.getRoomName();
			roomDetails.setDisplayName(comboDisplayText);
			recommendedCombo.setBaseComboText(comboDisplayText);
		}
		else if(Constants.CLIENT_DESKTOP.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue())) && Utility.isGroupBookingFunnel(funnelSource)  && recommendedCombo != null && roomDetails.getBaseRoom() != null &&
				roomDetails.getBaseRoom() && occupancyDetails != null){
			int roomCount = occupancyDetails.getNumOfRooms();
			String comboDisplayText = roomCount + SPACE_X_SPACE + roomDetails.getRoomName();
			roomDetails.setDisplayName(comboDisplayText);
			recommendedCombo.setBaseComboText(comboDisplayText);
		}
	}

	private void buildStayDetails(SearchRoomsResponse searchRoomsResponse, SleepingArrangementRoomInfo roomInfo, int sellableCombo, String propertyType, HotelRates hotelRates) {
		StayDetail stayDetail = null;
		int bedCount = 0;
		int bedRoomCount = 0;
		int maxGuest = 0;
		int extraBeds = 0;
		int baseOccupancy = 0;
		int maxCapacity=0;
		int bathroomCount = 0;
		LinkedHashMap<String,Integer> bedInfoMap = new LinkedHashMap<>();
		if (CollectionUtils.isNotEmpty(searchRoomsResponse.getExactRooms())) {
			List<RoomDetails> roomDetailsList = searchRoomsResponse.getExactRooms();
			if (CollectionUtils.isNotEmpty(roomDetailsList)) {
				RoomDetails exactRoom = roomDetailsList.get(0);
				if(exactRoom.getPrivateSpaces()!=null){
					// It means It's an Migrated Property , So need to compute On the basis PrivateSpace;
					int bathRoomMultiplier = 0;
					if(CollectionUtils.isNotEmpty(exactRoom.getPrivateSpaces().getSpaces())){
						for(Space space : exactRoom.getPrivateSpaces().getSpaces()){
							if(CollectionUtils.isNotEmpty(space.getSleepingInfoArrangement())){
								if((Constants.BEDROOM.equalsIgnoreCase(space.getSpaceType()) || LIVING_ROOM.equalsIgnoreCase(space.getSpaceType())) && bathRoomMultiplier==0){
									bathRoomMultiplier = space.getSleepingInfoArrangement().size();
								}
								for(SleepingInfoArrangement sleepingInfoArrangement : space.getSleepingInfoArrangement()){
									bedCount+=sleepingInfoArrangement.getBed();
									bedRoomCount+=sleepingInfoArrangement.getBedRoom();
									baseOccupancy+=sleepingInfoArrangement.getGuest();
									maxGuest+=sleepingInfoArrangement.getGuest();
									maxCapacity+=sleepingInfoArrangement.getMaxCapacity();
									for(String bedType : sleepingInfoArrangement.getBedInfos().keySet()){
										if(!bedInfoMap.containsKey(bedType))
											bedInfoMap.put(bedType,0);
										bedInfoMap.put(bedType,bedInfoMap.get(bedType)+sleepingInfoArrangement.getBedInfos().get(bedType));
									}
								}
							}
						}
						bathroomCount += ((exactRoom.getBathroomCount()!=null?exactRoom.getBathroomCount() * bathRoomMultiplier:0));
					}
					stayDetail = new StayDetail();
					stayDetail.setBedRoom(bedRoomCount);
					stayDetail.setBed(bedCount);
					stayDetail.setMaxGuests(maxGuest);
					stayDetail.setBaseGuests(baseOccupancy);
					stayDetail.setExtraBeds(extraBeds);
					stayDetail.setBathroom(bathroomCount);
					stayDetail.setMaxCapacity(maxCapacity);
					stayDetail.setBedInfoMap(bedInfoMap);
					stayDetail.setBedInfoText(utility.createBedInfoTextFromBedInfoMap(bedInfoMap));
				}else if(exactRoom.getPrivateSpacesV2() != null && hotelRates != null && hotelRates.getRoomTypeDetails() != null &&
						MapUtils.isNotEmpty(hotelRates.getRoomTypeDetails().getRoomToSleepingInfoArrangementMap())) {
					for (Map.Entry<String, List<RoomSleepingInfoLayout>> roomSleepingInfo : hotelRates.getRoomTypeDetails().getRoomToSleepingInfoArrangementMap().entrySet()) {
						String roomCode = roomSleepingInfo.getKey().split(PIPE_SEPARATOR_WITH_BACKSLASH).length == 3 ? roomSleepingInfo.getKey().split(PIPE_SEPARATOR_WITH_BACKSLASH)[0] : StringUtils.EMPTY;
						if (StringUtils.isNotEmpty(roomCode) && roomCode.equalsIgnoreCase(exactRoom.getRoomCode()) && CollectionUtils.isNotEmpty(roomSleepingInfo.getValue())) {
							for (Map.Entry<String, SleepingInfoArrangement> sleepingInfo : roomSleepingInfo.getValue().get(0).getSpaceIdToSleepingInfoArrangementMap().entrySet()) {
								bathroomCount += sleepingInfo.getValue().getBathRoom();
								bedCount += sleepingInfo.getValue().getBed();
								bedRoomCount += sleepingInfo.getValue().getBedRoom();
								baseOccupancy += sleepingInfo.getValue().getGuest();
								maxGuest += sleepingInfo.getValue().getGuest();
								maxCapacity += sleepingInfo.getValue().getMaxCapacity();
								for (String bedType : sleepingInfo.getValue().getBedInfos().keySet()) {
									if (!bedInfoMap.containsKey(bedType))
										bedInfoMap.put(bedType, 0);
									bedInfoMap.put(bedType, bedInfoMap.get(bedType) + sleepingInfo.getValue().getBedInfos().get(bedType));
								}
							}
						}
						stayDetail = new StayDetail();
						stayDetail.setBedRoom(bedRoomCount);
						stayDetail.setBed(bedCount);
						stayDetail.setMaxGuests(maxGuest);
						stayDetail.setBaseGuests(baseOccupancy);
						stayDetail.setExtraBeds(extraBeds);
						stayDetail.setBathroom(bathroomCount);
						stayDetail.setMaxCapacity(maxCapacity);
						stayDetail.setBedInfoMap(bedInfoMap);
						stayDetail.setBedInfoText(utility.createBedInfoTextFromBedInfoMap(bedInfoMap));
					}
				}
				else
					stayDetail = roomDetailsList.get(0).getStayDetail();
			}
		} else if (CollectionUtils.isNotEmpty(searchRoomsResponse.getRecommendedCombos())) {
			if (CollectionUtils.isNotEmpty(searchRoomsResponse.getRecommendedCombos())) {
				List<RoomDetails> roomDetailsList = searchRoomsResponse.getRecommendedCombos().get(0).getRooms();
				if (CollectionUtils.isNotEmpty(roomDetailsList)) {
					if(roomDetailsList.get(0).getPrivateSpaces()!=null){
						// It means It's an Migrated Property , So need to compute On the basis PrivateSpace;
						for(RoomDetails roomDetails : roomDetailsList){
							int bathRoomMultiplier = 0;
							if(roomDetails.getPrivateSpaces()!=null && CollectionUtils.isNotEmpty(roomDetails.getPrivateSpaces().getSpaces())){
								for(Space space : roomDetails.getPrivateSpaces().getSpaces()){
									if(CollectionUtils.isNotEmpty(space.getSleepingInfoArrangement())){
										if((Constants.BEDROOM.equalsIgnoreCase(space.getSpaceType()) || LIVING_ROOM.equalsIgnoreCase(space.getSpaceType())) && bathRoomMultiplier==0){
											bathRoomMultiplier = space.getSleepingInfoArrangement().size();
										}
										for(SleepingInfoArrangement sleepingInfoArrangement : space.getSleepingInfoArrangement()){
											bedCount+=sleepingInfoArrangement.getBed();
											bedRoomCount+=sleepingInfoArrangement.getBedRoom();
											maxGuest+=sleepingInfoArrangement.getGuest();
											maxCapacity+=sleepingInfoArrangement.getMaxCapacity();
											for(String bedType : sleepingInfoArrangement.getBedInfos().keySet()){
												if(!bedInfoMap.containsKey(bedType))
													bedInfoMap.put(bedType,0);
												bedInfoMap.put(bedType,bedInfoMap.get(bedType)+sleepingInfoArrangement.getBedInfos().get(bedType));
											}
										}
									}
								}
							}
							bathroomCount += ((roomDetails.getBathroomCount()!=null?roomDetails.getBathroomCount() * bathRoomMultiplier:0));
						}
					} else if(roomDetailsList.get(0).getPrivateSpacesV2()!=null && hotelRates != null && hotelRates.getRecommendedRoomTypeDetails() != null &&
							MapUtils.isNotEmpty(hotelRates.getRecommendedRoomTypeDetails().getRoomToSleepingInfoArrangementMap())){
						for (RoomDetails roomDetails : roomDetailsList) {
							for (Map.Entry<String, List<RoomSleepingInfoLayout>> roomSleepingInfo : hotelRates.getRecommendedRoomTypeDetails().getRoomToSleepingInfoArrangementMap().entrySet()) {
								String roomCode = roomSleepingInfo.getKey().split(PIPE_SEPARATOR_WITH_BACKSLASH).length == 3 ? roomSleepingInfo.getKey().split(PIPE_SEPARATOR_WITH_BACKSLASH)[0] : StringUtils.EMPTY;
								if (StringUtils.isNotEmpty(roomCode) && CollectionUtils.isNotEmpty(roomSleepingInfo.getValue()) && roomCode.equals(roomDetails.getRoomCode())) {
									for (Map.Entry<String, SleepingInfoArrangement> sleepingInfo : roomSleepingInfo.getValue().get(0).getSpaceIdToSleepingInfoArrangementMap().entrySet()) {
										bathroomCount += sleepingInfo.getValue().getBathRoom();
										bedCount += sleepingInfo.getValue().getBed();
										bedRoomCount += sleepingInfo.getValue().getBedRoom();
										baseOccupancy += sleepingInfo.getValue().getGuest();
										maxGuest += sleepingInfo.getValue().getGuest();
										maxCapacity += sleepingInfo.getValue().getMaxCapacity();
										for (String bedType : sleepingInfo.getValue().getBedInfos().keySet()) {
											if (!bedInfoMap.containsKey(bedType))
												bedInfoMap.put(bedType, 0);
											bedInfoMap.put(bedType, bedInfoMap.get(bedType) + sleepingInfo.getValue().getBedInfos().get(bedType));
										}
									}
								}
							}
						}
					} else{
						// It's an Un-Migrated Property So read always from roomInfo Node;
						for(RoomDetails roomDetails : roomDetailsList){
							StayDetail roomStayDetail = roomDetails.getStayDetail();
							if (roomStayDetail != null) {
								if(roomStayDetail.getBed() != null){
									bedCount += roomStayDetail.getBed();
								}
								if(roomStayDetail.getBedRoom() != null){
									bedRoomCount += roomStayDetail.getBedRoom();
								}
								if(roomStayDetail.getMaxGuests() != null){
									maxGuest += roomStayDetail.getMaxGuests();
								}
								if(roomStayDetail.getExtraBeds() != null) {
									extraBeds += roomStayDetail.getExtraBeds();
								}
								if(roomStayDetail.getBaseGuests() != null) {
									baseOccupancy += roomStayDetail.getBaseGuests();
								}
								if (roomStayDetail.getBathroom() != null) {
									bathroomCount += roomStayDetail.getBathroom();
								}
								if (roomStayDetail.getMaxCapacity() != null) {
									maxCapacity += roomStayDetail.getMaxCapacity();
								}
							}

						}
					}
					stayDetail = new StayDetail();
					stayDetail.setBedRoom(bedRoomCount);
					stayDetail.setBed(bedCount);
					stayDetail.setMaxGuests(maxGuest);
					stayDetail.setBaseGuests(baseOccupancy);
					stayDetail.setExtraBeds(extraBeds);
					stayDetail.setMaxCapacity(maxCapacity);
					stayDetail.setBathroom(bathroomCount);
					stayDetail.setBedInfoMap(bedInfoMap);
					stayDetail.setBedInfoText(utility.createBedInfoTextFromBedInfoMap(bedInfoMap));
				}
			}
		}
		if (stayDetail!=null && hotelRates!=null){
			stayDetail.setRoomCount(hotelRates.getRoomCount());
		}
		if(stayDetail!=null && StringUtils.isNotBlank(propertyType) && (propertyType.equalsIgnoreCase(Constants.PROPERTY_TYPE_HOSTEL) || propertyType.equalsIgnoreCase(Constants.PROPERTY_TYPE_HOMESTAY)) && (sellableCombo==1 || sellableCombo==3))
			stayDetail.setBedRoom(0);
		if(stayDetail!=null && hotelRates!=null){
			stayDetail.setStayTypeInfo(buildStayTypeInfo(hotelRates.getSellableUnit(),hotelRates.getPropertyType()));
		}
		roomInfo.setStayDetail(stayDetail);
		roomInfo.setBedInfoText((stayDetail!=null?stayDetail.getBedInfoText():null));
	}

	private StayTypeInfo buildStayTypeInfo(String propertySellableUnit, String propertyType) {
		if (MapUtils.isEmpty(actionInfoMap) || StringUtils.isEmpty(propertySellableUnit)) {
			return null;
		}
		if (SELLABLE_UNIT_ENTIRE.equalsIgnoreCase(propertySellableUnit)) {
			return actionInfoMap.get(SELLABLE_UNIT_ENTIRE_PMS_KEY);
		} else if (SELLABLE_UNIT_ROOM.equalsIgnoreCase(propertySellableUnit) && !PROPERTY_TYPE_HOSTEL.equalsIgnoreCase(propertyType)) {
			return actionInfoMap.get(SELLABLE_UNIT_ROOM_PMS_KEY);
		}
		return null;
	}


	/**
	 * To build BedInfoText, It is added to summarize the bed types and count for property Layout
	 * @param searchRoomsResponse
	 * @param roomInfo
	 */
	private void buildBedInfoText(SearchRoomsResponse searchRoomsResponse,SleepingArrangementRoomInfo roomInfo){
		String bedInfoText= null;
		if (searchRoomsResponse!=null && CollectionUtils.isNotEmpty(searchRoomsResponse.getExactRooms())) {
			bedInfoText = searchRoomsResponse.getExactRooms().get(0).getBedInfoText();
		}
		roomInfo.setBedInfoText(bedInfoText);
	}

	private void buildStayDetails(RoomDetails roomDetails) {
		int roomCount = 1;
		if (CollectionUtils.isNotEmpty(roomDetails.getRatePlans()) && CollectionUtils.isNotEmpty(roomDetails.getRatePlans().get(0).getTariffs())) {
			Tariff tariff = roomDetails.getRatePlans().get(0).getTariffs().get(0);
			if (tariff.getOccupancydetails() != null && tariff.getOccupancydetails().getRoomCount() != null) {
				roomCount = tariff.getOccupancydetails().getRoomCount();
			}
		}
		StayDetail stayDetail = new StayDetail();
		if (roomDetails.getBedCount() != null) {
			stayDetail.setBed(roomCount * roomDetails.getBedCount());
		}
		if (roomDetails.getMaxGuest() != null) {
			stayDetail.setMaxGuests(roomCount * roomDetails.getMaxGuest());
		}
		if (roomDetails.getBedroomCount() != null) {
			stayDetail.setBedRoom(roomCount * roomDetails.getBedroomCount());
		}
		if(roomDetails.getExtraBedCount() != null) {
			stayDetail.setExtraBeds(roomCount * roomDetails.getExtraBedCount());
		}
		if(roomDetails.getBaseGuest() != null) {
			stayDetail.setBaseGuests(roomCount * roomDetails.getBaseGuest());
		}
		roomDetails.setStayDetail(stayDetail);
	}

	private void setPackageRoomSpecificInfo(PackageRoomDetails roomDetails, PackageRoomType roomType){
		if(roomType==null){
			return;
		}
		roomDetails.setAnimationType(roomType.getAnimationType());
		roomDetails.setCtaText(roomType.getCtaText());
		roomDetails.setDescriptionText(roomType.getDescriptionText());
		roomDetails.setHeader(roomType.getHeader());
		roomDetails.setTitle(roomType.getTitle());
		roomDetails.setIsExtendedStayPackage(roomType.getIsExtendedStayPackage());
		roomDetails.setRecommendText(roomType.getRecommendText());
	}

	private List<MediaData> populateMedia(Map<String, RoomInfo> staticRoomInfoMap, List<String> images, String roomCode) {

		List<MediaData> mediaList = new ArrayList<>();

		if (MapUtils.isNotEmpty(staticRoomInfoMap) && staticRoomInfoMap.containsKey(roomCode) && CollectionUtils.isNotEmpty(staticRoomInfoMap.get(roomCode).getRoomLevelVideos())) {
			RoomInfo roomInfo = staticRoomInfoMap.get(roomCode);

			for (VideoInfo videoInfo : roomInfo.getRoomLevelVideos()) {

				MediaData mediaData = new MediaData();
				mediaData.setMediaType(Constants.VIDEO_TYPE);
				mediaData.setUrl(videoInfo.getUrl());
				mediaData.setThumbnailUrl(videoInfo.getThumbnailUrl());
				mediaData.setText(videoInfo.getText());
				mediaList.add(mediaData);
			}


		}

		if (CollectionUtils.isNotEmpty(images)) {

			for (String url : images) {
				MediaData mediaData = new MediaData();
				mediaData.setMediaType(Constants.IMAGE_TYPE);
				mediaData.setUrl(url);
				mediaList.add(mediaData);
			}
		}
		return mediaList;
	}

	protected abstract PersuasionObject createTopRatedPersuasion();

	protected PersuasionObject createTopRatedPersuasionForMoblie() {
		PersuasionObject persuasionObject = new PersuasionObject();
		persuasionObject.setData(new ArrayList<>());
		persuasionObject.setPlaceholder(Constants.PLACEHOLDER_SELECT_TOP_R1);
		persuasionObject.setTemplate("IMAGE_TEXT_H");
		PersuasionData persuasionData = new PersuasionData();
		PersuasionStyle style = new PersuasionStyle();
		style.setFontSize("SMALL");
		style.setTextColor("#b8860b");
		style.setFontType("B");
		style.setBorderColor("#b8860b");
		persuasionData.setStyle(style);
		persuasionData.setPersuasionType("PEITHO");
		persuasionData.setText(polyglotService.getTranslatedData(ConstantsTranslation.TOP_RATED));
		persuasionObject.setData(Arrays.asList(persuasionData));
		return persuasionObject;
	}

	private void addPersuasion(RoomDetails roomDetails, PersuasionObject persuasionObject) {
		if (roomDetails == null || persuasionObject == null) {
			return;
		}
		try {
			Map<Object, Object> map = new HashMap<>();
			map.put(persuasionObject.getPlaceholder(), persuasionObject);
			if (roomDetails.getRoomPersuasions() == null) {
				roomDetails.setRoomPersuasions(map);
			} else {
				((ObjectNode)roomDetails.getRoomPersuasions()).putPOJO(persuasionObject.getPlaceholder(), persuasionObject);
			}
		} catch (ClassCastException e) {
			LOGGER.error("Persuasion could not be added due to ClassCastException : {} ", e.getMessage());
		} catch (Exception e) {
			LOGGER.error("Persuasion could not be added due to : {} ", e.getMessage());
		}
	}

	private List<RoomDetails> populateSubRooms(String parentRoomCode, Map<String, RoomInfo> staticRoomInfoMap){
		if(MapUtils.isNotEmpty(staticRoomInfoMap) && CollectionUtils.isNotEmpty(staticRoomInfoMap.values())) {
			List<RoomInfo> childRoomInfos = staticRoomInfoMap.values().stream().filter(a-> parentRoomCode.equalsIgnoreCase(a.getParentRoomCode())).collect(Collectors.toList());
			if(CollectionUtils.isNotEmpty(childRoomInfos)){
				List<RoomDetails> roomDetailsList = new ArrayList<>();
				for(RoomInfo roomInfo: childRoomInfos){
					RoomDetails roomDetails = new RoomDetails();
					roomDetails.setMaster(roomInfo.isMaster());
					roomDetails.setMaxAdult(roomInfo.getMaxAdultCount());
					roomDetails.setMaxChild(roomInfo.getMaxChildCount());
					roomDetails.setMaxGuest(roomInfo.getMaxGuestCount());
					/* Below three nodes are duplicate & need to be removed after next client release */
					/* Only Temp addition to fix live bug */
					roomDetails.setMaxGuestCount(roomInfo.getMaxGuestCount());
					roomDetails.setMaxAdultCount(roomInfo.getMaxAdultCount());
					roomDetails.setMaxChildCount(roomInfo.getMaxChildCount());
					roomDetails.setBathroomCount(roomInfo.getBathroomCount());
					/* Above three nodes are duplicate & need to be removed after next client release */
					roomDetails.setBedCount(roomInfo.getBedCount());
					if (StringUtils.isNotBlank(roomInfo.getBedRoomCount()))
						roomDetails.setBedroomCount(NumberUtils.toInt(roomInfo.getBedRoomCount(),0));
					roomDetails.setParentRoomCode(roomInfo.getParentRoomCode());
					roomDetails.setRoomSize(roomInfo.getRoomSize());
					roomDetails.setRoomName(roomInfo.getRoomName());
					roomDetails.setRoomViewName(roomInfo.getRoomViewName());
					roomDetails.setBeds(populateBedTypeKey(roomInfo.getBeds()));
					roomDetailsList.add(roomDetails);
				}
				return  roomDetailsList;
			}
			return null;
		}
		return null;
	}

	private List<SleepingArrangement> populateBedTypeKey(List<SleepingArrangement> beds) {
		if (Objects.nonNull(beds)) {
			beds.stream().forEach(bed -> {
				if (StringUtils.isNotEmpty(bed.getType())) {
					bed.setBedTypeKey(bed.getType().replace(" ", "_").toUpperCase());
				}
			});
		}
		return beds;
	}

	private com.mmt.hotels.clientgateway.response.rooms.Segments getSegments(com.mmt.hotels.model.response.searchwrapper.Segments segments) {
		if (segments == null) {
			return null;
		}
		com.mmt.hotels.clientgateway.response.rooms.Segments segmentsCG = new com.mmt.hotels.clientgateway.response.rooms.Segments();
		segmentsCG.setSegmentsCount(segments.getSegmentsCount());
		segmentsCG.setSegmentList(getSegmentsList(segments.getSegmentList()));
		return segmentsCG;
	}

	private Map<String, com.mmt.hotels.clientgateway.response.rooms.Segment> getSegmentsList(Map<String,com.mmt.hotels.model.response.searchwrapper.Segment> segmentList) {
		if (MapUtils.isEmpty(segmentList))
			return null;
		Map<String, com.mmt.hotels.clientgateway.response.rooms.Segment> segmentMap = new HashMap<>();
		for (Map.Entry<String,com.mmt.hotels.model.response.searchwrapper.Segment> entry : segmentList.entrySet()) {
			segmentMap.put(entry.getKey(), getSegment(entry.getValue()));
		}
		return segmentMap;
	}

	private com.mmt.hotels.clientgateway.response.rooms.Segment getSegment(com.mmt.hotels.model.response.searchwrapper.Segment segment) {
		if (segment == null) {
			return null;
		}
		com.mmt.hotels.clientgateway.response.rooms.Segment segmentCG = new Segment();
		segmentCG.setId(segment.getId());
		segmentCG.setUserSegment(segment.getUserSegment());
		segmentCG.setChannelSegment(segment.getChannelSegment());
		return segmentCG;
	}

	private com.mmt.hotels.clientgateway.response.rooms.WalletSurge getWalletSurge(WalletSurge walletSurge) {
		if (walletSurge == null) {
			return null;
		}
		com.mmt.hotels.clientgateway.response.rooms.WalletSurge walletSurgeCG = new com.mmt.hotels.clientgateway.response.rooms.WalletSurge();
		walletSurgeCG.setSurge(walletSurge.isSurge());
		walletSurgeCG.setEndTime(walletSurge.getEndTime());
		walletSurgeCG.setPersuasionText(walletSurge.getPersuasionText());
		walletSurgeCG.setStartTime(walletSurge.getStartTime());
		return walletSurgeCG;
	}
	private String buildRoomSizeText(RoomInfo roomInfo) {
		String roomSizeText = null;
		if (roomInfo != null && StringUtils.isNotBlank(roomInfo.getRoomSize()) && StringUtils.isNotBlank(roomInfo.getRoomSizeUnit())) {
			roomSizeText = roomInfo.getRoomSize() + SPACE + roomInfo.getRoomSizeUnit();
			if (SQUARE_FEET_V2.equalsIgnoreCase(roomInfo.getRoomSizeUnit())) {
				try {
					double roomSize = Double.parseDouble(roomInfo.getRoomSize());
					roomSize = roomSize * SQUARE_FEET_TO_SQUARE_METER_CONVERSION_FACTOR;
					long roundedRoomSize = Math.round(roomSize); // Round off to the nearest integer
					String roomSizeInMeterSquare = String.valueOf(roundedRoomSize);
					roomSizeText = roomSizeText + SPACE + AMENITIES_OPEN_BRACE + roomSizeInMeterSquare + SPACE + SQUARE_METER + AMENITIES_CLOSING_BRACE;
				} catch (Exception e) {
					LOGGER.error("Error while parsing room size text : {}", e.getMessage());
				}
			}
		}
		return roomSizeText;
	}
	public List<RoomHighlight> getRoomHighlights(RoomInfo roomInfo, ExtraGuestDetail extraGuestDetail, boolean altAccoHotel,boolean pilgrimageBedInfoEnable) {
		List<RoomHighlight> roomHighlights = new ArrayList<>();

		if(StringUtils.isNotBlank(roomInfo.getDormType()) && altAccoHotel == true) {
			RoomHighlight roomHighlight = new RoomHighlight();
			roomHighlight.setSelectRoomRevampOrder(0);

			roomHighlight.setDescription(StringUtils.capitalize(roomInfo.getDormType().toLowerCase()) + " Dorm");
			roomHighlight.setText(StringUtils.capitalize(roomInfo.getDormType().toLowerCase()) + " Dorm");
			roomHighlight.setIconUrl(IMAGE_URL_DORM_TYPE);
			roomHighlight.setIdentifier(RoomHighlightType.DORM_TYPE.name());
			roomHighlights.add(roomHighlight);
		}
		if(roomInfo.getDormBedCount() != null && altAccoHotel == true) {
			RoomHighlight roomHighlight = new RoomHighlight();
			roomHighlight.setSelectRoomRevampOrder(1);
			roomHighlight.setIconUrl(IMAGE_URL_DORM_BED_COUNT);
			roomHighlight.setDescription(roomInfo.getDormBedCount().toString() + "-Bed Sharing");
			roomHighlight.setText(roomInfo.getDormBedCount().toString() + "-Bed Sharing");
			roomHighlight.setIdentifier(RoomHighlightType.DORM_BED_COUNT.name());
			roomHighlights.add(roomHighlight);
		}
		if(StringUtils.isNotBlank(roomInfo.getBathroomInfo()) && altAccoHotel == true) {
			RoomHighlight roomHighlight = new RoomHighlight();
			roomHighlight.setDescription(roomInfo.getBathroomInfo() + " Bathroom");
			roomHighlight.setText(roomInfo.getBathroomInfo() + " Bathroom");
			roomHighlight.setIdentifier(RoomHighlightType.ROOM_INFO.name());
			roomHighlight.setIconUrl(IMAGE_URL_DORM_BATHROOM);
			roomHighlight.setSelectRoomRevampOrder(2);
			roomHighlights.add(roomHighlight);
		}
		if(StringUtils.isNotBlank(roomInfo.getRoomSize())) {
			RoomHighlight roomHighlight = new RoomHighlight();

			roomHighlight.setIconUrl(Constants.IMAGE_URL_ROOM_SIZE);
			roomHighlight.setText(buildRoomSizeText(roomInfo));
			roomHighlight.setDescription(roomHighlight.getText());
			roomHighlight.setIdentifier(RoomHighlightType.ROOM_SIZE.name());
			roomHighlight.setSelectRoomRevampOrder(3);
			roomHighlights.add(roomHighlight);
		}
		if(StringUtils.isNotBlank(roomInfo.getRoomViewName())) {
			RoomHighlight roomHighlight = new RoomHighlight();
			roomHighlight.setIconUrl("https://gos3.ibcdn.com/roomViewIcon-1678093525.png");
			roomHighlight.setText(roomInfo.getRoomViewName());
			roomHighlight.setDescription(roomInfo.getRoomViewName());
			roomHighlight.setIdentifier(RoomHighlightType.ROOM_VIEW.name());
			roomHighlight.setSelectRoomRevampOrder(4);
			roomHighlights.add(roomHighlight);
		}

		if (CollectionUtils.isNotEmpty(roomInfo.getExternalVendorBedRoomInfoList())) {
			roomInfo.getExternalVendorBedRoomInfoList().stream()
					.filter(bedRoomInfo -> StringUtils.isNotBlank(bedRoomInfo.getBedRoomDescription()))
					.forEach(bedRoomInfo -> {
						String bedTypeText = null;
						if (StringUtils.isNotEmpty(bedRoomInfo.getBedRoomName())) {
							bedTypeText = new StringBuilder(bedRoomInfo.getBedRoomName()).append(Constants.SPACE).append(Constants.HYPEN).append(Constants.SPACE).append(bedRoomInfo.getBedRoomDescription()).toString();
						} else {
							bedTypeText = bedRoomInfo.getBedRoomDescription();
						}
						RoomHighlight roomHighlight = new RoomHighlight();
						if (Constants.BATHROOMS.equalsIgnoreCase(bedRoomInfo.getBedRoomName())) {
							roomHighlight.setIconUrl(Constants.IMAGE_URL_BATHROOM_TYPE);
						} else {
							roomHighlight.setIconUrl(Constants.IMAGE_URL_ROOM_TYPE);
						}
						roomHighlight.setSelectRoomRevampOrder(6);
						roomHighlight.setIdentifier(RoomHighlightType.BATHROOM_TYPE.name());
						roomHighlight.setText(bedTypeText);
						roomHighlights.add(roomHighlight);
					});
		} else {
			if (pilgrimageBedInfoEnable) {
				if (CollectionUtils.isNotEmpty(roomInfo.getBeds()) || CollectionUtils.isNotEmpty(roomInfo.getAlternateBeds())) {
					RoomHighlight roomHighlight = new RoomHighlight();
					roomHighlight.setIconUrl(Constants.IMAGE_URL_ROOM_TYPE);
					List<String> bedTypeList = new ArrayList<>();
					List<String> alternateBedTypeList = new ArrayList<>();

					if (CollectionUtils.isNotEmpty(roomInfo.getBeds())) {
						roomInfo.getBeds().forEach(bedType -> {
							String bedTypeText = bedType.getCount() + SPACE + bedType.getType();
							bedTypeList.add(bedTypeText);
						});
					}
					if (CollectionUtils.isNotEmpty(roomInfo.getAlternateBeds())) {
						roomInfo.getAlternateBeds().forEach(bedType -> {
							String bedTypeText = bedType.getCount() + SPACE + bedType.getType();
							alternateBedTypeList.add(bedTypeText);
						});
					}
					String bedTypeListString = "";
					if (!bedTypeList.isEmpty()) {
						bedTypeListString = String.join(COMMA_SPACE, bedTypeList);
						if (!alternateBedTypeList.isEmpty()) {
							bedTypeListString = bedTypeListString + SPACE + polyglotService.getTranslatedData(ROOM_DETAILS_ALTERNATE_BED_TYPE_OR_TEXT) + SPACE + String.join(COMMA_SPACE, alternateBedTypeList);
						}
					} else {
						bedTypeListString = String.join(COMMA_SPACE, alternateBedTypeList);
					}

					roomHighlight.setText(bedTypeListString);
					roomHighlight.setDescription(bedTypeListString);
					if (extraGuestDetail != null && StringUtils.isNotEmpty(extraGuestDetail.getRoomSelectionExtraBedText()) && !altAccoHotel) {
						if (altAccoHotel && roomInfo.getBeds().size() == 1) {
							roomHighlight.setSubText(extraGuestDetail.getRoomSelectionExtraBedText());
						} else if (!altAccoHotel) {
							roomHighlight.setSubText(extraGuestDetail.getRoomSelectionExtraBedText());
						}
					}
					roomHighlight.setIdentifier(RoomHighlightType.BED_TYPE.name());
					roomHighlight.setSelectRoomRevampOrder(5);
					roomHighlights.add(roomHighlight);
				}
			} else {
				if ((CollectionUtils.isNotEmpty(roomInfo.getBeds()))) {
					RoomHighlight roomHighlight = new RoomHighlight();
					roomHighlight.setIconUrl("https://gos3.ibcdn.com/bedBlackIcon-1678093474.png");
					List<String> bedTypeList = new ArrayList<>();
					roomInfo.getBeds().forEach(bedType -> {
						String bedTypeText;
						if (bedType.getCount() > 1) {
							bedTypeText = bedType.getCount() + SPACE_X_SPACE + bedType.getType();
						} else {
							bedTypeText = bedType.getType();
						}
						bedTypeList.add(bedTypeText);
					});
					roomHighlight.setText(String.join(COMMA_SPACE, bedTypeList));
					roomHighlight.setDescription(String.join(COMMA_SPACE, bedTypeList));
					if (extraGuestDetail != null && StringUtils.isNotEmpty(extraGuestDetail.getRoomSelectionExtraBedText()) && !altAccoHotel) {
						if (altAccoHotel && roomInfo.getBeds().size() == 1) {
							roomHighlight.setSubText(extraGuestDetail.getRoomSelectionExtraBedText());
						} else if (!altAccoHotel) {
							roomHighlight.setSubText(extraGuestDetail.getRoomSelectionExtraBedText());
						}
					}
					roomHighlight.setIdentifier(RoomHighlightType.BED_TYPE.name());
					roomHighlight.setSelectRoomRevampOrder(5);
					roomHighlights.add(roomHighlight);
				}
			}
			if (CollectionUtils.isNotEmpty(roomInfo.getBathrooms())) {
				RoomHighlight roomHighlight = new RoomHighlight();
				roomHighlight.setIconUrl(Constants.IMAGE_URL_BATHROOM_TYPE);
				BathroomArrangement bathroomArrangement = roomInfo.getBathrooms().get(0);
				String bedTypeText;
				if (bathroomArrangement.getCount() > 1) {
					bedTypeText = bathroomArrangement.getCount() + SPACE + polyglotService.getTranslatedData(ROOM_DETAILS_BATHROOMS_TEXT);
				} else {
					bedTypeText = bathroomArrangement.getCount() + SPACE + polyglotService.getTranslatedData(ROOM_DETAILS_BATHROOM_TEXT);
				}
				roomHighlight.setText(bedTypeText);
				roomHighlight.setSelectRoomRevampOrder(6);
				roomHighlight.setIdentifier(RoomHighlightType.BATHROOM_TYPE.name());
				roomHighlight.setDescription(bedTypeText);
				roomHighlights.add(roomHighlight);
			}
		}

		if (roomInfo.getMaxGuestCount() > 0) {
			RoomHighlight roomHighlight = new RoomHighlight();
			roomHighlight.setIconUrl("https://gos3.ibcdn.com/paxBlackIcon-1678093500.png");
			if (roomInfo.getMaxGuestCount() == 1) {
				String countText = String.format("Max %s Guest",roomInfo.getMaxGuestCount());
				roomHighlight.setText(countText);
				roomHighlight.setDescription(countText);
			}else {
				String countText = String.format("Max %s Guests",roomInfo.getMaxGuestCount());
				roomHighlight.setText(countText);
				roomHighlight.setDescription(countText);
			}
			roomHighlight.setIdentifier(RoomHighlightType.GUEST_COUNT.name());
			roomHighlights.add(roomHighlight);
		}


		return roomHighlights;
	}

	private ExtraGuestInfo getExtraGuestInfo(ExtraGuestDetail extraGuestDetail){

		ExtraGuestInfo extraGuestInfo = null;
		if(extraGuestDetail!= null && StringUtils.isNotEmpty(extraGuestDetail.getRoomSelectionDetailExtraBedText())
				&& extraGuestDetail.getRoomSelectionDetailExtraBedText().contains(Constants.HASH_SEPARATOR)){
			extraGuestInfo = new ExtraGuestInfo();
			String[] tokens=extraGuestDetail.getRoomSelectionDetailExtraBedText().split(Constants.HASH_SEPARATOR);
			extraGuestInfo.setExtraGuestInfoHeading(polyglotService.getTranslatedData(ConstantsTranslation.ROOM_DETAILS_EXTRA_GUEST_INFO_HEADING));
			extraGuestInfo.setExtraGuestInfoDscr(tokens[1]);

		}
	return extraGuestInfo;
	}
	private TariffViewType getTariffViewType(RoomDetails roomDetails, Integer starRating, boolean isFirstRoom, Map<String, String> expData) {
		int totalRatePlans = roomDetails.getRatePlans().size();

		TariffViewType clientViewType = new TariffViewType();
		clientViewType.setTotalTariffs(roomDetails.getRatePlans().size());
		clientViewType.setBaseTariffText(polyglotService.getTranslatedData("STARTING_PRICE_AT"));
		if (totalRatePlans == 1) {
			clientViewType.setInitialVisible(1);
			return clientViewType;
		}


		//CLEANUP : PREMIUM_SR, ROOM_COUNT_SR, BUDGET_SR
		/* Search-Rooms UAT JIRA HTL-29300 */

//		&& MapUtils.isNotEmpty(expData)
		if (MapUtils.isNotEmpty(ratePlanDisplayLogic)) {
			Map<String,Integer> map;
//			if (("true").equalsIgnoreCase(expData.get("Premium_SR")) && starRating!=null && starRating >=4) {
//				if (("true").equalsIgnoreCase(expData.get("Room_Count_SR"))) {
//					/* Premium_SR = true && Room_Count_SR = true */
//					map = ratePlanDisplayLogic.get("PREMIUM").get(getKeyAccordingToCount(ratePlanDisplayLogic.get("PREMIUM"),totalRatePlans));
//				} else {
//					/* Premium_SR = true && Room_Count_SR = false */
//					map = ratePlanDisplayLogic.get("PREMIUM").get("DEFAULT");
//				}
//				if (isFirstRoom) {
//					clientViewType.setInitialVisible(map.get("FIRST_ROOM_RPC_COUNT"));
//				} else {
//					clientViewType.setInitialVisible(map.get("OTHER_ROOM_RPC_COUNT"));
//				}
//			} else
//				if (("false").equalsIgnoreCase(expData.get("Premium_SR"))){
//					|| (("true").equalsIgnoreCase(expData.get("Premium_SR")) && starRating!=null && starRating <4 )) {
//				if (("true").equalsIgnoreCase(expData.get("Room_Count_SR"))) {
//					/* Premium_SR = false && Room_Count_SR = true */
//					map = ratePlanDisplayLogic.get("BUDGET").get(getKeyAccordingToCount(ratePlanDisplayLogic.get("BUDGET"),totalRatePlans));
//				} else {
					/* Premium_SR = false && Room_Count_SR = false */
					map = ratePlanDisplayLogic.get("BUDGET").get("DEFAULT");
//				}
				if (isFirstRoom) {
					clientViewType.setInitialVisible(map.get("FIRST_ROOM_RPC_COUNT"));
				} else {
					clientViewType.setInitialVisible(map.get("OTHER_ROOM_RPC_COUNT"));
				}
//			} else {
//				clientViewType.setInitialVisible(ratePlanMoreOptionsLimit);
//			}
		} else {
			clientViewType.setInitialVisible(ratePlanMoreOptionsLimit);
		}
		/* Search-Rooms UAT JIRA HTL-29300 */

		/* Check and correct initialVisibleCount for case when : (initialVisibleCount > TotalTariffsCount) */
		if (clientViewType.getInitialVisible()>clientViewType.getTotalTariffs())
			clientViewType.setInitialVisible(clientViewType.getTotalTariffs());

		return clientViewType;
	}

	private String getKeyAccordingToCount(Map<String,Map<String,Integer>> map, int roomCount) {
		String key = "DEFAULT";
		if (map.containsKey(String.valueOf(roomCount))) {
			key = String.valueOf(roomCount);
		} else {
			List<String> list = new ArrayList<>(map.keySet());
			List<Integer> arr = list.stream().filter(NumberUtils::isCreatable).map(Integer::new).sorted(Comparator.comparingInt(Integer::intValue)).collect(Collectors.toList());
			Optional<Integer> k = arr.stream().sequential().filter(i->i>roomCount).findFirst();
			if (k.isPresent()) {
				key = String.valueOf(k.get());
			} else if (CollectionUtils.isNotEmpty(arr) && (arr.get(arr.size() - 1) < roomCount)) {
				key = String.valueOf(arr.get(arr.size()-1));
			}
		}
		return key;
	}

	private List<SelectRoomRatePlan> getRatePlans(HotelRates hotelRates,RoomType roomType, String listingType, Map<String, String> expData,
												  boolean ratePlanGroup, String askedCurrency, String sellableType,
												  String funnelSource, int days, int ap, boolean isBlockPAH,
												  CommonModifierResponse commonModifierResponse, boolean isPackageRoom,
												  Map<String, String> ratePlanCodeAndNameMap, boolean isLuxeHotel, boolean isUserGCCAndMmtExclusive, boolean groupBookingPrice, BlackInfo blackInfo, boolean isOccassion,String roomViewType,List<String> amenitiesFilterCodes) {
		if (roomType == null)
			return null;
		List<SelectRoomRatePlan> ratePlans = new ArrayList<>();
		Map<String, List<com.mmt.hotels.model.response.pricing.RatePlan>> rpcRatePlanMap = new LinkedHashMap<>();
		boolean myPartner = Objects.nonNull(commonModifierResponse) && Objects.nonNull(commonModifierResponse.getExtendedUser()) && Utility.isMyPartnerRequest(commonModifierResponse.getExtendedUser().getProfileType(), commonModifierResponse.getExtendedUser().getAffiliateId());
		Comparator<RatePlan> compRatePlanPrice = (h1, h2) -> new Double(h1.getDisplayFare().getDisplayPriceBreakDown().getDisplayPrice()).compareTo(new Double(h2.getDisplayFare().getDisplayPriceBreakDown().getDisplayPrice()));
        List<RatePlan> sortedRatePlanList = roomType.getRatePlanList().values().stream().sorted(compRatePlanPrice).collect(Collectors.toList());
		String linkedRateExperimentValue = getExperimentValue(commonModifierResponse, Constants.LINKED_RATE_EXPERIMENT_NR);
//		roomType.getViewType()
//		roomType.getam
		for (RatePlan ratePlanHes : sortedRatePlanList) {
			String ratePlanCode = ratePlanHes.getRatePlanCode();
			ratePlanHes.setRatePlanCode(ratePlanCode);
			if (ratePlanGroup) {
				rpcRatePlanMap.computeIfAbsent(ratePlanCode, k -> new ArrayList<>());
				rpcRatePlanMap.get(ratePlanCode).add(ratePlanHes);
			}else {
				String rpccCancelPolicy = "";
				String cancelPolicy = CollectionUtils.isNotEmpty(ratePlanHes.getCancelPenaltyList())
						&& ratePlanHes.getCancelPenaltyList().get(0).getPenaltyDescription() != null ? ratePlanHes.getCancelPenaltyList().get(0).getPenaltyDescription().getName():"";
				if(StringUtils.isNotEmpty(cancelPolicy)){
					rpccCancelPolicy = ratePlanHes.getRpcc() + ":" + cancelPolicy;
				}
				else{
					rpccCancelPolicy = ratePlanHes.getRpcc();
				}
				rpcRatePlanMap.computeIfAbsent(rpccCancelPolicy, k -> new ArrayList<>());
				rpcRatePlanMap.get(rpccCancelPolicy).add(ratePlanHes);
			}
		}
		boolean goTribeRevamp = MapUtils.isNotEmpty(expData) && StringUtils.equalsIgnoreCase(expData.get(GOTRIBE_REVAMP_POKUS_EXP_KEY), TRUE);

		for (String rpc : rpcRatePlanMap.keySet()) {
			SelectRoomRatePlan ratePlan = new SelectRoomRatePlan();
			if (isPackageRoom || isOccassion) {
				ratePlan = new PackageSelectRoomRatePlan();
			}
			ratePlan.setRpc(rpc);
			RatePlan hesRatePlan = rpcRatePlanMap.get(rpc).get(0);
			ratePlan.setFilterCode(getFilterCodes(hesRatePlan, isBlockPAH, ap, commonModifierResponse, isLuxeHotel,sellableType,roomViewType,amenitiesFilterCodes));
			if(null != hesRatePlan.getPaymentDetails())
				ratePlan.setPayMode(hesRatePlan.getPaymentDetails().getPaymentMode().name());
			if (CollectionUtils.isNotEmpty(hesRatePlan.getMealPlans())) {
				ratePlan.setMealPlanCode(hesRatePlan.getMealPlans().get(0).getCode());
			}
			//ratePlan.setInclusionsList(getInclusionsList(hesRatePlan.getInclusions()));
			ratePlan.setInclusionsList(transformInclusions(hesRatePlan, mealPlanMapPolyglot, ap, isBlockPAH, expData));
			if (goTribeRevamp)
				utility.restructureBlackInclusions(ratePlan, blackInfo, hesRatePlan.getBlackBenefits(), expData != null && (expData.containsKey("goTribe3") && "true".equalsIgnoreCase(expData.get("goTribe3"))));
			//updateInclusion(ratePlan.getInclusionsList(), hesRatePlan.getInclusions());
			if(null != hesRatePlan.getSupplierDetails())
				ratePlan.setSupplierCode(hesRatePlan.getSupplierDetails().getSupplierCode());
			ratePlan.setReviewDeeplinkUrl(hesRatePlan.getReviewDeeplinkUrl());
			ratePlan.setHesInclusions(hesRatePlan.getInclusions());
			ratePlan.setVendorRatePlanCode(hesRatePlan.getRpcc());
			ratePlan.setCorpApprovalInfo(commonResponseTransformer.buildCorpApprovalInfo(hesRatePlan.getDisplayFare().getCorpMetaData()));
			ratePlan.setAddons(commonResponseTransformer.getAddons(hesRatePlan.getAddOns()));
			ratePlan.setStaycationDeal(hesRatePlan.isStaycationDeal());
			ratePlan.setCheckinPolicy(hesRatePlan.getCheckinPolicy());
			ratePlan.setConfirmationPolicy(hesRatePlan.getConfirmationPolicy());
			ratePlan.setInstantConfirmation(hesRatePlan.getInstantConfirmation());
			ratePlan.setQuickBook(getQuickBook(hesRatePlan.getQuickBook()));
			ratePlan.setCancellationTimeline(commonResponseTransformer.buildCancellationTimeline(hesRatePlan.getCancellationTimeline()));
			ratePlan.setCancellationPolicyTimeline(commonResponseTransformer.buildCancellationPolicyTimeline(hesRatePlan.getCancellationTimeline()));
			if(ratePlan.getCancellationTimeline() != null)
				ratePlan.getCancellationTimeline().setTitle(polyglotService.getTranslatedData("RATEPLAN_CANCELLATION_POLICY"));
			/*
			 * myPartner change log : commonModifierResponse floated down
			 * */
			ratePlan.setPersuasions(getRatePlanPersuasion(ratePlan,hesRatePlan,funnelSource, commonModifierResponse, isLuxeHotel));
			ratePlan.setTariffs(getTariffs(
					rpcRatePlanMap.get(rpc), expData, askedCurrency,sellableType, days, funnelSource,
					groupBookingPrice, myPartner, listingType
			));
			BNPLVariant bnplVariant = hesRatePlan.getBnplVariant();
			ratePlan.setRatePlanPersuasionsMap(buildRatePlanPersuasionsMap(hesRatePlan,commonModifierResponse));
			ratePlan.setTariffPersuasion(hesRatePlan.getTariffPersuasions());
			ratePlan.setCancellationPolicy(utility.transformCancellationPolicy(hesRatePlan.getCancelPenaltyList(), false, bnplVariant, null, polyglotService.getTranslatedData(ConstantsTranslation.CANCELLATION_POLICY_NR_NON_INSTANT_TEXT), ap));
			ratePlan.setName(utility.getRatePlanName(hesRatePlan.getMealPlans(), ratePlan.getCancellationPolicy(),sellableType, listingType, expData));
			ratePlan.setLucky(hesRatePlan.isLuckyRateAvailable());
			ratePlanCodeAndNameMap.put(rpc, utility.getRatePlanName(hesRatePlan.getMealPlans(), null, sellableType, listingType, expData));
			if(LINKED_RATE_EXP_ONE_VARIANT.equalsIgnoreCase(linkedRateExperimentValue) && CollectionUtils.isNotEmpty(hesRatePlan.getLinkedRates())){

				String parent = hesRatePlan.getLinkedRates().get(0).getPricingKey();
				if(CollectionUtils.isNotEmpty(rpcRatePlanMap.get(parent))){
					RatePlan parentRatePlan = rpcRatePlanMap.get(parent).get(0);
					if(Objects.nonNull(parentRatePlan)){
						String parentPartialRefundText = utility.buildPartialRefundDateText(parentRatePlan.getCancellationTimeline());
						BookedCancellationPolicy parentCancellationPolicy = utility.transformCancellationPolicy(
								parentRatePlan.getCancelPenaltyList(),
								false,
								bnplVariant,
								null,
								polyglotService.getSafeTranslatedData(ConstantsTranslation.CANCELLATION_POLICY_NR_NON_INSTANT_TEXT),
								ap
						);
						String linkedRatePlanName = utility.getRatePlanName(parentRatePlan.getMealPlans(), parentCancellationPolicy, sellableType, listingType, expData);
						ratePlan.setLinkedRatePlanName(utility.replaceWithFreeCancellation(linkedRatePlanName));
						buildParentLinkedRates(ratePlan,hesRatePlan.getLinkedRates());
					}
				}
			}
			List<com.mmt.hotels.model.response.pricing.LinkedRate> linkedRates = hesRatePlan.getLinkedRates();
			if (linkedRates != null && linkedRates.size() > 0) {

				if ("UPSELL".equalsIgnoreCase(linkedRates.get(0).getType())) {
					UpsellRatePlan upsellRatePlan = new UpsellRatePlan();
					upsellRatePlan.setHeading(polyglotService.getSafeTranslatedData(ConstantsTranslation.HOTEL_UPSELL_CARD_TITLE));
					upsellRatePlan.setSubHeading(polyglotService.getSafeTranslatedData(ConstantsTranslation.HOTEL_UPSELL_CARD_SUBTITLE));

					List<UpsellRatePlanOptions> upsellOptions = new ArrayList<UpsellRatePlanOptions>();

					for (com.mmt.hotels.model.response.pricing.LinkedRate linkedRate : linkedRates) {
						List<LinkedRateDetail> linkedRateDetails = linkedRate.getLinkedRateDetail();
						if (linkedRateDetails == null) continue;
						UpsellRatePlanOptions upsellRatePlanOptions = new UpsellRatePlanOptions();
						StringBuilder titleStrBuilder = new StringBuilder();
						StringBuilder subTitleBuilder = new StringBuilder();

						for (int i = 0; i < linkedRateDetails.size(); i++) {
							LinkedRateDetail linkedRateDetail = linkedRateDetails.get(i);

							String titleDefaultString = "";
							if (linkedRateDetail.getLinkedRateValue() != null) {
								titleDefaultString = buildTitleMealTitleDefaultString(linkedRateDetail.getLinkedRateValue());
							}
							String subtitleDefaultString = buildUpSellSubTitle(linkedRateDetail, linkedRateDetail.getSubType(), linkedRateDetail.getLinkedRateValue(),rpcRatePlanMap,linkedRate.getPricingKey(),ap);

							if (StringUtils.isNotEmpty(titleDefaultString)) {
								if (StringUtils.isNotEmpty(titleStrBuilder.toString())) {
									titleStrBuilder.append("|");
								}
								titleStrBuilder.append(titleDefaultString);
							}

							if (StringUtils.isNotEmpty(subtitleDefaultString)) {
								if (StringUtils.isNotEmpty(subTitleBuilder.toString())) {
									subTitleBuilder.append("|");
								}
								subTitleBuilder.append(subtitleDefaultString);
							}
							if (linkedRateDetail.getLinkedRateValue() != null) {
								upsellRatePlanOptions.setSheetTitle(buildUpsellSheetTitleMealTitleDefaultString(linkedRateDetail.getLinkedRateValue()));
							}
						}


						upsellRatePlanOptions.setRpc(linkedRate.getPricingKey());
						upsellRatePlanOptions.setPriceLabel(polyglotService.getSafeTranslatedData(ConstantsTranslation.MEAL_SHEET_PRICE_LABEL));
						upsellRatePlanOptions.setSubTitle(subTitleBuilder.toString());
						upsellRatePlanOptions.setTitle(titleStrBuilder.toString());
						upsellOptions.add(upsellRatePlanOptions);
						upsellRatePlan.setUpsellOptions(upsellOptions);
					}

					ratePlan.setUpsellRatePlan(upsellRatePlan);
				}

			}


			ratePlan.setLinkedRatePlanAvailable(CollectionUtils.isNotEmpty(hesRatePlan.getChildLinkedRates()));

			if(LINKED_RATE_EXP_ONE_VARIANT.equalsIgnoreCase(linkedRateExperimentValue) && CollectionUtils.isNotEmpty(hesRatePlan.getChildLinkedRates())){
				buildChildLinkedRates(ratePlan,hesRatePlan.getChildLinkedRates());
			}
			ratePlan.setSellableType(StringUtils.isNotBlank(sellableType)?sellableType: "Room");
			if(null != hesRatePlan.getSegmentId())
				ratePlan.setSegmentId(hesRatePlan.getSegmentId());
			if (isPackageRoom || isOccassion) {
				setPackageRoomSpecificRatePlanInfo((PackageSelectRoomRatePlan) ratePlan,
						(PackageRoomRatePlan) hesRatePlan);
			}
			if(utility.isSPKGExperimentOn(expData) && hesRatePlan.getPackageRoomRatePlan()) {
				ratePlan.setPackageRateAvailable(true);
				ratePlan.setType(elitePackageType);
				ratePlan.setHighlightImage(elitePackageIconUrl);
				utility.transformInclusionsForPackageRatePlan(ratePlan.getInclusionsList());
			}
			if (utility.isShowOccassionPackagesPlanExperimentOn(expData) &&
					hesRatePlan != null && hesRatePlan.getOccassionRoomPersuasion() != null) {
				ratePlan.setType(hesRatePlan.getOccassionRoomPersuasion().getOccassionPersuaionId());
				ratePlan.setHighlightImage(hesRatePlan.getOccassionRoomPersuasion().getHighlightOccassionImageUrl());
			}
			ratePlan.setAllInclusiveRate(hesRatePlan.isAllInclusiveRate());
			//Added paymode policy for GI
			buildCancelPolicyDescriptionPaymod(ratePlan);


			/*GIHTL-14388 HCP ENCRYPTED*/
			if (hesRatePlan.getDisplayFare() != null && hesRatePlan.getDisplayFare().getDisplayPriceBreakDown() != null
					&& hesRatePlan.getDisplayFare().getDisplayPriceBreakDown().getHotelierCouponDiscount() > 0
					&& StringUtils.isNotEmpty(hesRatePlan.getDisplayFare().getDisplayPriceBreakDown().getHCPEncrypted())) {
				ratePlan.setHCP(hesRatePlan.getDisplayFare().getDisplayPriceBreakDown().getHCPEncrypted());
			}
			//Creating Node For MmtExclusive detail Page
			if(isUserGCCAndMmtExclusive) {
				Map<String, MmtExclusive> card = new HashMap<>();
				card.put(Constants.GCC_EXCLUSIVE, utility.buildMmtExclusiveNode(hesRatePlan.getInclusions()));
				ratePlan.setCards(card);
			}
			// Check if trafficSource is "flywheel", if not return null
			if (commonModifierResponse != null
					&& commonModifierResponse.getTrafficSource() != null
					&& Constants.TRAFFIC_SOURCE_FLYWHEEL.equalsIgnoreCase(commonModifierResponse.getTrafficSource())
					&& hotelRates != null) {
				boolean showTransfersFeeTxt = false;
				if (MapUtils.isNotEmpty(expData) && expData.containsKey(Constants.TRANSFERS_FEE_TEXT_KEY)) {
					showTransfersFeeTxt = Constants.EXP_TRUE_VALUE.equalsIgnoreCase(expData.get(Constants.TRANSFERS_FEE_TEXT_KEY));
				}
				// Set additionalFees (AdditionalMandatoryCharges) node
				ratePlan.setAdditionalFees(buildAdditionalChargesForRatePlan(hesRatePlan, hotelRates.getCurrencyCode(), hotelRates.getPropertyType(), hotelRates.getLocusCityCode(), showTransfersFeeTxt));
			}
			ratePlans.add(ratePlan);
		}
		return ratePlans;
	}

	private AdditionalMandatoryCharges buildAdditionalChargesForRatePlan(com.mmt.hotels.model.response.pricing.RatePlan hesRatePlan, String currencyCode, String propertyType, String locusCityCode, boolean showTransfersFeeTxt) {
		SupplierDetails supplierDetails = hesRatePlan.getSupplierDetails();
		AdditionalChargesBO additionalChargesBO = new AdditionalChargesBO.Builder()
				.buildUserCurrency(currencyCode)
				.buildHotelierCurrency(supplierDetails != null ? supplierDetails.getHotelierCurrencyCode() : "INR")
				.buildPropertyType(propertyType)
				.buildAdditionalFees(hesRatePlan.getAdditionalFees())
				.buildConversionFactor(hesRatePlan.getDisplayFare() != null ? hesRatePlan.getDisplayFare().getConversionFactor() : 1.0)
				.buildCityCode(locusCityCode)
				.build();
		return commonResponseTransformer.buildAdditionalCharges(additionalChargesBO, showTransfersFeeTxt);
	}

	private String buildUpSellSubTitle(LinkedRateDetail linkedRateDetail,LinkedRateSubType linkedRateSubType, LinkedRateValue linkedRateValue,Map<String, List<com.mmt.hotels.model.response.pricing.RatePlan>> rpcRatePlanMap,String pricingKey,int ap) {
		switch (linkedRateSubType) {
			case CANCELLATION_FC_NR:
				return "";
			case MEAL_DEFAULT:
				return "";
			case MEAL_RACKRATE:
				return polyglotService.getSafeTranslatedData(HOTEL_UPSELL_MEAL_RACKRATE).replace(ConstantsTranslation.PERCENTAGE.toUpperCase(),String.format("%.1f", linkedRateDetail.getRackRateSavingPct()));
			case MEAL_RATING:
				return polyglotService.getSafeTranslatedData(HOTEL_UPSELL_MEAL_RATING).replace(ConstantsTranslation.MEAL_RATING.toUpperCase(), String.valueOf(linkedRateDetail.getFoodRating()));
			case MEAL_RESTAURANT:
				return polyglotService.getSafeTranslatedData(HOTEL_UPSELL_MEAL_RESTAURANT).replace(ConstantsTranslation.RESTAURANT_DISTANCE.toUpperCase(), String.valueOf(linkedRateDetail.getEateriesDistance()));
			case CANCELLATION_DEFAULT:
				if (CollectionUtils.isNotEmpty(rpcRatePlanMap.get(pricingKey))) {
					RatePlan parentRatePlan = rpcRatePlanMap.get(pricingKey).get(0);
					if(Objects.nonNull(parentRatePlan)) {
						BNPLVariant bnplVariant = parentRatePlan.getBnplVariant();
						BookedCancellationPolicy parentCancellationPolicy = utility.transformCancellationPolicy(
								parentRatePlan.getCancelPenaltyList(),
								false,
								bnplVariant,
								null,
								polyglotService.getSafeTranslatedData(ConstantsTranslation.CANCELLATION_POLICY_NR_NON_INSTANT_TEXT),ap
						);

						return parentCancellationPolicy.getText();

					}
				}
				return "";

			default:
				return "";
		}
	}
	private String buildUpsellSheetTitleMealTitleDefaultString(LinkedRateValue linkedRateValue) {
		String sheetTitleElement = buildTitleMealTitleDefaultString(linkedRateValue);
		return polyglotService.getSafeTranslatedData(HOTEL_UPSELL_MEAL_SHEET_TITLE).replace(ConstantsTranslation.UPSELL_LINKED_VALUE.toUpperCase(),sheetTitleElement);

	}
	private String buildTitleMealTitleDefaultString(LinkedRateValue linkedRateValue) {
		switch(linkedRateValue){
			case DN:
				return polyglotService.getSafeTranslatedData(MEAL_TITLE_DN);
			case EP:
				return polyglotService.getSafeTranslatedData(MEAL_TITLE_EP);
			case CB:
				return polyglotService.getSafeTranslatedData(MEAL_TITLE_CB);
			case LCP:
				return polyglotService.getSafeTranslatedData(MEAL_TITLE_LCP);
			case LN:
				return polyglotService.getSafeTranslatedData(MEAL_TITLE_LN);
			case LD:
				return polyglotService.getSafeTranslatedData(MEAL_TITLE_LD);
			case TMAP:
				return polyglotService.getSafeTranslatedData(MEAL_TITLE_TMAP);
			case JP:
				return polyglotService.getSafeTranslatedData(MEAL_TITLE_JP);
			case AP:
				return polyglotService.getSafeTranslatedData(MEAL_TITLE_AP);
			case CP:
				return polyglotService.getSafeTranslatedData(MEAL_TITLE_CP);
			case MAP:
				return polyglotService.getSafeTranslatedData(MEAL_TITLE_MAP);
			case SMAP:
				return polyglotService.getSafeTranslatedData(MEAL_TITLE_SMAP);
			case AI:
				return polyglotService.getSafeTranslatedData(MEAL_TITLE_AI);
			case FREE_CANCELLATION:
				return  polyglotService.getSafeTranslatedData(HOTEL_UPSELL_MEAL_CANCELLATION_TEXT);
			case NON_REFUNDABLE:
				return polyglotService.getSafeTranslatedData(MEAL_TITLE_NON_REFUNDABLE);

			default:
				return "";
		}
	}



	private void buildCancelPolicyDescriptionPaymod(SelectRoomRatePlan ratePlan) {
		if (StringUtils.isEmpty(ratePlan.getPayMode())){
			return;
		}
		PaymentModeDescriptionPolicy paymentModeDescriptionPolicy = null;
		switch (ratePlan.getPayMode()) {
			case PAH_WITH_CC:
				paymentModeDescriptionPolicy = new PaymentModeDescriptionPolicy();
				paymentModeDescriptionPolicy.setTitleText("Pay at Hotel(CC) | Available on this plan");
				paymentModeDescriptionPolicy.setTitleMsg("Pay to the hotel - Credit card required");
				paymentModeDescriptionPolicy.setSubMsgText1("No Payment needed at the time of booking");
				paymentModeDescriptionPolicy.setSubMsgText2("Booking amount will be charged to your card by the hotel at any time before check-in");
				break;
			case PAS:
				break;
			default:
				paymentModeDescriptionPolicy = new PaymentModeDescriptionPolicy();
				paymentModeDescriptionPolicy.setTitleText("Pay at Hotel | Available on this plan");
				paymentModeDescriptionPolicy.setTitleMsg("Pay at the hotel using cash/card");
				paymentModeDescriptionPolicy.setSubMsgText1("No Payment needed at the time of booking");
				paymentModeDescriptionPolicy.setSubMsgText2("Pay the booking amount directly at the hotel");
		}
		ratePlan.setPayModePolicy(paymentModeDescriptionPolicy);
	}

	private void setPackageRoomSpecificRatePlanInfo(PackageSelectRoomRatePlan ratePlan,
																									PackageRoomRatePlan packageRoomRatePlan){
		if (packageRoomRatePlan == null) {
			return;
		}
		ratePlan.setExtendedCheckInDate(packageRoomRatePlan.getExtendedCheckInDate());
		ratePlan.setExtendedCheckOutDate(packageRoomRatePlan.getExtendedCheckOutDate());
		ratePlan.setPackageInclusionDetails(convertPackageInclusionDetails(packageRoomRatePlan.getPackageInclusionDetails()));
	}

	private PackageInclusionDetails convertPackageInclusionDetails(com.mmt.hotels.model.response.pricing.PackageInclusionDetails packageInclusionDetails){
		if(packageInclusionDetails == null){
			return null;
		}
		PackageInclusionDetails packageInclusionDetailsCG = new PackageInclusionDetails();
		BeanUtils.copyProperties(packageInclusionDetails, packageInclusionDetailsCG);
		return packageInclusionDetailsCG;
	}

	private Map<String,PersuasionResponse> buildRatePlanPersuasionsMap(RatePlan ratePlanHes,CommonModifierResponse commonModifierResponse) {

		Map<String,PersuasionResponse> persuasionMap = new HashMap<>();
		if(ratePlanHes!=null && ratePlanHes.getMpFareHoldStatus()!=null && ratePlanHes.getMpFareHoldStatus().isHoldEligible()) {
			LOGGER.debug("MPFAREHOLD Building details page persuasion for  {}", ratePlanHes.getMpFareHoldStatus());
			int bookingValue = (int)(ratePlanHes.getMpFareHoldStatus().getBookingAmount());

			PersuasionResponse fareHoldPersuasion = new PersuasionResponse();
			fareHoldPersuasion.setTitle(MessageFormat.format(polyglotService.getTranslatedData(ConstantsTranslation.BOOK_NOW_PERSUASION_TITLE),
					String.valueOf(bookingValue)));
			Hover hover = new Hover();
			if(ratePlanHes.getMpFareHoldStatus().getExpiry()!=null) {
				long expiry = ratePlanHes.getMpFareHoldStatus().getExpiry();
				hover.setTitleText(MessageFormat.format(polyglotService.getTranslatedData(ConstantsTranslation.BOOK_NOW_PERSUASION_HOVER_TITLE),
						dateUtil.convertEpochToDateTime(expiry, dateUtil.DD_MMM_hh_mm_a)));
			}
			hover.setSubText(polyglotService.getTranslatedData(ConstantsTranslation.BOOK_NOW_PERSUASION_HOVER_SUB_TITLE));
			fareHoldPersuasion.setHover(hover);
			persuasionMap.put(Constants.BOOK_NOW_PERSUASION_KEY, fareHoldPersuasion);
		}
		boolean isMyPartnerRequest = (commonModifierResponse!=null) && (commonModifierResponse.getExtendedUser()!=null) && Utility.isMyPartnerRequest(commonModifierResponse.getExtendedUser().getProfileType(), commonModifierResponse.getExtendedUser().getAffiliateId());
		if(isMyPartnerRequest)
		{
			if(null!=ratePlanHes.getDisplayFare() && null != ratePlanHes.getDisplayFare().getDisplayPriceBreakDown() && null!= ratePlanHes.getDisplayFare().getDisplayPriceBreakDown().getCouponInfo()) {
				BestCoupon coupon = ratePlanHes.getDisplayFare().getDisplayPriceBreakDown().getCouponInfo();
				//If manthan is sending rewardbonus in mmtVals node, coupon will be set as CTW->Cashback Amount(Cashback offer persuasion condition)
				LOGGER.debug("Manthan HybridDiscounts {}",coupon.getHybridDiscounts());
				boolean isCashbackAmtAvailable=MapUtils.isNotEmpty(coupon.getHybridDiscounts()) && coupon.getHybridDiscounts().containsKey("CTW");
				if(StringUtils.isNotBlank(coupon.getLoyaltyOfferMessage()) || isCashbackAmtAvailable) {
					buildLoyaltyCashbackPersuasions(coupon,persuasionMap);
				}
			}
		}
		return persuasionMap;
	}
	private List<PersuasionResponse> getRatePlanPersuasion(SelectRoomRatePlan ratePlan,RatePlan ratePlanHES,String funnelSource, CommonModifierResponse commonModifierResponse, boolean isLuxeHotel) {
		//adding CTRIP noninstant confirmation in rateplan persuasion for now. later to be replaced with  persuasions from PErsuasion engine
		List<PersuasionResponse> persuasions = null;
		boolean isBelowRatePlanAdded = false;


		/*
		 * myPartner change log :
		 * 	Adding myPartner persuasion. It depends on the segmentId of the ratePlan
		 * 	These are added only for CTA profile
		 *
		 * 	The values are added in accordance with the other persuasion object standards. Moving forward, some persuasions
		 *  object will be extracted out to a function since they've been duplicated across conditions
		 *
		 * 	Not null constraints are added to handle test cases
		 * */

		if(Objects.nonNull(commonModifierResponse) && Objects.nonNull(commonModifierResponse.getExtendedUser()) &&  Utility.isMyPartnerRequest(commonModifierResponse.getExtendedUser().getProfileType(), commonModifierResponse.getExtendedUser().getAffiliateId()) &&
				mypatExclusiveRateSegmentIdList.contains(ratePlanHES.getSegmentId())) {
			persuasions = new ArrayList<>();
			PersuasionResponse persuasion = new PersuasionResponse();
			persuasion.setPersuasionText("Partner Exclusive Rate");
			persuasion.setId("MY_PARTNER_SEGMENTS");
			persuasion.setPlaceholderId("PC_RIGHT_3");
			persuasion.setTemplate("PARTNER_PERSUASION");
			persuasion.setStyle(new Style());
			persuasion.getStyle().setTextColor("#fff");
			persuasion.getStyle().setBgGradient(new BGGradient());
			persuasion.getStyle().getBgGradient().setAngle("H");
			persuasion.getStyle().getBgGradient().setColor(Arrays.asList("#f5515f","#9f0469"));
			persuasion.setSubText("Get the best rates possible");
			persuasions.add(persuasion);
		}
		if(ratePlan.getConfirmationPolicy()!= null && StringUtils.isNotBlank(ratePlan.getConfirmationPolicy().getShortDescription())){
			if(persuasions == null)
				persuasions = new ArrayList<>();
			PersuasionResponse persuasion = new PersuasionResponse();
			persuasion.setPersuasionText(ratePlan.getConfirmationPolicy().getShortDescription());
			persuasion.setId("CONFIRMATION_POLICY");
			persuasion.setPlaceholderId("belowRatePlan");
			persuasion.setTemplate("IMAGE_TEXT_H");
			persuasion.setStyle(new Style());
			persuasion.getStyle().setTextColor("#8b572a");
			persuasion.getStyle().setFontSize("SMALL");
			persuasions.add(persuasion);
			isBelowRatePlanAdded  = true;

		}

		if (!isBelowRatePlanAdded && CollectionUtils.isNotEmpty(ratePlan.getInclusionsList()) &&  ratePlan.getInclusionsList().stream().filter(a-> a.isOnOffer()).findFirst().isPresent()){
			BookedInclusion inclusion = ratePlan.getInclusionsList().stream().filter(a-> a.isOnOffer()).findFirst().get();
			if(persuasions == null)
				persuasions = new ArrayList<>();
			PersuasionResponse persuasion = new PersuasionResponse();
			persuasion.setPersuasionText(inclusion.getText());
			persuasion.setId("PACKAGE");
			persuasion.setTemplate("IMAGE_TEXT_H");
			persuasion.setPlaceholderId("belowRatePlan");
			persuasion.setStyle(new Style());
			persuasion.getStyle().setTextColor("#8b572a");
			persuasion.getStyle().setFontSize("SMALL");
			persuasions.add(persuasion);
		}

		if(ratePlanHES!= null && ratePlanHES.getCorpMetaData() != null && ratePlanHES.getCorpMetaData().getValidationPayload()!= null){

			if(!ratePlanHES.getCorpMetaData().getValidationPayload().isWithinPolicy()) {
				if(persuasions == null)
					persuasions = new ArrayList<>();
				PersuasionResponse persuasion = new PersuasionResponse();
				persuasion.setPersuasionText(polyglotService.getTranslatedData("OUT_OF_POLICY_BOLD"));
				persuasion.setPlaceholderId("topRight");
				persuasion.setHtml(true);
				persuasion.setId("OOP");
				persuasion.setTemplate("OVAL");
				persuasion.setStyle(new Style());
				persuasion.getStyle().setTextColor("#ffffff");
				persuasion.getStyle().setFontSize("SMALL");
				persuasion.getStyle().setBgColor("#d0021b");
				persuasions.add(persuasion);
			}

			if(CollectionUtils.isNotEmpty(ratePlanHES.getCorpMetaData().getTags())){
				if(persuasions == null)
					persuasions = new ArrayList<>();
				PersuasionResponse persuasion = new PersuasionResponse();
				persuasion.setPersuasionText(ratePlanHES.getCorpMetaData().getTags().get(0).getTagName());
				persuasion.setHtml(true);
				persuasion.setPlaceholderId("rightBottom");
				persuasion.setId("SPECIALRATE");
				persuasion.setTemplate("TEXT_WITH_BG_IMAGE");
				persuasion.setStyle(new Style());
				persuasion.getStyle().setTextColor("#ffffff");
				persuasion.getStyle().setFontSize("SMALL");
				persuasion.getStyle().setBgUrl("https://promos.makemytrip.com/images/myBiz/mybiz_special.png");
				persuasions.add(persuasion);
			}
		}


		if(ratePlanHES!= null && ratePlanHES.isStaycationDeal() && "GETAWAY".equalsIgnoreCase(funnelSource) ){
			if(persuasions == null)
				persuasions = new ArrayList<>();
			PersuasionResponse persuasion = new PersuasionResponse();
			persuasion.setPersuasionText(polyglotService.getTranslatedData("GETAWAY_DEAL_PERSUASION_TEXT"));
			persuasion.setPlaceholderId("rightBottom");
			persuasion.setId("STAYCATION");
			persuasion.setTemplate("TEXT_WITH_BG_IMAGE");
			persuasion.setStyle(new Style());
			persuasion.getStyle().setTextColor("#4a4a4a");
			persuasion.getStyle().setFontSize("SMALL");
			persuasion.getStyle().setBgUrl("https://promos.makemytrip.com/Hotels_product/Hotel_SR/iOS/DealBG.png");
			persuasions.add(persuasion);
		}

		if (ratePlanHES != null && ratePlanHES.getPreApproved() != null && ratePlanHES.getPreApproved()) {
			if (persuasions == null)
				persuasions = new ArrayList<>();
			PersuasionResponse persuasion = new PersuasionResponse();
			persuasion.setPersuasionText(polyglotService.getTranslatedData(RTB_PRE_APPROVED_TEXT));
			persuasion.setPlaceholderId("belowRatePlan");
			persuasion.setId("PACKAGE");
			persuasion.setTemplate("IMAGE_TEXT_H");
			persuasion.setStyle(new Style());
			persuasion.getStyle().setTextColor("#249995");
			persuasion.getStyle().setFontSize("SMALL");
			persuasions.add(persuasion);
		}

		if(ratePlanHES!=null && ratePlanHES.getSupplierDetails()!=null && supplierToRateSegmentMapping!=null && supplierToRateSegmentMapping.get(Constants.PACKAGE_RATE_KEY)!=null &&
				supplierToRateSegmentMapping.get(Constants.PACKAGE_RATE_KEY).get(ratePlanHES.getSupplierDetails().getSupplierCode())!=null
				&& (supplierToRateSegmentMapping.get(Constants.PACKAGE_RATE_KEY).get(ratePlanHES.getSupplierDetails().getSupplierCode()).isEmpty()
		|| supplierToRateSegmentMapping.get(Constants.PACKAGE_RATE_KEY).get(ratePlanHES.getSupplierDetails().getSupplierCode()).contains(ratePlanHES.getSegmentId()))){

			if (persuasions == null)
				persuasions = new ArrayList<>();
			PersuasionResponse persuasion = new PersuasionResponse();
			persuasion.setId(Constants.MYPARTNER_EXPEDIA_PKG_RATE);
			persuasion.setPersuasionText(polyglotService.getTranslatedData(PACKAGE_RATE_TEXT));
			persuasions.add(persuasion);
		}
		return persuasions;

	}

	private List<BookedInclusion> transformInclusions(RatePlan ratePlanHES , Map<String, String> mealPlanMap, int ap, boolean isBlockPah, Map<String, String> expData) {
		List<BookedInclusion> inclusions = new ArrayList<>();
		if (ratePlanHES==null){
			return inclusions;
		}
		List<MealPlan> mealPlan = ratePlanHES.getMealPlans();
		List<Inclusion> inclusionList = ratePlanHES.getInclusions();
		String supplierCode = ratePlanHES.getSupplierDetails()!= null ? ratePlanHES.getSupplierDetails().getSupplierCode() : "";
		List<Inclusion> inclusionListCopy = new ArrayList<>();
		if(CollectionUtils.isNotEmpty(inclusionList)) {
            inclusionListCopy.addAll(inclusionList);
		}

		boolean isMealPlanPresent = false;
		int count = 0;
		if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(inclusionList)) {
			inclusionList.sort(Comparator.comparing(Inclusion::getSegmentIdentifier,Comparator.nullsLast(Comparator.naturalOrder())));
			for (Inclusion inclusion : inclusionList) {
				if (StringUtils.isEmpty(inclusion.getValue()))
					continue;
				count++;
                BookedInclusion bookedInclusion = new BookedInclusion();
                bookedInclusion.setText(inclusion.getCode());
                bookedInclusion.setSubText(inclusion.getValue());
                bookedInclusion.setCode(inclusion.getCode());
				bookedInclusion.setLeafCategory(inclusion.getLeafCategory());
				if (StringUtils.isNotEmpty(inclusion.getCategory()) && (BREAKFAST.equalsIgnoreCase(inclusion.getCategory()) || LUNCH.equalsIgnoreCase(inclusion.getCategory()) || DINNER.equalsIgnoreCase(inclusion.getCategory()))) {
                    bookedInclusion.setCategory(MEAL);
                }
				if(StringUtils.isNotBlank(bookedInclusion.getText()) && bookedInclusion.getText().length() < 10 && !bookedInclusion.getText().equalsIgnoreCase(bookedInclusion.getSubText())){
					bookedInclusion.setText(bookedInclusion.getText() + " - " + bookedInclusion.getSubText());
				}
                bookedInclusion.setIconType(IconType.DEFAULT);
                bookedInclusion.setIconUrl("https://promos.makemytrip.com/Hotels_product/Persuasion_Icons/Dot.png");
                bookedInclusion.setSegmentIdentifier(inclusion.getSegmentIdentifier());
                if(count < 2 && !isMealPlanPresent  && CollectionUtils.isNotEmpty(mealPlan)) {
                    String inclusionText50 = inclusion.getValue().substring(0,inclusion.getValue().length() > 30 ? 30 : inclusion.getValue().length()).toLowerCase();
                    if( Constants.MEAL_PLAN_CODE_BREAKFAST.equalsIgnoreCase(mealPlan.get(0).getCode()) && (inclusionText50.contains("breakfast")) && inclusion.getValue().length() < 50 )
                        isMealPlanPresent = true;
                    else if (!Constants.MEAL_PLAN_CODE_ROOM_ONLY.equalsIgnoreCase(mealPlan.get(0).getCode()) &&
							!Constants.MEAL_PLAN_CODE_BED_ONLY.equalsIgnoreCase(mealPlan.get(0).getCode()) &&
							!Constants.MEAL_PLAN_CODE_ACC_ONLY.equalsIgnoreCase(mealPlan.get(0).getCode())
							&& (inclusionText50.contains("breakfast") || inclusionText50.contains("meals") ) && inclusion.getValue().length() < 50)
                        isMealPlanPresent = true;
                    if (isMealPlanPresent) {
                    	bookedInclusion.setInclusionCode(mealPlan.get(0).getCode());
                    	bookedInclusion.setType("MEAL_PLAN");
						bookedInclusion.setCategory(MEAL);
                    	bookedInclusion.setIconUrl("https://promos.makemytrip.com/Hotels_product/Hotel_SR/Android/drawable-hdpi/meal.png");
						if(ratePlanHES.getExtraGuestDetail()!=null && StringUtils.isNotEmpty(ratePlanHES.getExtraGuestDetail().getRatePlanExtraBedText())){
							bookedInclusion.setText(ratePlanHES.getExtraGuestDetail().getRatePlanExtraBedText());
							bookedInclusion.setCode(ratePlanHES.getExtraGuestDetail().getRatePlanExtraBedText());
							}
                    }
                }
                if ("Packages".equalsIgnoreCase(inclusion.getCategory()) || "Packages1".equalsIgnoreCase(inclusion.getCategory())
                		|| "Packages2".equalsIgnoreCase(inclusion.getCategory()) || "Packages3".equalsIgnoreCase(inclusion.getCategory())
                		|| "MMTBLACK".equalsIgnoreCase(inclusion.getCategory())) {
					bookedInclusion.setOnOffer(true);
					bookedInclusion.setIconUrl(inclusion.getImageURL());
					bookedInclusion.setCategory(USP);
				}
				if (BLACK_SEGMENT_IDENTIFIER.equalsIgnoreCase(inclusion.getSegmentIdentifier()) && TRUE.equalsIgnoreCase(expData.get(ExperimentKeys.GOTRIBE_REVAMP.getKey()))) {
					bookedInclusion.setIconUrl(inclusion.getImageURL());
					bookedInclusion.setCategory(inclusion.getCategory());
				}

                if (!"Packages3".equalsIgnoreCase(inclusion.getCategory()) && StringUtils.isNotEmpty(inclusion.getCategory()))
                	bookedInclusion.setBookable(true);
                if(bookedInclusion.getType()=="MEAL_PLAN")
				{
					inclusions.add(bookedInclusion);
				}
                else {
					inclusions.add(0,bookedInclusion);
				}
			}
		}

		//GIHTL-15565 : CLEAN UP -> NO_MEAL_INCLUSION_REMOVE Is always false. So, this block of code is not required.
//		Map<String,String> experimentDataMap = expData;
//		if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(mealPlan) && MapUtils.isNotEmpty(mealPlanMap)
//		&& !(MapUtils.isNotEmpty(expData) && StringUtils.isNotBlank(expData.get(Constants.NO_MEAL_INCLUSION_REMOVE)) && Constants.TRUE.equalsIgnoreCase(expData.get(Constants.NO_MEAL_INCLUSION_REMOVE)))) {
//			if (Constants.MEAL_PLAN_CODE_ROOM_ONLY.equalsIgnoreCase(mealPlan.get(0).getCode()) ||
//					Constants.MEAL_PLAN_CODE_BED_ONLY.equalsIgnoreCase(mealPlan.get(0).getCode()) ||
//					Constants.MEAL_PLAN_CODE_ACC_ONLY.equalsIgnoreCase(mealPlan.get(0).getCode())
//			) {
//				BookedInclusion noMeanInclusion = new BookedInclusion();
//				noMeanInclusion.setText(polyglotService.getTranslatedData(mealPlanMap.get(mealPlan.get(0).getCode())));
//				noMeanInclusion.setCode(noMeanInclusion.getText());
//				if (ap < apLimitForInclusionIcons) {
//					noMeanInclusion.setIconType(IconType.DEFAULT);
//					noMeanInclusion.setIconUrl("https://promos.makemytrip.com/Hotels_product/Persuasion_Icons/Dot.png");
//				} else {
//					noMeanInclusion.setIconType(IconType.CROSS);
//					noMeanInclusion.setIconUrl("https://promos.makemytrip.com/Hotels_product/Hotel_SR/Android/drawable-hdpi/meal.png");
//				}
//				noMeanInclusion.setInclusionCode(mealPlan.get(0).getCode());
//				noMeanInclusion.setCategory(MEAL);
//                inclusions.add(0, noMeanInclusion);
//            } else if (!isMealPlanPresent && StringUtils.isNotBlank(supplierCode) && !Constants.SUPPLIER_INGO.equalsIgnoreCase(supplierCode)) {
//                BookedInclusion noMeanInclusion = new BookedInclusion();
//
//				if(ratePlanHES.getExtraGuestDetail()!= null && StringUtils.isNotEmpty(ratePlanHES.getExtraGuestDetail().getRatePlanExtraBedText())){
//					noMeanInclusion.setText(ratePlanHES.getExtraGuestDetail().getRatePlanExtraBedText());
//					noMeanInclusion.setCode(ratePlanHES.getExtraGuestDetail().getRatePlanExtraBedText());
//				}
//				else{
//					noMeanInclusion.setText(mealPlanMap.containsKey(mealPlan.get(0).getCode()) ? polyglotService.getTranslatedData(mealPlanMap.get(mealPlan.get(0).getCode())) : mealPlan.get(0).getValue());
//					noMeanInclusion.setCode(noMeanInclusion.getText());
//
//				}
//
//                noMeanInclusion.setInclusionCode(mealPlan.get(0).getCode());
//                noMeanInclusion.setType("MEAL_PLAN");
//				noMeanInclusion.setCategory(MEAL);
//				noMeanInclusion.setIconUrl("https://promos.makemytrip.com/Hotels_product/Hotel_SR/Android/drawable-hdpi/meal.png");
//				inclusions.add(0, noMeanInclusion);
//			}
//		}

		String region = MDC.get(MDCHelper.MDCKeys.REGION.getStringValue());
		if (ratePlanHES.getPaymentDetails()!= null && ratePlanHES.getPaymentDetails().getPaymentMode()!=null &&
				!Constants.PAS.equalsIgnoreCase(ratePlanHES.getPaymentDetails().getPaymentMode().getMappedPayMode())){
			// Don't remove PAH and Book at zero inclusion from this place, as it is used further to form other nodes.
			BookedInclusion pahInclusion = new BookedInclusion();
			if(AE.equalsIgnoreCase(region)){
				pahInclusion.setCode(pahGccText);
				pahInclusion.setText(polyglotService.getTranslatedData(PAH_GCC_TEXT));
			}
			else if(Constants.PAH_WITHOUT_CC.equalsIgnoreCase(ratePlanHES.getPaymentDetails().getPaymentMode().name())) {
				pahInclusion.setText(polyglotService.getTranslatedData(PAH_WITHOUT_CC_TEXT_GI));
				pahInclusion.setCode(pahwithoutccText);
			}else{
				pahInclusion.setCode(pahwithccText);
				pahInclusion.setText(polyglotService.getTranslatedData(PAH_WITH_CC_TEXT_GI));
			}

			pahInclusion.setIconType(IconType.DEFAULT);
			pahInclusion.setIconUrl(ICON_URL_PAH_INCLUSION);
			pahInclusion.setInclusionCode(ratePlanHES.getPaymentDetails().getPaymentMode().getMappedPayMode());
			pahInclusion.setType(PAY_MODE);
			pahInclusion.setCategory(ZPN);
			inclusions.add(0, pahInclusion);
		}

        if (CollectionUtils.isNotEmpty(ratePlanHES.getCancelPenaltyList()) && ratePlanHES.getCancelPenaltyList().get(0).getCancellationType()!=null
		  && ratePlanHES.getCancelPenaltyList().get(0).getCancellationType() ==  CancellationType.FREE_CANCELLATON
		  && ratePlanHES.isBnplApplicable() && !isBlockPah && ratePlanHES.getPaymentDetails()!= null && ratePlanHES.getPaymentDetails().getPaymentMode() != PaymentMode.PAH_WITHOUT_CC) {


        	/* For FCZPN add an inclusion at start */
			// Don't remove PAH and Book at zero inclusion from this place, as it is used further to form other nodes.
        	BookedInclusion fczpnInlclusions = new BookedInclusion();
			utility.setInclusionCodeAndText(fczpnInlclusions,ratePlanHES.getBnplVariant(),region);
			fczpnInlclusions.setType(Constants.CANCELLATION_TYPE_FCZPN);
        	fczpnInlclusions.setIconType(IconType.DEFAULT);
        	fczpnInlclusions.setIconUrl("https://promos.makemytrip.com/Hotels_product/Persuasion_Icons/Dot.png");
        	fczpnInlclusions.setInclusionCode(Constants.CANCELLATION_TYPE_FCZPN);
			fczpnInlclusions.setCategory(ZPN);
        	inclusions.add(0,fczpnInlclusions);
		}
		if (ratePlanHES != null && ratePlanHES.getFreeChildCount() > 0 && StringUtils.isNotEmpty(ratePlanHES.getFreeChildText())) {
			BookedInclusion freeChildInclusion = utility.getFreeChildInclusion(ratePlanHES.getFreeChildText(),INCLUSIONS_DEFAULT_DOT_ICON_URL);
			inclusions.add(0, freeChildInclusion);
		}
		//		We have added category for inclusions that are made so that we can order them in a specific order based on it
		if(utility.isRatePlanRedesign(expData)){
			inclusions = utility.reorderInclusions(inclusions);
		}


		// 		======================= new logic for Inclusions =========================

		if(DOM_COUNTRY.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.REGION.getStringValue())) && org.apache.commons.collections4.CollectionUtils.isNotEmpty(inclusions)) {
			inclusions.clear();
			if (ratePlanHES.getPaymentDetails() != null && ratePlanHES.getPaymentDetails().getPaymentMode() != null &&
					!Constants.PAS.equalsIgnoreCase(ratePlanHES.getPaymentDetails().getPaymentMode().getMappedPayMode())) {
				// Don't remove PAH and Book at zero inclusion from this place, as it is used further to form other nodes.
				BookedInclusion pahInclusion = new BookedInclusion();
				if (AE.equalsIgnoreCase(region)) {
					pahInclusion.setCode(pahGccText);
					pahInclusion.setText(polyglotService.getTranslatedData(ConstantsTranslation.PAH_GCC_TEXT));
				} else if (Constants.PAH_WITHOUT_CC.equalsIgnoreCase(ratePlanHES.getPaymentDetails().getPaymentMode().name())) {
					pahInclusion.setCode(pahwithoutccText);
					pahInclusion.setText(polyglotService.getTranslatedData(ConstantsTranslation.PAH_WITHOUT_CC_TEXT_GI));
				} else {
					pahInclusion.setCode(pahwithccText);
					pahInclusion.setText(polyglotService.getTranslatedData(PAH_WITH_CC_TEXT_GI));
				}

				pahInclusion.setIconType(IconType.DEFAULT);
				pahInclusion.setIconUrl(ICON_URL_PAH_INCLUSION);
				pahInclusion.setInclusionCode(ratePlanHES.getPaymentDetails().getPaymentMode().getMappedPayMode());
				pahInclusion.setType("PAY_MODE");
				pahInclusion.setCategory(ZPN);
				inclusions.add(0, pahInclusion);
			}

//			buildNoMealInclusions(expData, mealPlan, mealPlanMap, ratePlanHES, inclusions, isMealPlanPresent, supplierCode, ap, true);

			if (CollectionUtils.isNotEmpty(ratePlanHES.getCancelPenaltyList()) && ratePlanHES.getCancelPenaltyList().get(0).getCancellationType()!=null
					&& ratePlanHES.getCancelPenaltyList().get(0).getCancellationType() ==  CancellationType.FREE_CANCELLATON
					&& ratePlanHES.isBnplApplicable() && !isBlockPah ) {
				// Don't remove PAH and Book at zero inclusion from this place, as it is used further to form other nodes.
				/* For FCZPN add an inclusion at start */
				BookedInclusion fczpnInlclusions = new BookedInclusion();
				utility.setInclusionCodeAndText(fczpnInlclusions, ratePlanHES.getBnplVariant(),region);
				fczpnInlclusions.setType(Constants.CANCELLATION_TYPE_FCZPN);
				fczpnInlclusions.setIconType(IconType.DEFAULT);
				fczpnInlclusions.setIconUrl(INCLUSIONS_DEFAULT_DOT_ICON_URL);
				fczpnInlclusions.setInclusionCode(Constants.CANCELLATION_TYPE_FCZPN);
				fczpnInlclusions.setCategory(ZPN);
				inclusions.add(0,fczpnInlclusions);
			}

			List<BookedInclusion> losInclusions = new ArrayList<>();
			for(Inclusion inclusion : inclusionListCopy) {
				BookedInclusion bookedInclusion = new BookedInclusion();
				bookedInclusion.setCode(inclusion.getCode());
				bookedInclusion.setSubText(inclusion.getValue());
				bookedInclusion.setText(inclusion.getCode());
				bookedInclusion.setIconUrl(inclusion.getIconUrl());
				bookedInclusion.setIUrl(inclusion.getIconUrl());
				bookedInclusion.setSegmentIdentifier(inclusion.getSegmentIdentifier());
				bookedInclusion.setLeafCategory(inclusion.getLeafCategory());
				bookedInclusion.setSegmentIdentifier(inclusion.getSegmentIdentifier());
				/*Adding to handle black segment, to be removed in the future.*/
				if (BLACK_SEGMENT_IDENTIFIER.equalsIgnoreCase(inclusion.getSegmentIdentifier()) && TRUE.equalsIgnoreCase(expData.get(ExperimentKeys.GOTRIBE_REVAMP.getKey()))) {
					bookedInclusion.setIconUrl(inclusion.getImageURL());
				}
				bookedInclusion.setCategory(inclusion.getCategory());
				bookedInclusion.setLeafCategory(inclusion.getLeafCategory());
				if (INCLUSION_TYPE_LOS.equalsIgnoreCase(inclusion.getInclusionType())) {
					losInclusions.add(bookedInclusion);
				} else {
					inclusions.add(bookedInclusion);
				}
			}

			if (ratePlanHES.getFreeChildCount() > 0 && StringUtils.isNotEmpty(ratePlanHES.getFreeChildText())) {
				BookedInclusion freeChildInclusion = utility.getFreeChildInclusion(ratePlanHES.getFreeChildText(), INCLUSIONS_DEFAULT_DOT_ICON_URL);
				inclusions.add(0, freeChildInclusion);
			}
			if (CollectionUtils.isNotEmpty(losInclusions)) {
				BookedInclusion losInclusion = utility.mergeLOSInclusions(losInclusions);
				if (null != losInclusion) {
					inclusions.add(losInclusion);
				}
			}
		}
        return inclusions;
    }


	private void setInclusionCodeAndText(BookedInclusion fczpnInlclusions, BNPLVariant bnplVariant) {
		 if (BNPLVariant.BNPL_AT_1.equals(bnplVariant)) {
			String bnplNewVariantText = polyglotService.getTranslatedData(BNPL_NEW_VARIANT_TEXT);
			if (StringUtils.isNotBlank(bnplNewVariantText)) {
				fczpnInlclusions.setCode(bnplNewVariantText);
				fczpnInlclusions.setText(bnplNewVariantText);
			}
		} else if (BNPLVariant.BNPL_AT_0.equals(bnplVariant)) {
			String bnplZeroVariantText = polyglotService.getTranslatedData(BNPL_ZERO_VARIANT_TEXT);
			if (StringUtils.isNotBlank(bnplZeroVariantText)) {
				fczpnInlclusions.setCode(bnplZeroVariantText);
				fczpnInlclusions.setText(bnplZeroVariantText);
			}
		} else {
			fczpnInlclusions.setCode(polyglotService.getTranslatedData(ConstantsTranslation.ZERO_PAYMENT_NOW_WITH_CC));
			fczpnInlclusions.setText(polyglotService.getTranslatedData(ConstantsTranslation.ZERO_PAYMENT_NOW_WITH_CC));
		}
	}

	////GIHTL-15565 Clean Up for NO_MEAL_INCLUSION_REMOVE flag
//	private void buildNoMealInclusions(Map<String, String> experimentDataMap, List<MealPlan> mealPlan, Map<String, String> mealPlanMap, RatePlan ratePlanHES, List<BookedInclusion> inclusions, boolean isMealPlanPresent, String supplierCode, int ap, boolean appendAtLast) {
//		// this piece of code was to be reused in for reordering of inclusion so created the function that can be called from anywhere it is needed
//		if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(mealPlan) && MapUtils.isNotEmpty(mealPlanMap) && !(MapUtils.isNotEmpty(experimentDataMap) && StringUtils.isNotBlank(experimentDataMap.get(Constants.NO_MEAL_INCLUSION_REMOVE)) && Constants.TRUE.equalsIgnoreCase(experimentDataMap.get(Constants.NO_MEAL_INCLUSION_REMOVE)))) {
//			if (Constants.MEAL_PLAN_CODE_ROOM_ONLY.equalsIgnoreCase(mealPlan.get(0).getCode()) || Constants.MEAL_PLAN_CODE_BED_ONLY.equalsIgnoreCase(mealPlan.get(0).getCode()) || Constants.MEAL_PLAN_CODE_ACC_ONLY.equalsIgnoreCase(mealPlan.get(0).getCode())) {
//				BookedInclusion noMeanInclusion = new BookedInclusion();
//				noMeanInclusion.setText(polyglotService.getTranslatedData(mealPlanMap.get(mealPlan.get(0).getCode())));
//				noMeanInclusion.setCode(noMeanInclusion.getText());
//				if (ap < apLimitForInclusionIcons) {
//					noMeanInclusion.setIconType(IconType.DEFAULT);
//					noMeanInclusion.setIconUrl(INCLUSIONS_DEFAULT_DOT_ICON_URL);
//				} else {
//					noMeanInclusion.setIconType(IconType.CROSS);
////					noMeanInclusion.setIconUrl(redCrossIcon);
//				}
//				noMeanInclusion.setInclusionCode(mealPlan.get(0).getCode());
//				noMeanInclusion.setCategory(MEAL);
//				inclusions.add(0, noMeanInclusion);
//			} else if (!isMealPlanPresent && StringUtils.isNotBlank(supplierCode) && !Constants.SUPPLIER_INGO.equalsIgnoreCase(supplierCode)) {
//				BookedInclusion noMeanInclusion = new BookedInclusion();
//
//				if (ratePlanHES.getExtraGuestDetail() != null && StringUtils.isNotEmpty(ratePlanHES.getExtraGuestDetail().getRatePlanExtraBedText())) {
//					noMeanInclusion.setText(ratePlanHES.getExtraGuestDetail().getRatePlanExtraBedText());
//					noMeanInclusion.setCode(ratePlanHES.getExtraGuestDetail().getRatePlanExtraBedText());
//				} else {
//					noMeanInclusion.setText(mealPlanMap.containsKey(mealPlan.get(0).getCode()) ? polyglotService.getTranslatedData(mealPlanMap.get(mealPlan.get(0).getCode())) : mealPlan.get(0).getValue());
//					noMeanInclusion.setCode(noMeanInclusion.getText());
//
//				}
//
//				noMeanInclusion.setInclusionCode(mealPlan.get(0).getCode());
//				noMeanInclusion.setType("MEAL_PLAN");
//				noMeanInclusion.setCategory(MEAL);
//				noMeanInclusion.setIconUrl("https://promos.makemytrip.com/Hotels_product/Hotel_SR/Android/drawable-hdpi/meal.png");
//				if (appendAtLast) {
//					inclusions.add(noMeanInclusion);
//				} else {
//					inclusions.add(0, noMeanInclusion);
//				}
//			}
//		}
//	}


	private List<String> getFilterCodes(com.mmt.hotels.model.response.pricing.RatePlan ratePlan , boolean isBlockPAH , int ap,
										CommonModifierResponse commonModifierResponse, boolean isLuxeHotel,String sellableType,String roomViewType,List<String> amenitiesFilterCode) {
		List<String> filterCodes = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(ratePlan.getMealPlans())
				&& ! (Constants.MEAL_PLAN_CODE_ROOM_ONLY.equalsIgnoreCase(ratePlan.getMealPlans().get(0).getCode())
				|| Constants.MEAL_PLAN_CODE_BED_ONLY.equalsIgnoreCase(ratePlan.getMealPlans().get(0).getCode())
				|| Constants.MEAL_PLAN_CODE_ACC_ONLY.equalsIgnoreCase(ratePlan.getMealPlans().get(0).getCode()))) {
			filterCodes.add(Constants.FREE_BREAKFAST);
		}
		if (mealplanFilterEnable &&  CollectionUtils.isNotEmpty(ratePlan.getMealPlans())
				&&  (Constants.MEAL_PLAN_CODE_BREAKFAST_LUNCH_OR_DINNER.equalsIgnoreCase(ratePlan.getMealPlans().get(0).getCode())
				|| Constants.MEAL_PLAN_CODE_BREAKFAST_DINNER.equalsIgnoreCase(ratePlan.getMealPlans().get(0).getCode())
				|| Constants.MEAL_PLAN_CODE_BREAKFAST_LUNCH.equalsIgnoreCase(ratePlan.getMealPlans().get(0).getCode()))) {
			filterCodes.add(Constants.TWO_MEAL_AVAIL);
		}
		if (mealplanFilterEnable && CollectionUtils.isNotEmpty(ratePlan.getMealPlans())
				&&  (Constants.MEAL_PLAN_CODE_ALL_MEALS_AI.equalsIgnoreCase(ratePlan.getMealPlans().get(0).getCode())
				|| Constants.MEAL_PLAN_CODE_ALL_MEALS.equalsIgnoreCase(ratePlan.getMealPlans().get(0).getCode()))) {
			filterCodes.add(Constants.ALL_MEAL_AVAIL);
		}
		if (mealplanFilterEnable && CollectionUtils.isNotEmpty(ratePlan.getMealPlans())
				&&  (Constants.MEAL_PLAN_CODE_ALL_MEALS_AI.equalsIgnoreCase(ratePlan.getMealPlans().get(0).getCode()))) {
			filterCodes.add(Constants.ALL_INCLUSIVE);
		}
		if (CollectionUtils.isNotEmpty(ratePlan.getCancelPenaltyList())
				&& null != ratePlan.getCancelPenaltyList().get(0).getCancellationType()
				&& ratePlan.getCancelPenaltyList().get(0).getCancellationType() == CancellationType.FREE_CANCELLATON) {
			filterCodes.add(Constants.FREE_CANCELLATION);
		}
		if (CollectionUtils.isNotEmpty(ratePlan.getCancelPenaltyList())
				&& null != ratePlan.getCancelPenaltyList().get(0).getCancellationType()
				&& ratePlan.getCancelPenaltyList().get(0).getCancellationType() == CancellationType.FREE_CANCELLATON
		&& ratePlan.getMpFareHoldStatus()!=null && ratePlan.getMpFareHoldStatus().isHoldEligible() && ratePlan.getMpFareHoldStatus().getExpiry()!=null &&
				ratePlan.getMpFareHoldStatus().getBookingAmount()==0f) {
			filterCodes.add(BOOK_NOW_AT_0);
		}

		if (CollectionUtils.isNotEmpty(ratePlan.getCancelPenaltyList())
				&& null != ratePlan.getCancelPenaltyList().get(0).getCancellationType()
				&& ratePlan.getCancelPenaltyList().get(0).getCancellationType() == CancellationType.FREE_CANCELLATON
				&& ratePlan.getMpFareHoldStatus()!=null && ratePlan.getMpFareHoldStatus().isHoldEligible() && ratePlan.getMpFareHoldStatus().getExpiry()!=null &&
				ratePlan.getMpFareHoldStatus().getBookingAmount()==1f) {
			filterCodes.add(BOOK_NOW_AT_1);
		}

		if(!(Objects.nonNull(commonModifierResponse) && Objects.nonNull(commonModifierResponse.getExtendedUser()) &&  Utility.isMyPartnerRequest(commonModifierResponse.getExtendedUser().getProfileType(), commonModifierResponse.getExtendedUser().getAffiliateId()))) {
			if (null != ratePlan.getPaymentDetails()
					&& ratePlan.getPaymentDetails().getPaymentMode() != PaymentMode.PAS) {
				if(isBlockPAH && ap < 5){
					filterCodes.add("FCZPN");
				}else {
					filterCodes.add("PAH");
				}
			}
			if(ratePlan.isBnplApplicable()){
				filterCodes.add("FCZPN");
			}
			if(CollectionUtils.isNotEmpty(ratePlan.getInclusions()) && ratePlan.getInclusions().stream().anyMatch(a-> ("Packages".equalsIgnoreCase(a.getCategory()) || "Packages1".equalsIgnoreCase(a.getCategory())
					|| "Packages2".equalsIgnoreCase(a.getCategory()) || "Packages3".equalsIgnoreCase(a.getCategory())
					|| "MMTBLACK".equalsIgnoreCase(a.getCategory())))){
				filterCodes.add("SPECIALDEALS");
			}

			/*
			* Logic for GI Getaway filter stayCation == true && mealPLan either AP or MAP
			* AP = All meals included
			* MAP = Two meals included
			*/
			if(ratePlan.isStaycationDeal()
					&& (mealplanFilterEnable && CollectionUtils.isNotEmpty(ratePlan.getMealPlans())
					&&  (Constants.MEAL_PLAN_CODE_BREAKFAST_LUNCH_OR_DINNER.equalsIgnoreCase(ratePlan.getMealPlans().get(0).getCode())
					|| Constants.MEAL_PLAN_CODE_BREAKFAST_DINNER.equalsIgnoreCase(ratePlan.getMealPlans().get(0).getCode())
					|| Constants.MEAL_PLAN_CODE_BREAKFAST_LUNCH.equalsIgnoreCase(ratePlan.getMealPlans().get(0).getCode()))
					|| Constants.MEAL_PLAN_CODE_ALL_MEALS.equalsIgnoreCase(ratePlan.getMealPlans().get(0).getCode()))) {
				filterCodes.add("STAYCATION");
			}
		}

		if (ratePlan.getPackageRoomRatePlan()) {
			filterCodes.add(PACKAGE_RATE);
		}
		if (partnerExclusiveFilterEnable && mypatExclusiveRateSegmentIdList.contains(ratePlan.getSegmentId()))
			filterCodes.add("CTA_RATES_AVAIL");
		if (SELLABLE_ROOM_TYPE.equalsIgnoreCase(sellableType)) {
			filterCodes.add(ROOMS_SELLABLE_TYPE_FILTER_CODE);
		}
		if (roomViewType != null && !roomViewType.isEmpty()) {
			filterCodes.add(Utility.convertToUnderscoreCaps(roomViewType));
		}
		if (amenitiesFilterCode != null && amenitiesFilterCode.size() > 0 ) {
			filterCodes.addAll(amenitiesFilterCode);
		}
		return filterCodes;
	}


	private List<Tariff> getTariffs(List<RatePlan> list, Map<String, String> expData, String askedCurrency,
									String sellableType, int days, String funnelSource, boolean groupBookingPrice,
									boolean myPartner, String listingType) {
		if (CollectionUtils.isEmpty(list))
			return null;
		List<Tariff> tariffList = new ArrayList<>();
		for (com.mmt.hotels.model.response.pricing.RatePlan ratePlan : list) {
			Tariff tariff = new Tariff();
			tariff.setTariffCode(ratePlan.getRatePlanCode());
			tariff.setOccupancydetails(new RoomTariff());
			if(null != ratePlan.getAvailDetails()) {
				if(null != ratePlan.getAvailDetails().getOccupancyDetails()) {
					tariff.getOccupancydetails().setNumberOfAdults(ratePlan.getAvailDetails().getOccupancyDetails().getAdult());
					tariff.getOccupancydetails().setNumberOfChildren(ratePlan.getAvailDetails().getOccupancyDetails().getChild());
					if (ratePlan.getAvailDetails().getOccupancyDetails().getChild() > 0)
						tariff.getOccupancydetails().setChildAges(ratePlan.getAvailDetails().getOccupancyDetails().getChildAges());
					tariff.getOccupancydetails().setRoomCount(ratePlan.getAvailDetails().getOccupancyDetails().getNumOfRooms());
				}
				tariff.setAvailCount(ratePlan.getAvailDetails().getCount());
			}
			if (ratePlan.getDisplayFare() != null)
				tariff.setBnplApplicable(ratePlan.getDisplayFare().getIsBNPLApplicable());
			tariff.setBnplPersuasionMsg(ratePlan.getBnplPersuasionMsg());
			tariff.setRoomTariffs(getRoomTariffs(ratePlan.getRoomTariff()));
			tariff.setMtKey(ratePlan.getMtKey());
			tariff.setIsPreApproved(ratePlan.getPreApproved());
			//set campaign alert node
			if(StringUtils.isNotBlank(ratePlan.getCampaingText())) {
				Alert alert = new Alert();
				alert.setType(AlertType.FREE_CANC_CAMPAIGN);
				alert.setText(ratePlan.getCampaingText());
				tariff.setCampaignAlert(alert);
			}else if(ratePlan.isAllInclusiveRate() &&
					utility.isExperimentOn(expData, Constants.ALL_INCLUSIVE_TRANSFER_EXPERIMENT)
					&& CollectionUtils.isNotEmpty(ratePlan.getAdditionalFees())){
				try {
					double additionalFee = ratePlan.getAdditionalFees().stream()
							.map(e -> e.getAskedCurrencyAmount())
							.collect(Collectors.summingDouble(Double::doubleValue));
					Alert alert = new Alert();
					alert.setType(AlertType.MANDATORY_FEE);
					alert.setText(mandatoryChargesAlert.replace("{currency_code}", askedCurrency).replace("{amount}", String.valueOf((int) additionalFee)));
					tariff.setCampaignAlert(alert);
				}catch(Exception e){
					LOGGER.error("Error while building mandatory fee alert",e);
				}

			}
			tariff.setPriceMap(commonResponseTransformer.getPriceMap(ratePlan.getDisplayFare().getDisplayPriceBreakDown(),
					ratePlan.getDisplayFare().getDisplayPriceBreakDownList(), expData,
					null != ratePlan.getAvailDetails() ? ratePlan.getAvailDetails().getOccupancyDetails().getNumOfRooms() : null,
					askedCurrency,sellableType, days,
					ratePlan.getDisplayFare().getCorpMetaData() != null, ratePlan.getSegmentId(),
					utility.buildToolTip(funnelSource), utility.isGroupBookingFunnel(funnelSource), groupBookingPrice, myPartner, listingType));
			tariff.setDefaultPriceKey(ratePlan.getDisplayFare().getDisplayPriceBreakDown() != null ? (ratePlan.getDisplayFare().getDisplayPriceBreakDown().getCouponInfo() != null ?
					ratePlan.getDisplayFare().getDisplayPriceBreakDown().getCouponInfo().getCouponCode() : "DEFAULT"): null);
			if(ratePlan.getDisplayFare().getDisplayPriceBreakDown() != null && ratePlan.getDisplayFare().getDisplayPriceBreakDown().getCouponInfo()!=null &&
					CollectionUtils.isNotEmpty(ratePlan.getDisplayFare().getDisplayPriceBreakDown().getCouponInfo().getVoucherList())) {
				List<Voucher> voucherList = ratePlan.getDisplayFare().getDisplayPriceBreakDown().getCouponInfo().getVoucherList();
				tariff.setVoucherCode(voucherList.get(0).getVoucherCode());
				tariff.setVoucherType(voucherList.get(0).getVoucherType());
			}
			tariffList.add(tariff);
		}
		return tariffList;
	}

	private List<RoomTariff> getRoomTariffs(List<com.mmt.hotels.model.response.pricing.RoomTariff> roomTariff) {
		if (CollectionUtils.isEmpty(roomTariff))
			return null;
		List<RoomTariff> roomTariffs = new ArrayList<>();
		for (com.mmt.hotels.model.response.pricing.RoomTariff room : roomTariff) {
			RoomTariff roomTariffCG = new RoomTariff();
			roomTariffCG.setNumberOfAdults(room.getNumberOfAdults());
			roomTariffCG.setNumberOfChildren(room.getNumberOfChildren());
			roomTariffCG.setDisplayPrice(room.getPerNightPrice());
			if (room.getNumberOfChildren()>0)
				roomTariffCG.setChildAges(room.getChildAges());
			roomTariffCG.setChildBuckets(room.getChildAgesBuckets());
			roomTariffs.add(roomTariffCG);
		}
		return roomTariffs;
	}

	private List<OfferDetail> getOffers(List<RangePrice> offers) {
		if (CollectionUtils.isEmpty(offers))
			return null;
		List<OfferDetail> offerDetails = new ArrayList<>();
		for (RangePrice rangePrice : offers) {
			OfferDetail offerDetail = new OfferDetail();
			offerDetail.setLongText(rangePrice.getLongText());
			offerDetail.setOfferType(rangePrice.getOfferType());
			offerDetail.setPriority(rangePrice.getPriority());
			offerDetail.setShortText(rangePrice.getShortText());
			offerDetail.setTncLink(rangePrice.getTncLink());
			offerDetail.setIconUrl(rangePrice.getIconUrl());
			offerDetails.add(offerDetail);
		}
		return offerDetails;
	}

	private SelectRoomBanner buildBanner(SearchRoomsResponse searchRoomsResponse, HotelRates hotelRates) {
		if (hotelRates!=null && searchRoomsResponse!=null) {
			SelectRoomBanner banner = new SelectRoomBanner();
			if (CollectionUtils.isNotEmpty(searchRoomsResponse.getExactRooms()) && hotelRates.getRoomTypeDetails()!=null) {
				Map<String,RoomType> roomTypeMap = hotelRates.getRoomTypeDetails().getRoomType();
				Optional<RoomType> maxRoom = roomTypeMap.values()
						.stream()
						.max(Comparator.comparingInt(room -> (room.getSelectedAmenities()==null?0:room.getSelectedAmenities().size())));
				if (maxRoom.isPresent() && !maxRoom.get().getRoomTypeCode().equalsIgnoreCase(searchRoomsResponse.getExactRooms().get(0).getRoomCode())) {
					makeBanner(banner,maxRoom.get());
					return banner;
				}
			} else if (CollectionUtils.isNotEmpty(searchRoomsResponse.getOccupancyRooms())) {
				Map<String,RoomType> roomTypeMap = hotelRates.getOccupencyLessRoomTypeDetails().getRoomType();
				Optional<RoomType> maxRoom = roomTypeMap.values()
						.stream()
						.max(Comparator.comparingInt(room -> (room.getSelectedAmenities()==null?0:room.getSelectedAmenities().size())));
				if (maxRoom.isPresent() && !maxRoom.get().getRoomTypeCode().equalsIgnoreCase(searchRoomsResponse.getOccupancyRooms().get(0).getRoomCode())) {
					makeBanner(banner,maxRoom.get());
					return banner;
				}
			} else if (CollectionUtils.isNotEmpty(searchRoomsResponse.getRecommendedCombos())) {
				Map<String,RoomType> roomTypeMap = hotelRates.getRecommendedRoomTypeDetails().getRoomType();
				Optional<RoomType> maxRoom = roomTypeMap.values()
						.stream()
						.max(Comparator.comparingInt(room -> (room.getSelectedAmenities()==null?0:room.getSelectedAmenities().size())));
				if (maxRoom.isPresent() && searchRoomsResponse.getRecommendedCombos().get(0).getRooms().stream().noneMatch(e->e.getRoomCode().equalsIgnoreCase(maxRoom.get().getRoomTypeCode()))) {
					makeBanner(banner,maxRoom.get());
					return banner;
				}
			}
		}
		return null;
	}

	private void makeBanner(SelectRoomBanner banner, RoomType roomDetails) {
		String text = StringUtils.EMPTY;
		try {
			if (roomDetails.getSelectedAmenities().size()==1) {
				text = polyglotService.getTranslatedData(SELECT_ROOM_1_AMENITIES_BANNER)
						.replace(ROOM_NAME,roomDetails.getRoomTypeName())
						.replace(AMENITY_1,roomDetails.getSelectedAmenities().get(0).getName());
			} else if (roomDetails.getSelectedAmenities().size()==2) {
				text = polyglotService.getTranslatedData(SELECT_ROOM_2_AMENITIES_BANNER)
						.replace(ROOM_NAME,roomDetails.getRoomTypeName())
						.replace(AMENITY_1,roomDetails.getSelectedAmenities().get(0).getName())
						.replace(AMENITY_2,roomDetails.getSelectedAmenities().get(1).getName());
			} else {
				text = polyglotService.getTranslatedData(SELECT_ROOM_3_AMENITIES_BANNER)
						.replace(ROOM_NAME,roomDetails.getRoomTypeName())
						.replace(AMENITY_1,roomDetails.getSelectedAmenities().get(0).getName())
						.replace(AMENITY_2,roomDetails.getSelectedAmenities().get(1).getName())
						.replace(AMENITY_3,roomDetails.getSelectedAmenities().get(2).getName());
			}
		} catch (Exception e) {
			LOGGER.error("Search-rooms banner could not be made");
		}
		if (StringUtils.isNotBlank(text)) {
			banner.setTitle(text);
			banner.setRedirectType(Constants.SELECT_ROOM_BANNER_TYPE);
			banner.setRedirectLink(roomDetails.getRoomTypeCode());
			banner.setIconUrl(Constants.SELECT_ROOM_BANNER_ICON_URL);
			banner.setBgColor(Constants.SELECT_ROOM_BANNER_BG_COLOR);
		} else {
			banner = null;
		}
	}

	public String getComboText(String mealPlanCode, long differenceInPriceFromBaseCombo) {
		if (StringUtils.isNotBlank(mealPlanCode)) {
			switch (mealPlanCode) {
				case Constants.MEAL_PLAN_CODE_ACC_ONLY:
					return "<b>"+polyglotService.getTranslatedData("ACCOMODATION_ONLY")+"</b>";

				case Constants.MEAL_PLAN_CODE_BED_ONLY:
					return "<b>"+polyglotService.getTranslatedData("BED_ONLY")+"</b>";

				case Constants.MEAL_PLAN_CODE_ROOM_ONLY:
					return "<b>"+polyglotService.getTranslatedData("ROOM_ONLY_TEXT")+"</b>";

				case Constants.MEAL_PLAN_CODE_BREAKFAST:
					return polyglotService.getTranslatedData(ADD_BREAKFAST).replace(ConstantsTranslation.AMOUNT.toUpperCase(), String.valueOf(differenceInPriceFromBaseCombo));

				case Constants.MEAL_PLAN_CODE_BREAKFAST_LUNCH:
					return polyglotService.getTranslatedData(ADD_BREAKFAST_AND_LUNCH).replace(ConstantsTranslation.AMOUNT.toUpperCase(), String.valueOf(differenceInPriceFromBaseCombo));

				case Constants.MEAL_PLAN_CODE_BREAKFAST_DINNER:
					return polyglotService.getTranslatedData(ADD_BREAKFAST_AND_DINNER).replace(ConstantsTranslation.AMOUNT.toUpperCase(), String.valueOf(differenceInPriceFromBaseCombo));

				case Constants.MEAL_PLAN_CODE_BREAKFAST_LUNCH_OR_DINNER:
					return polyglotService.getTranslatedData(ADD_BREAKFAST_LUNCH_OR_DINNER).replace(ConstantsTranslation.AMOUNT.toUpperCase(), String.valueOf(differenceInPriceFromBaseCombo));

				case Constants.MEAL_PLAN_CODE_ALL_MEALS:
				case Constants.MEAL_PLAN_CODE_ALL_MEALS_AI:
					return polyglotService.getTranslatedData(ADD_BREAKFAST_LUNCH_AND_DINNER).replace(ConstantsTranslation.AMOUNT.toUpperCase(), String.valueOf(differenceInPriceFromBaseCombo));

				default:
					return null;

			}
		}
		return null;
	}

	public CalendarAvailabilityResponse convertCalendarAvailabilityResponse(com.mmt.hotels.model.response.CalendarAvailabilityResponse calendarAvailabilityResponseHES) {
		CalendarAvailabilityResponse calendarAvailabilityResponseCG = new CalendarAvailabilityResponse();
		Map<String, CalendarBO> dates = new LinkedHashMap<>();
		if(MapUtils.isNotEmpty(calendarAvailabilityResponseHES.getDates())) {
			calendarAvailabilityResponseHES.getDates().forEach((date, calendarBOHES) -> {
				CalendarBO calendarBO = new CalendarBO();
				calendarBO.setStatus(calendarBOHES.getStatus().name());
				calendarBO.setPrice(calendarBOHES.getPrice());
				dates.put(date, calendarBO);
			});
		}
		calendarAvailabilityResponseCG.setDates(dates);
		return calendarAvailabilityResponseCG;
	}

	private void buildParentLinkedRates(SelectRoomRatePlan ratePlan, List<com.mmt.hotels.model.response.pricing.LinkedRate> linkedRates) {

		if(CollectionUtils.isNotEmpty(linkedRates)){
			List<LinkedRate> parentLinkedRates = new ArrayList<>();
			for(com.mmt.hotels.model.response.pricing.LinkedRate hesLinkedRate : linkedRates){
				LinkedRate cgLinkedRate = new LinkedRate();
				cgLinkedRate.setType(hesLinkedRate.getType());
				cgLinkedRate.setPricingKey(hesLinkedRate.getPricingKey());
				parentLinkedRates.add(cgLinkedRate);
			}
			ratePlan.setParentLinkedRates(parentLinkedRates);
		}
	}

	private void buildChildLinkedRates(SelectRoomRatePlan ratePlan, List<com.mmt.hotels.model.response.pricing.LinkedRate> linkedRates) {

		if(CollectionUtils.isNotEmpty(linkedRates)){
			List<LinkedRate> childLinkedRates = new ArrayList<>();
			for(com.mmt.hotels.model.response.pricing.LinkedRate hesLinkedRate : linkedRates){
				LinkedRate cgLinkedRate = new LinkedRate();
				cgLinkedRate.setType(hesLinkedRate.getType());
				cgLinkedRate.setPricingKey(hesLinkedRate.getPricingKey());
				childLinkedRates.add(cgLinkedRate);
			}
			ratePlan.setChildLinkedRates(childLinkedRates);
		}
	}


	private void linkRatePlans(List<SelectRoomRatePlan> ratePlans) {
		try {

			List<SelectRoomRatePlan> ratePlansCopy = new ArrayList<>(ratePlans);

			for (SelectRoomRatePlan ratePlan : ratePlansCopy) {
				List<LinkedRatePlan> linkedRatePlans = new ArrayList<>();
				UpsellRatePlan upsellRatePlan = ratePlan.getUpsellRatePlan();
				if (CollectionUtils.isNotEmpty(ratePlan.getChildLinkedRates())) {
					for (LinkedRate childLinkedRate : ratePlan.getChildLinkedRates()) {
						if (LINKEDRATE_FCNR.equalsIgnoreCase(childLinkedRate.getType())) {
							Iterator<SelectRoomRatePlan> iterator = ratePlans.iterator();
							while (iterator.hasNext()) {
								SelectRoomRatePlan selectRoomRatePlan = iterator.next();
								if (selectRoomRatePlan.getRpc().equalsIgnoreCase(childLinkedRate.getPricingKey())) {
									LinkedRatePlan linkedRatePlan = new LinkedRatePlan();
									linkedRatePlan.setRatePlan(selectRoomRatePlan);
									RatePlanData ratePlanData = createRatePlanData();
									ratePlanData.setRatePlanName(selectRoomRatePlan.getLinkedRatePlanName());
									linkedRatePlan.setData(ratePlanData);
									linkedRatePlans.add(linkedRatePlan);
									iterator.remove();
								}
							}
						}
					}
				}
				ratePlan.setUpsellRatePlan(upsellRatePlan);
				ratePlan.setLinkedRatePlans(linkedRatePlans);
			}
		} catch (Exception ex){
			LOGGER.error("Error in linking rate plans", ex);
		}
	}
	private RatePlanData createRatePlanData() {
		RatePlanData ratePlanData = new RatePlanData();
		ratePlanData.setTitle(polyglotService.getTranslatedData(GI_LINKED_RATE_PLAN_TITLE));
		ratePlanData.setDescription(polyglotService.getTranslatedData(GI_LINKED_RATE_PLAN_DESCRIPTION));
		ratePlanData.setCtaText(polyglotService.getTranslatedData(GI_LINKED_RATE_PLAN_CTATEXT));
		ratePlanData.setBottomSheetFooterText(polyglotService.getTranslatedData(GI_LINKED_RATE_PLAN_BOTTOM_SHEET_FOOTER_TEXT));
		return ratePlanData;
	}
}

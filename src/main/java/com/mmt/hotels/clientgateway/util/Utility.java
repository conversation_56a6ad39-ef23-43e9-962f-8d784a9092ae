package com.mmt.hotels.clientgateway.util;

import com.fasterxml.jackson.databind.JsonNode;
import com.gi.hotels.model.response.staticdata.Amenities;
import com.gi.hotels.model.response.staticdata.Categorized;
import com.gi.hotels.model.response.staticdata.CategorizedV2;
import com.gi.hotels.model.response.staticdata.Transformed;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.google.i18n.phonenumbers.PhoneNumberUtil;
import com.google.i18n.phonenumbers.Phonenumber;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.configuration.SecretMangerConfig;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.constants.ControllerConstants;
import com.mmt.hotels.clientgateway.enums.Brand;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.DeviceConstant;
import com.mmt.hotels.clientgateway.enums.ExperimentKeys;
import com.mmt.hotels.clientgateway.enums.UpgradeType;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.pms.CommonConfig;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.response.*;
import com.mmt.hotels.clientgateway.response.dayuse.rooms.DayUsePersuasion;
import com.mmt.hotels.clientgateway.response.rooms.*;
import com.mmt.hotels.clientgateway.response.staticdetail.HotelResult;
import com.mmt.hotels.clientgateway.response.staticdetail.StaticDetailResponse;
import com.mmt.hotels.clientgateway.response.streaks.userinfo.PageDetails;
import com.mmt.hotels.clientgateway.response.streaks.userinfo.StreaksUserInfoResponse;
import com.mmt.hotels.clientgateway.restexecutors.StreaksExecutor;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.thirdparty.response.AltVerifiedDetails;
import com.mmt.hotels.clientgateway.thirdparty.response.ExtendedUser;
import com.mmt.hotels.model.enums.BNPLVariant;
import com.mmt.hotels.model.persuasion.peitho.request.hotelDetails.BlackBenefits;
import com.mmt.hotels.model.request.GuestCount;
import com.mmt.hotels.model.request.PriceByHotelsRequestBody;
import com.mmt.hotels.model.request.RequestIdentifier;
import com.mmt.hotels.model.request.RoomStayCandidate;
import com.mmt.hotels.model.request.SearchWrapperInputRequest;
import com.mmt.hotels.model.request.dayuse.Slot;
import com.mmt.hotels.model.request.flyfish.OTA;
import com.mmt.hotels.model.request.payment.DeviceDetails;
import com.mmt.hotels.model.request.payment.UserDetail;
import com.mmt.hotels.model.response.mmtprime.BlackInfo;
import com.mmt.hotels.model.response.pricing.*;
import com.mmt.hotels.model.response.pricing.CancelPenalty;
import com.mmt.hotels.model.response.pricing.CancellationTimeline;
import com.mmt.hotels.model.response.pricing.FullPayment;
import com.mmt.hotels.model.response.pricing.RatePlan;
import com.mmt.hotels.model.response.staticdata.SleepingInfoArrangement;
import com.mmt.hotels.model.response.pricing.Inclusion;
import com.mmt.hotels.model.response.txn.PersistedMultiRoomData;
import com.mmt.hotels.pojo.request.detail.mob.HotelDetailsMobRequestBody;
import com.mmt.hotels.pojo.response.detail.HotelDetailWrapperResponse;
import com.mmt.hotels.util.Tuple;
import com.mmt.model.Facility;
import com.mmt.model.FacilityGroup;
import com.mmt.model.RoomInfo;
import com.mmt.model.SleepingArrangement;
import com.mmt.model.SubAttributeFacility;
import com.mmt.propertymanager.config.PropertyManager;
import com.mmt.scrambler.ScramblerClient;
import com.mmt.scrambler.exception.ScramblerClientException;
import com.mmt.scrambler.utils.HashType;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Nullable;
import javax.annotation.PostConstruct;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Type;
import java.net.URLEncoder;
import java.text.MessageFormat;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static com.mmt.hotels.clientgateway.constants.Constants.BEDROOM;
import static com.mmt.hotels.clientgateway.constants.Constants.ENTIRE_APARTMENT;
import static com.mmt.hotels.clientgateway.constants.Constants.ENTIRE_APARTMENTS;
import static com.mmt.hotels.clientgateway.constants.Constants.ENTIRE_SERVICE_APARTMENT;
import static com.mmt.hotels.clientgateway.constants.Constants.ENTIRE_SERVICE_APARTMENTS;
import static com.mmt.hotels.clientgateway.constants.Constants.ROOM_IN_APARTMENT;
import static com.mmt.hotels.clientgateway.constants.Constants.ROOM_IN_SERVICE_APARTMENT;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.*;
import static com.mmt.hotels.clientgateway.enums.ExperimentKeys.privateRoomFilter;
import static java.lang.Math.max;

@Component
public class Utility {

    private static Gson gson = new Gson();

    @Value("${searchrooms.rateplan.name.config}")
    private String ratePlanNameConfig;

    @Value("${searchrooms.rateplan.redesign}")
    private String ratePlanNameConfigRedesign;

    @Value("${mmt.ratings.count.exp.threshold}")
    private int mmtRatingsCountThreshold;

    @Value("${los.icon.url.room}")
    private String losIconUrl;

    @Value("${los.title.text.colour}")
    private String losTitleTextColor;

    @Value("${gi.private.room.adult.count.threshold}")
    private int giPrivateRoomAdultCountThreshold;

    @Value("${gi.private.room.room.count.threshold}")
    private int giPrivateRoomRoomCountThreshold;

    @Autowired
    ObjectMapperUtil objectMapperUtil;private Map<String, Map<String, Map<String, String>>> ratePlanNameMap;

    private  Map<String, Map<String, Map<String, String>>> ratePlanNameMapRedesign;
    private  Map<String, Filter> preAppliedFilterListMap;

   @Autowired
   private PropertyManager propertyManager;

    @Autowired
    private PolyglotService polyglotService;

    @Autowired
    private DateUtil dateUtil;

    @Value("${pah.with.cc.text}")
    private String pahwithccText;

    @Value("${pah.without.cc.text}")
    private String pahwithoutccText;

    @Autowired
    StreaksExecutor streaksExecutor;

    @Autowired
    SecretMangerConfig cgConfig;

    @Value("#{'${homestay.bedType.priority.order}'.split(',')}")
    private List<String> bedTypePriorityOrder;

    private int apLimitForInclusionIcons = 2;

    @Value("${bnpl.gcc.text}")
    private String bnplGccText;
	private static final Logger LOGGER = LoggerFactory.getLogger(Utility.class);

    private static final String PROFILE_TYPE = "CTA";
    private static final String SUB_PROFILE_TYPE = "MYPARTNER";

    private static final Pattern HH_AA_TIME_REGEX = Pattern.compile("((1[0-2]|0?[0-9])\\s*([AaPp][.]*[Mm][.]*))", Pattern.CASE_INSENSITIVE);

    @Value("#{'${business.Identification.Affiliates.list}'.split(',')}")
    private Set<String> businessIdentificationAffiliates;

    @Value("#{'${business.Identification.Segments.list}'.split(',')}")
    private Set<String> businessIdentificationSegments;

    @Value("${preapplied.filter.list}")
    String preAppliedFilterList;

    public static String getMMTorGIHotelId(String hotelId, String giHotelId) {
        return StringUtils.isNotEmpty(hotelId) ? hotelId : giHotelId;
    }

    @PostConstruct
    public void init() {
        CommonConfig commonConfig = propertyManager.getProperty("commonConfig", CommonConfig.class);
        apLimitForInclusionIcons = commonConfig.apLimitForInclusionIcons();
        ratePlanNameMap = gson.fromJson(ratePlanNameConfig, new TypeToken<Map<String,Map<String,Map<String,String>>> >() {
        }.getType());
        ratePlanNameMapRedesign = gson.fromJson(ratePlanNameConfigRedesign, new TypeToken<Map<String,Map<String,Map<String,String>>> >() {
        }.getType());
        preAppliedFilterListMap = gson.fromJson(preAppliedFilterList, new TypeToken<Map<String,Filter>>() {
        }.getType());
    }

    public void buildSlot(PriceByHotelsRequestBody priceByHotelsRequestBody, SearchCriteria searchCriteria) {
        if (searchCriteria.getSlot() != null) {
            Slot slot = new Slot();
            slot.setDuration(searchCriteria.getSlot().getDuration());
            slot.setTimeSlot(searchCriteria.getSlot().getTimeSlot());
            priceByHotelsRequestBody.setSlot(slot);
        }
    }

    public void buildSlot(HotelDetailsMobRequestBody hotelDetailsMobRequestBody, StaticDetailCriteria searchCriteria) {
        if (searchCriteria.getSlot() != null) {
            Slot slot = new Slot();
            slot.setDuration(searchCriteria.getSlot().getDuration());
            slot.setTimeSlot(searchCriteria.getSlot().getTimeSlot());
            hotelDetailsMobRequestBody.setSlot(slot);
        }
    }

	public  static String removeSpecialChar(String text){
	    if(StringUtils.isNotBlank(text))
            return text.trim().replaceAll("([^A-Za-z0-9])", "_").replaceAll("\\s", "_");
	    return null;
    }

    public static String getcompleteURL(String intialURL, Map<String, String[]> paramsMap, String coRelationKey) {
	    if (StringUtils.isBlank(intialURL))
	        return intialURL;
	    try {
            StringBuilder sb = new StringBuilder(intialURL);
            if (!intialURL.contains(Constants.QUESTION))
                sb.append(Constants.QUESTION);
            else
                sb.append(Constants.AMP);
            sb.append(Constants.CORRELATIONKEY);
            sb.append(Constants.EQUI);
            sb.append(coRelationKey);
            boolean isRegion = false;
            if (MapUtils.isNotEmpty(paramsMap)) {
                for (Map.Entry<String, String[]> entry : paramsMap.entrySet()) {
                    sb.append(Constants.AMP);
                    sb.append(entry.getKey());
                    sb.append(Constants.EQUI);
                    sb.append(URLEncoder.encode(entry.getValue()[0], "UTF-8"));
                    if (Constants.REGION.equalsIgnoreCase(entry.getKey()))
                        isRegion = true;
                }
            }
            if (!isRegion) {
                sb.append(Constants.AMP);
                sb.append(Constants.REGION);
                sb.append(Constants.EQUI);
                sb.append(Constants.DEFAULT_SITE_DOMAIN);

            }
            return sb.toString();
        } catch (UnsupportedEncodingException e) {
	        LOGGER.error("Url modification failed. - {}" + e.getMessage());
	        return intialURL;
        }
    }


    public static String getCompleteUrl(String url, Map<String, String[]> parameterMap) {
        if (StringUtils.isEmpty(url) || MapUtils.isEmpty(parameterMap)) return url;
        try {
            StringBuilder sb = new StringBuilder(url);
            sb.append(url.contains(Constants.QUESTION) ? Constants.AMP : Constants.QUESTION);

            for (Map.Entry<String, String[]> entry : parameterMap.entrySet()) {
                if (entry.getKey().equalsIgnoreCase("profile") || entry.getKey().equals("Region")) {
                    continue;
                }
                sb.append(entry.getKey());
                sb.append(Constants.EQUI);
                sb.append(URLEncoder.encode(entry.getValue()[0], "UTF-8"));
                sb.append(Constants.AMP);
            }

            if (sb.toString().endsWith(Constants.AMP)) {
                sb.setLength(sb.length() - Constants.AMP.length());
            }
            return sb.toString();
        } catch (UnsupportedEncodingException e) {
            LOGGER.error("Url modification failed {}", e.getMessage());
            return url;
        }
    }

    public static Double round(double value, int precision) {
        int scale = (int) Math.pow(10, precision);
        return (double) Math.round(value * scale) / scale;
    }

    public static String getHotelierCurrency(PersistedMultiRoomData persistedMultiRoomData) {
        String supplierCurrency = "INR";
        if (null != persistedMultiRoomData.getHotelList().get(0).getTariffInfoList().get(0).getSupplierDetails()
                && StringUtils.isNotBlank(persistedMultiRoomData.getHotelList().get(0).getTariffInfoList().get(0)
                .getSupplierDetails().getHotelierCurrencyCode()))
            supplierCurrency = persistedMultiRoomData.getHotelList().get(0).getTariffInfoList().get(0).getSupplierDetails()
                    .getHotelierCurrencyCode();
        return supplierCurrency;
    }

    public static String getAskedCurrency(PersistedMultiRoomData persistedMultiRoomData) {
        String askedCurrency = "INR";
        if (null != persistedMultiRoomData.getAvailReqBody() && StringUtils.isNotBlank(persistedMultiRoomData.getAvailReqBody().getCurrency()))
            askedCurrency = persistedMultiRoomData.getAvailReqBody().getCurrency();
        return askedCurrency;
    }

    public static double getHotelierConversionFactor(PersistedMultiRoomData persistedMultiRoomData) {
        return null != persistedMultiRoomData.getHotelList().get(0).getTariffInfoList().get(0).getDisplayFare() ?
                persistedMultiRoomData.getHotelList().get(0).getTariffInfoList().get(0).getDisplayFare().getConversionFactor()
                : 1.0;
    }

    /**
     * Total adult count
     * @param roomStayCandidates
     * @return
     */
    public Integer getTotalAdultsFromRequest(List<com.mmt.hotels.clientgateway.request.RoomStayCandidate> roomStayCandidates) {
        int adultCount = 0;
        if (CollectionUtils.isNotEmpty(roomStayCandidates)) {
            for (com.mmt.hotels.clientgateway.request.RoomStayCandidate roomStayCandidate : roomStayCandidates) {
                adultCount += roomStayCandidate.getAdultCount();
            }
        }
        return adultCount;
    }

    /**
     * * Total child count
     * @param roomStayCandidates
     * @return
     */
    public Integer getTotalChildrenFromRequest(List<com.mmt.hotels.clientgateway.request.RoomStayCandidate> roomStayCandidates) {
        int childCount = 0;
        if (CollectionUtils.isNotEmpty(roomStayCandidates)) {
            for (com.mmt.hotels.clientgateway.request.RoomStayCandidate roomStayCandidate : roomStayCandidates) {
                if (CollectionUtils.isNotEmpty(roomStayCandidate.getChildAges())) {
                    childCount += roomStayCandidate.getChildAges().size();
                }
            }
        }
        return childCount;
    }


    public static String buildRoomStayQualifierFromRoomStayCandidates(@Nullable List<RoomStayCandidate> roomStayCandidates, boolean tildeRequiredInRSQ) {
        StringBuilder builder = new StringBuilder();
        if (CollectionUtils.isEmpty(roomStayCandidates)) {
            return builder.toString();
        }

        for (RoomStayCandidate roomStayCandidate : roomStayCandidates) {
            if (CollectionUtils.isEmpty(roomStayCandidate.getGuestCounts()))
                continue;
            for (GuestCount guestCount: roomStayCandidate.getGuestCounts()) {
                if (guestCount == null)
                    continue;
                int adultCount = Integer.parseInt(guestCount.getCount());
                int childCount = 0;
                if (CollectionUtils.isNotEmpty(guestCount.getAges()))
                    childCount = guestCount.getAges().size();
                builder.append(adultCount);
                builder.append(Constants.RSQ_SPLITTER);
                builder.append(childCount);
                builder.append(Constants.RSQ_SPLITTER);
                if (CollectionUtils.isNotEmpty(guestCount.getAges())) {
                    for (int age : guestCount.getAges()) {
                        builder.append("1");
                        builder.append(Constants.RSQ_SPLITTER);
                    }
                }
                if (tildeRequiredInRSQ)
                    builder.append(Constants.RSQ_ROOM_SPLITTER);
            }
        }

        String roomStayQualifier = builder.toString();
        if (roomStayQualifier.length() > 0) {
            return roomStayQualifier.substring(0, roomStayQualifier.length()-1);
        }
        return roomStayQualifier;
    }

    public BookedCancellationPolicy transformCancellationPolicy(List<CancelPenalty> cancelPenalty, boolean bnplApplicable, BNPLVariant bnplVariant, String confirmationPolicyType, String cancellationPolicyNrNonInstantText, Integer ap){
        BookedCancellationPolicy bookedCancellationPolicy = new BookedCancellationPolicy();
        boolean isFC = false;
        boolean isPC = false;
        boolean isCancelRulesRequired = true;
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(cancelPenalty) && null != cancelPenalty.get(0).getCancellationType()) {
            if(CancelPenalty.CancellationType.FREE_CANCELLATON.equals(cancelPenalty.get(0).getCancellationType())){
                isFC = true;
            }else if(CancelPenalty.CancellationType.PARTIAL_REFUNDABLE.equals(cancelPenalty.get(0).getCancellationType())){
                isPC = true;
            }
        }
        bookedCancellationPolicy.setIconUrl("https://promos.makemytrip.com/Hotels_product/Hotel_SR/Android/drawable-hdpi/policy.png");
        if (isFC) {
            bookedCancellationPolicy.setIconType(IconType.DOUBLETICK);
            bookedCancellationPolicy.setText(cancelPenalty.get(0).getFreeCancellationText());
            bookedCancellationPolicy.setType(BookedCancellationPolicyType.FC);
            if (bnplApplicable) {
                if (!Utility.isGCC()) {
                    if (BNPLVariant.BNPL_AT_1 == bnplVariant) {
                        bookedCancellationPolicy.setSubText(polyglotService.getTranslatedData(ConstantsTranslation.CANCEL_POLICY_BNPL_SUBTEXT_NEW_VARIANT));
                    } else if (BNPLVariant.BNPL_AT_0 == bnplVariant) {
                        bookedCancellationPolicy.setSubText(polyglotService.getTranslatedData(ConstantsTranslation.CANCEL_POLICY_BNPL_SUBTEXT_ZERO_VARIANT));
                    }
                } else {
                    bookedCancellationPolicy.setSubText(polyglotService.getTranslatedData(ConstantsTranslation.CANCEL_POLICY_BNPL_SUBTEXT));
                }
            }
        } else if(isPC){
            bookedCancellationPolicy.setIconType(IconType.DOUBLETICK);
            String partialRefundText = buildPartialRefundDateText(cancellationTimeline);
            bookedCancellationPolicy.setText(partialRefundText);
            bookedCancellationPolicy.setType(BookedCancellationPolicyType.FC);
        } else {
            String controller = MDC.get(MDCHelper.MDCKeys.CONTROLLER.getStringValue());
            if ((ControllerConstants.DETAIL_SEARCH_ROOMS.equalsIgnoreCase(controller) || ControllerConstants.REVIEW_ROOM_INFOS.equalsIgnoreCase(controller)
                    || ControllerConstants.REVIEW_AVAIL_ROOMS.equalsIgnoreCase(controller))
                    && ap!=null && ap < apLimitForInclusionIcons) {
                bookedCancellationPolicy.setIconType(IconType.DEFAULT);
            } else {
                bookedCancellationPolicy.setIconType(IconType.BIGCROSS);
            }
            bookedCancellationPolicy.setText(polyglotService.getTranslatedData(ConstantsTranslation.NON_REFUNDABLE_TEXT));
            if (Constants.CTRIP_NONINS.equalsIgnoreCase(confirmationPolicyType)) {
                bookedCancellationPolicy.setSubText(polyglotService.getTranslatedData(ConstantsTranslation.CANCELLATION_POLICY_NR_NON_INSTANT_TEXT));
            } else {
                bookedCancellationPolicy.setSubText(polyglotService.getTranslatedData(ConstantsTranslation.NON_REFUNDABLE_SUBTEXT));
            }
            bookedCancellationPolicy.setType(BookedCancellationPolicyType.NR);
        }

        if (CollectionUtils.isNotEmpty(cancelPenalty) && cancelPenalty.get(0).getCancelRules() != null)
            bookedCancellationPolicy.setCancelRules(cancelPenalty.get(0).getCancelRules());

        return bookedCancellationPolicy;
    }

    public List<BookedInclusion> reorderInclusions(List<BookedInclusion> inclusions){
//		this function orders the inculsion based on ratePlanCategoryList {zpn -> meal -> others, usp}
        List<BookedInclusion> newInclusions = new ArrayList<>();
        Map<String, List<BookedInclusion>> mp = new HashMap<>();
        for (String s: inclusionOrderList){
            mp.put(s, new ArrayList<>());
        }
        inclusions.forEach(e -> {
            if(StringUtils.isNotEmpty(e.getCategory()) && mp.containsKey(e.getCategory())){
                mp.get(e.getCategory()).add(e);
            }else {
                mp.get(OTHERS).add(e);
            }
        });
        for (String s: inclusionOrderList){
            newInclusions.addAll(mp.get(s));
        }
        return newInclusions;
    }

    public List<BookedInclusion> reorderBlackInclusions(List<BookedInclusion> inclusions){
//		this function orders the inculsion based on ratePlanCategoryList {zpn -> meal -> others, usp}
        List<BookedInclusion> newInclusions = new ArrayList<>();
        Map<String, List<BookedInclusion>> mp = new HashMap<>();
        for (String s: blackInclusionsOrderList){
            mp.put(s, new ArrayList<>());
        }
        if (CollectionUtils.isNotEmpty(inclusions)) {
            inclusions.forEach(e -> {
                if(StringUtils.isNotEmpty(e.getLeafCategory()) && mp.containsKey(e.getLeafCategory())){
                    mp.get(e.getLeafCategory()).add(e);
                }else {
                    mp.get(OTHERS).add(e);
                }
            });
        }
        for (String s: blackInclusionsOrderList){
            newInclusions.addAll(mp.get(s));
        }
        return newInclusions;
    }

    public List<Inclusion> reorderBlackBenefits(List<Inclusion> inclusions){
        List<Inclusion> newInclusions = new ArrayList<>();
        Map<String, List<Inclusion>> mp = new HashMap<>();
        for (String s: blackInclusionsOrderList){
            mp.put(s, new ArrayList<>());
        }
        if (CollectionUtils.isNotEmpty(inclusions)) {
            inclusions.forEach(e -> {
                if(StringUtils.isNotEmpty(e.getLeafCategory()) && mp.containsKey(e.getLeafCategory())){
                    mp.get(e.getLeafCategory()).add(e);
                }else {
                    mp.get(OTHERS).add(e);
                }
            });
        }
        for (String s: blackInclusionsOrderList){
            newInclusions.addAll(mp.get(s));
        }
        return newInclusions;
    }

    public void setInclusionCodeAndText(BookedInclusion fczpnInlclusions, BNPLVariant bnplVariant, String region) {
        if (AE.equalsIgnoreCase(region)) {
            fczpnInlclusions.setCode(bnplGccText);
            fczpnInlclusions.setText(polyglotService.getTranslatedData(BNPL_GCC_TEXT));
        } else if (BNPLVariant.BNPL_AT_1.equals(bnplVariant)) {
            String bnplNewVariantText = polyglotService.getTranslatedData(BNPL_NEW_VARIANT_TEXT_GI);
            if (StringUtils.isNotBlank(bnplNewVariantText)) {
                fczpnInlclusions.setCode(bnplNewVariantText);
                fczpnInlclusions.setText(bnplNewVariantText);
            }
        } else if (BNPLVariant.BNPL_AT_0.equals(bnplVariant)) {
            String bnplZeroVariantText = polyglotService.getTranslatedData(BNPL_ZERO_VARIANT_TEXT_GI_AVAILABLE);
            if (StringUtils.isNotBlank(bnplZeroVariantText)) {
                fczpnInlclusions.setCode(bnplZeroVariantText);
                fczpnInlclusions.setText(bnplZeroVariantText);
            }
        } else {
            fczpnInlclusions.setCode(polyglotService.getTranslatedData(ConstantsTranslation.ZERO_PAYMENT_NOW_WITH_CC));
            fczpnInlclusions.setText(polyglotService.getTranslatedData(ConstantsTranslation.ZERO_PAYMENT_NOW_WITH_CC));
        }
    }

    public List<BookedInclusion> transformInclusions(List<MealPlan> mealPlan, List<Inclusion> inclusionList,
                                                     Map<String,String> mealPlanMap, String supplierCode, Integer ap ,
                                                     ExtraGuestDetail extraGuestDetail, Map<String, String > experimentDataMap,List<CancelPenalty> cancelPenaltyList,
                                                     Boolean isBnplApplicable,BNPLVariant bnplVariant,PaymentDetails paymentDetails, String freeChildText, int goCashAmount){


        List<BookedInclusion> inclusions = new ArrayList<>();
        List<BookedInclusion> losInclusions = new ArrayList<>();
        boolean isMealPlanPresent = false;
        int count = 0;
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(inclusionList)) {
            for(Inclusion inclusion : inclusionList) {
                if (StringUtils.isEmpty(inclusion.getValue()))
                    continue;
                count++;
                BookedInclusion bookedInclusion = new BookedInclusion();
                bookedInclusion.setCode(inclusion.getCode());
                String controller = MDC.get(MDCHelper.MDCKeys.CONTROLLER.getStringValue());
                if (ControllerConstants.REVIEW_ROOM_INFOS.equalsIgnoreCase(controller))
                    bookedInclusion.setSubText(inclusion.getValue());
                bookedInclusion.setText(inclusion.getCode());
                bookedInclusion.setIconType(IconType.DEFAULT);
                bookedInclusion.setCategory(inclusion.getCategory());
                bookedInclusion.setIconUrl("https://promos.makemytrip.com/Hotels_product/Persuasion_Icons/Dot.png");
                bookedInclusion.setSegmentIdentifier(inclusion.getSegmentIdentifier());
                bookedInclusion.setLeafCategory(inclusion.getLeafCategory());
                if (count < 2 && !isMealPlanPresent && CollectionUtils.isNotEmpty(mealPlan)) {
                    String inclusionText50 = inclusion.getValue().substring(0, inclusion.getValue().length() > 30 ? 30 : inclusion.getValue().length()).toLowerCase();
                    if (Constants.MEAL_PLAN_CODE_BREAKFAST.equalsIgnoreCase(mealPlan.get(0).getCode()) && (inclusionText50.contains("breakfast")) && inclusion.getValue().length() < 50)
                        isMealPlanPresent = true;
                    else if (!Constants.MEAL_PLAN_CODE_ROOM_ONLY.equalsIgnoreCase(mealPlan.get(0).getCode())
                            && !Constants.MEAL_PLAN_CODE_BED_ONLY.equalsIgnoreCase(mealPlan.get(0).getCode())
                              &&  !Constants.MEAL_PLAN_CODE_ACC_ONLY.equalsIgnoreCase(mealPlan.get(0).getCode())
                            && (inclusionText50.contains("breakfast") || inclusionText50.contains("meals")) && inclusion.getValue().length() < 50)
                        isMealPlanPresent = true;
                }
                if (((ControllerConstants.REVIEW_AVAIL_ROOMS.equalsIgnoreCase(controller) || ControllerConstants.REVIEW_ROOM_INFOS.equalsIgnoreCase(controller))
                        && ("Packages".equalsIgnoreCase(inclusion.getCategory()) || "Packages1".equalsIgnoreCase(inclusion.getCategory())
                        || "Packages2".equalsIgnoreCase(inclusion.getCategory()) || "Packages3".equalsIgnoreCase(inclusion.getCategory())
                        || "MMTBLACK".equalsIgnoreCase(inclusion.getCategory())))
                        || (BLACK_SEGMENT_IDENTIFIER.equalsIgnoreCase(inclusion.getSegmentIdentifier()) && isExperimentTrue(experimentDataMap, ExperimentKeys.GOTRIBE_REVAMP.getKey()))) {
                    bookedInclusion.setIconUrl(inclusion.getImageURL());
                }
                if (INCLUSION_TYPE_LOS.equalsIgnoreCase(inclusion.getInclusionType())) {
                    losInclusions.add(bookedInclusion);
                } else {
                    inclusions.add(bookedInclusion);
                }
            }
        }

        //GIHTL-15565 : CLEAN UP -> NO_MEAL_INCLUSION_REMOVE Is always false.
//        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(mealPlan)
//            && !(MapUtils.isNotEmpty(experimentDataMap) && StringUtils.isNotBlank(experimentDataMap.get(Constants.NO_MEAL_INCLUSION_REMOVE)) && Constants.TRUE.equalsIgnoreCase(experimentDataMap.get(Constants.NO_MEAL_INCLUSION_REMOVE)))) {
//            String controller = MDC.get(MDCHelper.MDCKeys.CONTROLLER.getStringValue());
//            if (Constants.MEAL_PLAN_CODE_ROOM_ONLY.equalsIgnoreCase(mealPlan.get(0).getCode())
//                    || Constants.MEAL_PLAN_CODE_BED_ONLY.equalsIgnoreCase(mealPlan.get(0).getCode())
//                    || Constants.MEAL_PLAN_CODE_ACC_ONLY.equalsIgnoreCase(mealPlan.get(0).getCode())
//                    && mealPlanMap.containsKey(mealPlan.get(0).getCode())) {
//                BookedInclusion noMeanInclusion = new BookedInclusion();
//                if(mealPlanMap != null)
//                noMeanInclusion.setText(polyglotService.getTranslatedData(mealPlanMap.get(mealPlan.get(0).getCode())));
//                noMeanInclusion.setCode(noMeanInclusion.getText());
//                if ((ControllerConstants.DETAIL_SEARCH_ROOMS.equalsIgnoreCase(controller) || ControllerConstants.REVIEW_ROOM_INFOS.equalsIgnoreCase(controller)
//                        || ControllerConstants.REVIEW_AVAIL_ROOMS.equalsIgnoreCase(controller))
//                        && ap!=null && ap < apLimitForInclusionIcons) {
//                    noMeanInclusion.setIconUrl("https://promos.makemytrip.com/Hotels_product/Persuasion_Icons/Dot.png");
//                    noMeanInclusion.setIconType(IconType.DEFAULT);
//                } else {
//                    noMeanInclusion.setIconType(IconType.CROSS);
//                }
//                if (ControllerConstants.REVIEW_ROOM_INFOS.equalsIgnoreCase(controller))
//                    noMeanInclusion.setIconUrl("https://promos.makemytrip.com/Hotels_product/Hotel_SR/Android/drawable-hdpi/meal.png");
//                inclusions.add(0, noMeanInclusion);
//
//            } else
//                if (!isMealPlanPresent && StringUtils.isNotBlank(supplierCode) && !Constants.SUPPLIER_INGO.equalsIgnoreCase(supplierCode)) {
//                BookedInclusion noMeanInclusion = new BookedInclusion();
//                noMeanInclusion.setText(mealPlanMap.containsKey(mealPlan.get(0).getCode()) ? polyglotService.getTranslatedData(mealPlanMap.get(mealPlan.get(0).getCode())) : mealPlan.get(0).getValue());
//                noMeanInclusion.setCode(noMeanInclusion.getText());
//                noMeanInclusion.setIconType(IconType.DEFAULT);
//                if (ControllerConstants.REVIEW_ROOM_INFOS.equalsIgnoreCase(controller))
//                    noMeanInclusion.setIconUrl("https://promos.makemytrip.com/Hotels_product/Hotel_SR/Android/drawable-hdpi/meal.png");
//                inclusions.add(0, noMeanInclusion);
//            }
//        }

        if(extraGuestDetail!= null && StringUtils.isNotEmpty(extraGuestDetail.getReviewPageExtraBedText())){
            BookedInclusion bookedInclusion = new BookedInclusion();
            bookedInclusion.setText(extraGuestDetail.getReviewPageExtraBedText());
            bookedInclusion.setCode(extraGuestDetail.getReviewPageExtraBedText());
            bookedInclusion.setIconType(IconType.DEFAULT);
            bookedInclusion.setIconUrl(Constants.INCLUSIONS_DEFAULT_DOT_ICON_URL);
            inclusions.add(bookedInclusion);
            }
        if (StringUtils.isNotEmpty(freeChildText)) {
            BookedInclusion freeChildInclusion = getFreeChildInclusion(freeChildText, INCLUSIONS_DEFAULT_DOT_ICON_URL);
            inclusions.add(0, freeChildInclusion);
        }

        //GIHTL-15565 : CLEAN UP
        boolean isBlockPah = false;
//        boolean isBlockPah = MapUtils.isNotEmpty(experimentDataMap) && experimentDataMap.containsKey("blockPAH")
//                && StringUtils.isNotBlank(experimentDataMap.get("blockPAH")) && Boolean.parseBoolean(experimentDataMap.get("blockPAH"));

        if (CollectionUtils.isNotEmpty(cancelPenaltyList) && cancelPenaltyList.get(0).getCancellationType()!=null
                && cancelPenaltyList.get(0).getCancellationType() ==  CancelPenalty.CancellationType.FREE_CANCELLATON
                && isBnplApplicable != null && isBnplApplicable && !isBlockPah && paymentDetails!= null && paymentDetails.getPaymentMode() != PaymentMode.PAH_WITHOUT_CC) {

            String region = MDC.get(MDCHelper.MDCKeys.REGION.getStringValue());

            /* For FCZPN add an inclusion at start */
            BookedInclusion fczpnInlclusions = new BookedInclusion();
            setInclusionCodeAndText(fczpnInlclusions,bnplVariant,region);
            fczpnInlclusions.setType(Constants.CANCELLATION_TYPE_FCZPN);
            fczpnInlclusions.setIconType(IconType.DEFAULT);
            fczpnInlclusions.setIconUrl("https://promos.makemytrip.com/Hotels_product/Persuasion_Icons/Dot.png");
            fczpnInlclusions.setInclusionCode(Constants.CANCELLATION_TYPE_FCZPN);
            fczpnInlclusions.setCategory(ZPN);
            inclusions.add(0,fczpnInlclusions);
        }

        /*PAH INCLUSION*/
        if (paymentDetails != null && paymentDetails.getPaymentMode()!=null &&
                !Constants.PAS.equalsIgnoreCase(paymentDetails.getPaymentMode().getMappedPayMode())){

            BookedInclusion pahInclusion = new BookedInclusion();

            if(Constants.PAH_WITHOUT_CC.equalsIgnoreCase(paymentDetails.getPaymentMode().name())) {
                pahInclusion.setText(polyglotService.getTranslatedData(PAH_WITHOUT_CC_TEXT_GI));
                pahInclusion.setCode(pahwithoutccText);
            }else{
                pahInclusion.setCode(pahwithccText);
                pahInclusion.setText(polyglotService.getTranslatedData(PAH_WITH_CC_TEXT_GI));
            }

            pahInclusion.setIconType(IconType.DEFAULT);
            pahInclusion.setIconUrl(ICON_URL_PAH_INCLUSION);
            pahInclusion.setInclusionCode(paymentDetails.getPaymentMode().getMappedPayMode());
            pahInclusion.setType(PAY_MODE);
            pahInclusion.setCategory(ZPN);
            inclusions.add(0, pahInclusion);
        }

        if (CollectionUtils.isNotEmpty(losInclusions)) {
            String controller = MDC.get(MDCHelper.MDCKeys.CONTROLLER.getStringValue());
            if (ControllerConstants.REVIEW_ROOM_INFOS.equalsIgnoreCase(controller)) {
                inclusions.addAll(losInclusions);
            } else {
                BookedInclusion losInclusion = mergeLOSInclusions(losInclusions);
                if (null != losInclusion) {
                    inclusions.add(losInclusion);
                }
            }
        }


        if(isRatePlanRedesign(experimentDataMap)){
            inclusions = reorderInclusions(inclusions);
        }
        return inclusions;
    }

    public void transformInclusionsForPackageRatePlan(List<BookedInclusion> inclusionList) {
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(inclusionList)) {
            for (BookedInclusion inclusion : inclusionList) {
                if (StringUtils.isNotBlank(inclusion.getLeafCategory())) {
                    inclusion.setPackageBenefit(true);
                    inclusion.setTextColor(INCLUSIONS_PACKAGE_TEXT_COLOUR);
                    inclusion.setIUrl(inclusion.getIconUrl());
                    if (StringUtils.isNotBlank(inclusion.getIconUrl())
                            && (inclusion.getIconUrl().equals(INCLUSIONS_DEFAULT_DOT_ICON_URL)
                            || inclusion.getIconUrl().equals(INCLUSIONS_DEFAULT_DEFAULT_DOT_ICON_URL))
                    ) {
                        inclusion.setIUrl(INCLUSIONS_RIGHT_TICK_ICON_URL);
                    }
                }
            }
        }
    }

    public String buildPartialRefundDateText(CancellationTimeline cancellationTimeline){
        String partialRefundDateText = "";
        if(cancellationTimeline == null)
            return partialRefundDateText;

        String checkInDate = cancellationTimeline.getCheckInDate();
        String refundDate = "", refundDateTime = "";

        if(CollectionUtils.isNotEmpty(cancellationTimeline.getCancellationPolicyTimelineList()) && cancellationTimeline.getCancellationPolicyTimelineList().get(0) != null) {
            refundDate = cancellationTimeline.getCancellationPolicyTimelineList().get(0).getEndDate();
            refundDateTime = cancellationTimeline.getCancellationPolicyTimelineList().get(0).getEndDateTime();
        }

        boolean isCheckInAndRefundDateSame = false;
        if(StringUtils.isNotEmpty(checkInDate) && StringUtils.isNotEmpty(refundDate)) {
            isCheckInAndRefundDateSame = checkInDate.equalsIgnoreCase(refundDate);
        }

        if(isCheckInAndRefundDateSame) {
            partialRefundDateText = polyglotService.getTranslatedData(ConstantsTranslation.PARTIAL_REFUNDABLE_CHECKIN_TEXT);
        } else {
            String refundText = polyglotService.getTranslatedData(ConstantsTranslation.PARTIAL_REFUNDABLE_WITH_REFUNDDATE_TEXT);
            if(StringUtils.isNotEmpty(refundDate) && StringUtils.isNotEmpty(refundDateTime) && StringUtils.isNotEmpty(refundText)) {
                partialRefundDateText = MessageFormat.format(refundText, refundDate, refundDateTime);
            }else{
                partialRefundDateText = polyglotService.getTranslatedData(ConstantsTranslation.PARTIAL_REFUNDABLE_TEXT);
            }
        }

        return partialRefundDateText;
    }

    public BookedInclusion getFreeChildInclusion(String freeChildText, String dotIconUrl) {
        BookedInclusion freeChildInclusion = null;
        if (StringUtils.isNotEmpty(freeChildText)) {
            freeChildInclusion = new BookedInclusion();
            freeChildInclusion.setType(Constants.KIDS);
            freeChildInclusion.setCategory(Constants.KIDS);
            freeChildInclusion.setIconType(IconType.DEFAULT);
            freeChildInclusion.setIconUrl(dotIconUrl);
            freeChildInclusion.setCode(freeChildText);
            freeChildInclusion.setText(freeChildText);
            freeChildInclusion.setBookable(true);
        }
        return freeChildInclusion;

    }

    public static String appendQueryParamsInUrl(String url, Map<String, String> queryParamValues){
        if(MapUtils.isEmpty(queryParamValues))
            return url;
        UriComponentsBuilder uriComponentsBuilder = UriComponentsBuilder.fromUriString(url);
        for(String paramKey : queryParamValues.keySet()){
            if(StringUtils.isBlank(paramKey) || StringUtils.isBlank(queryParamValues.get(paramKey)))
                continue;
            uriComponentsBuilder.replaceQueryParam(paramKey, queryParamValues.get(paramKey));
        }
        return uriComponentsBuilder.build().toUri().toString();
    }

    public static void updatePayAtHotelText(com.mmt.hotels.clientgateway.response.TotalPricing totalPricing, String payMode, String pahText) {
        if (Utility.isPahOnlyPaymode(payMode)) {
            String region = MDC.get(MDCHelper.MDCKeys.REGION.getStringValue());
            if (Constants.AE.equalsIgnoreCase(region)) {
                totalPricing.setPayAtHotelText(pahText.replace("{currency}", EMPTY_STRING));
            } else {
                totalPricing.setPayAtHotelText(pahText.replace("{currency}", com.mmt.hotels.clientgateway.util.Currency.INR.getCurrencySymbol()));
            }
        }
    }

    public static boolean isPahOnlyPaymode(String payMode) {
        return PaymentMode.PAH_WITH_CC.name().equalsIgnoreCase(payMode);
    }

    public static boolean isPahWithCCPaymode(String payMode) {
        return PaymentMode.PAH_WITH_CC.name().equalsIgnoreCase(payMode);
    }

	public static Map<String, String> getRequestParam(Map<String, String[]> paramsMap) {
		Map<String, String> requestParam = new HashMap<String, String>();
		try {
			for (Map.Entry<String, String[]> entry : paramsMap.entrySet()) {
				requestParam.put(entry.getKey(), entry.getValue()[0]);
			}
		} catch (Exception e) {
			LOGGER.error("Error in getRequestParam {}", e);
		}
		return requestParam;
	}

	public static String getDeviceInfo(DeviceDetails deviceDetails) {
		return deviceDetails != null && deviceDetails.getOsType() != null
					? deviceDetails.getOsType().getValue().toUpperCase() : "";
	}

    public String getUpdatedPropertyType(String propertyType) {
        if(Constants.PROPERTY_TYPE_HOMESTAY.equalsIgnoreCase(propertyType)){
            return polyglotService.getTranslatedData(ConstantsTranslation.HOMESTAY_DISPLAY_TEXT);
        }
        return propertyType;
    }

	public Tuple<String, String> getGuestRoomKeyValue(Map<String, Integer> roomBedCountMap, String propertyType, String listingType){
        String guestRoomKey = null;
        String guestRoomValue = null;
        if(Constants.LISTING_TYPE_ENTIRE.equalsIgnoreCase(listingType)){
            guestRoomKey = polyglotService.getTranslatedData(ConstantsTranslation.GUESTS);
            guestRoomValue = polyglotService.getTranslatedData("ENTIRE") + Constants.SPACE + propertyType;
        }else{
            int bedCount = roomBedCountMap.get(Constants.SELLABLE_BED_TYPE);
            int roomCount = roomBedCountMap.get(Constants.SELLABLE_ROOM_TYPE);
            String roomGuestValue = null;
            String bedGuestValue = null;
            if(bedCount==1)
                bedGuestValue = polyglotService.getTranslatedData("BED_TEXT").replace("{num}", String.valueOf(bedCount));
            else if(bedCount > 1)
                bedGuestValue = polyglotService.getTranslatedData("BEDS_TEXT").replace("{num}", String.valueOf(bedCount));
            if(roomCount == 1)
                roomGuestValue = polyglotService.getTranslatedData("ROOM_TEXT").replace("{num}", String.valueOf(roomCount));
            else if(roomCount > 1)
                roomGuestValue = polyglotService.getTranslatedData("ROOMS_TEXT").replace("{num}", String.valueOf(roomCount));
            if(bedCount > 0 && roomCount > 0){
                guestRoomKey = polyglotService.getTranslatedData(ConstantsTranslation.GUESTS_AND_ROOMS);
                guestRoomValue =roomGuestValue + Constants.COMMA + Constants.SPACE +bedGuestValue;
            }else if(bedCount == 0){
                guestRoomKey = polyglotService.getTranslatedData(ConstantsTranslation.GUESTS_AND_ROOMS);
                guestRoomValue = roomGuestValue;
            }else if(roomCount == 0){
                guestRoomKey = polyglotService.getTranslatedData(ConstantsTranslation.GUESTS_AND_BEDS);
                guestRoomValue = bedGuestValue;
            }
        }
        return new Tuple<String,String>(guestRoomKey,guestRoomValue);
    }

    public Tuple<String, String> getGuestRoomKeyValue(Map<String, Integer> roomBedCountMap, String propertyType, String listingType, boolean serviceApartment, int propertyCount) {
        String guestRoomKey = null;
        String guestRoomValue = null;
        if (Constants.LISTING_TYPE_ENTIRE.equalsIgnoreCase(listingType)) {
            guestRoomKey = polyglotService.getTranslatedData(ConstantsTranslation.GUESTS);
            guestRoomValue = new StringBuilder().append(polyglotService.getTranslatedData(ConstantsTranslation.ENTIRE)).append(Constants.SPACE).append(propertyType).append(propertyCount > 1 ? "s" : "").toString();
            guestRoomValue = modifyStayType(serviceApartment, guestRoomValue, propertyCount);
        } else {
            int bedCount = roomBedCountMap.get(Constants.SELLABLE_BED_TYPE);
            int roomCount = roomBedCountMap.get(Constants.SELLABLE_ROOM_TYPE);
            String roomGuestValue = null;
            String bedGuestValue = null;
            if (bedCount == 1)
                bedGuestValue = polyglotService.getTranslatedData(ConstantsTranslation.BED_TEXT).replace("{num}", String.valueOf(bedCount));
            else if (bedCount > 1)
                bedGuestValue = polyglotService.getTranslatedData(ConstantsTranslation.BEDS_TEXT).replace("{num}", String.valueOf(bedCount));
            if (roomCount == 1)
                roomGuestValue = polyglotService.getTranslatedData(ConstantsTranslation.ROOM_TEXT).replace("{num}", String.valueOf(roomCount));
            else if (roomCount > 1)
                roomGuestValue = polyglotService.getTranslatedData(ConstantsTranslation.ROOMS_TEXT).replace("{num}", String.valueOf(roomCount));
            if (bedCount > 0 && roomCount > 0) {
                guestRoomKey = polyglotService.getTranslatedData(ConstantsTranslation.GUESTS_AND_ROOMS);
                guestRoomValue = roomGuestValue + Constants.COMMA + Constants.SPACE + bedGuestValue;
            } else if (bedCount == 0) {
                guestRoomKey = polyglotService.getTranslatedData(ConstantsTranslation.GUESTS_AND_ROOMS);
                guestRoomValue = roomGuestValue;
            } else if (roomCount == 0) {
                guestRoomKey = polyglotService.getTranslatedData(ConstantsTranslation.GUESTS_AND_BEDS);
                guestRoomValue = bedGuestValue;
            }
        }
        return new Tuple<String, String>(guestRoomKey, guestRoomValue);
    }

    private String modifyStayType(boolean serviceApartment, String stayType, int propertyCount) {
        if (serviceApartment && StringUtils.isNotEmpty(stayType)) {
            if (ROOM_IN_APARTMENT.equalsIgnoreCase(stayType)) return ROOM_IN_SERVICE_APARTMENT;
            if (ENTIRE_APARTMENT.equalsIgnoreCase(stayType) || ENTIRE_APARTMENTS.equalsIgnoreCase(stayType))
                return propertyCount > 1 ? ENTIRE_SERVICE_APARTMENTS : ENTIRE_SERVICE_APARTMENT;
        }
        return stayType;
    }

	public static void populateCommIdsInUserDetails(UserDetail userDetail,String correlationKey, ScramblerClient scramblerClient) throws ScramblerClientException {
		if(!StringUtils.isEmpty(userDetail.getEmailID())){
			userDetail.setEmailCommId(scramblerClient.encode(userDetail.getEmailID(), HashType.F));
		}
		PhoneNumberUtil phoneNumberUtil = PhoneNumberUtil.getInstance();
		if (!StringUtils.isEmpty(userDetail.getMobileNo())) {
			try {
				Phonenumber.PhoneNumber phoneNumber = phoneNumberUtil.parse("+" + userDetail.getMobileNo(), "");
				Integer isdCode = phoneNumber.getCountryCode();
				String mobileNum = Long.toString(phoneNumber.getNationalNumber());
				userDetail.setPhoneCommId(scramblerClient.encode(mobileNum, isdCode, HashType.N));
			}catch(Exception ex){
				LOGGER.error("Phone number from client in user detail is faulty, corr :" + correlationKey , ex);
			}
		}
	}

	public static boolean isPropertyHotelOrResort(List<HotelRates> hotelRates){
        if (CollectionUtils.isEmpty(hotelRates))
            return false;
        if ("hotel".equalsIgnoreCase(hotelRates.get(0).getPropertyType()) || "resort".equalsIgnoreCase(hotelRates.get(0).getPropertyType()))
            return true;
        return false;
    }

    public String getComboName(String mealPlanCode) {
        if (StringUtils.isNotBlank(mealPlanCode)) {
            switch (mealPlanCode){
                case Constants.MEAL_PLAN_CODE_ACC_ONLY:
                    return polyglotService.getTranslatedData("ACCOMODATION_ONLY");
                case Constants.MEAL_PLAN_CODE_BED_ONLY:
                    return polyglotService.getTranslatedData("BED_ONLY");
                case Constants.MEAL_PLAN_CODE_ROOM_ONLY:
                    return polyglotService.getTranslatedData("ROOM_ONLY_TEXT");
                case Constants.MEAL_PLAN_CODE_BREAKFAST:
                    return polyglotService.getTranslatedData("WITH_BREAKFAST_TEXT");
                case Constants.MEAL_PLAN_CODE_BREAKFAST_LUNCH:
                    return polyglotService.getTranslatedData("WITH_BREAKFAST_AND_LUNCH");
                case Constants.MEAL_PLAN_CODE_BREAKFAST_DINNER:
                    return polyglotService.getTranslatedData("WITH_BREAKFAST_AND_DINNER");
                case Constants.MEAL_PLAN_CODE_BREAKFAST_LUNCH_OR_DINNER:
                    return polyglotService.getTranslatedData("WITH_B_AND_LD");
                case Constants.MEAL_PLAN_CODE_ALL_MEALS:
                case Constants.MEAL_PLAN_CODE_ALL_MEALS_AI:
                    return polyglotService.getTranslatedData("WITH_B_L_D");
                default:
                    return null;
            }
        }
        return null;
    }

    public static String checkMealPlan(String mealCode){
        String mealName="RO";
        if ("EP".equalsIgnoreCase(mealCode) || "BD".equalsIgnoreCase(mealCode) || "AO".equalsIgnoreCase(mealCode)) {
            mealName="RO";
        } else if ("AP".equalsIgnoreCase(mealCode) || "AI".equalsIgnoreCase(mealCode)) {
            mealName="AP";
        } else if ("CP".equalsIgnoreCase(mealCode)) {
            mealName="CP";
        } else if ("MAP".equalsIgnoreCase(mealCode) || "SMAP".equalsIgnoreCase(mealCode) || "TMAP".equalsIgnoreCase(mealCode)) {
            mealName="MAP";
        }
        return mealName;
    }

    public String getRatePlanName(List<MealPlan> mealPlanList, BookedCancellationPolicy cancellationPolicy, String sellableType, String listingType, String expData) {
        return getRatePlanName(mealPlanList, cancellationPolicy, sellableType, listingType, getExpDataMap(expData));
    }

    public String getRatePlanName(List<MealPlan> mealPlanList, BookedCancellationPolicy cancellationPolicy, String sellableType, String listingType, Map<String, String> expDataMap) {

	    String ratePlanName = StringUtils.EMPTY;

        String mealCode = CollectionUtils.isNotEmpty(mealPlanList) ? mealPlanList.get(0).getCode() : StringUtils.EMPTY;

        String cancellationPolicyString = Constants.CANCELLATION_TYPE_NR;
        if (cancellationPolicy!=null && cancellationPolicy.getType()!=null) {
            if (Constants.CANCELLATION_TYPE_FC.equalsIgnoreCase(cancellationPolicy.getType().name()))
                cancellationPolicyString = Constants.CANCELLATION_TYPE_FC;
            else if (Constants.CANCELLATION_TYPE_NR.equalsIgnoreCase(cancellationPolicy.getType().name()))
                cancellationPolicyString = Constants.CANCELLATION_TYPE_NR;
            else if (Constants.CANCELLATION_TYPE_FCZPN.equalsIgnoreCase(cancellationPolicy.getType().name()))
                cancellationPolicyString = Constants.CANCELLATION_TYPE_FCZPN;
        }

        if (Constants.SELLABLE_ENTIRE_TYPE.equalsIgnoreCase(listingType))
            sellableType = Constants.SELLABLE_ENTIRE_TYPE;
        else
            sellableType = StringUtils.isBlank(sellableType) ? (Constants.SELLABLE_ROOM_TYPE).toUpperCase() : sellableType.toUpperCase();

        // this new variable is required because now we can have 2 values for ratePlanNameConfiguration and the relevant one will be added here
        Map<String, Map<String, Map<String, String>>> ratePlanNameMapTemp;
        // we have created a different configuration for name and it is only used when we get RATE_PLAN_REDESIGN as true form pokus
        if(isRatePlanRedesign(expDataMap)){
            ratePlanNameMapTemp = ratePlanNameMapRedesign;
        }else {
            ratePlanNameMapTemp = ratePlanNameMap;
        }
        if(MapUtils.isNotEmpty(ratePlanNameMapTemp)){
            if (!ratePlanNameMapTemp.containsKey(mealCode)) {
                if (ratePlanNameMapTemp.get(Constants.DEFAULT).get(cancellationPolicyString).containsKey(sellableType))
                    ratePlanName = ratePlanNameMapTemp.get(Constants.DEFAULT).get(cancellationPolicyString).get(sellableType);
                else
                    ratePlanName = ratePlanNameMapTemp.get(Constants.DEFAULT).get(cancellationPolicyString).get(Constants.SELLABLE_ROOM_TYPE);
            } else {
                if (ratePlanNameMapTemp.get(mealCode).get(cancellationPolicyString).containsKey(sellableType))
                    ratePlanName = ratePlanNameMapTemp.get(mealCode).get(cancellationPolicyString).get(sellableType);
                else
                    ratePlanName = ratePlanNameMapTemp.get(mealCode).get(cancellationPolicyString).get(Constants.SELLABLE_ROOM_TYPE);
            }
        }
        LOGGER.debug(ratePlanName);
        return getTranslationFromPolyglot(ratePlanName);
    }

    private String getTranslationFromPolyglot(String text) {
	    if (StringUtils.isBlank(text))
	        return text;
	    StringBuilder translatedText = new StringBuilder(text);
	    try {
            while (StringUtils.isNotBlank(translatedText) && translatedText.indexOf("{")!=-1) {
                int start = translatedText.indexOf("{");
                int end = translatedText.indexOf("}");
                String constant = translatedText.substring(start+1,end);
                translatedText.replace(start,end+1,polyglotService.getTranslatedData(constant));
            }
        } catch (StringIndexOutOfBoundsException e) {
	        LOGGER.warn("Error occurred when translating RatePlan Name.");
        }
	    return translatedText.toString();
    }

    public boolean isRatePlanRedesign(Map<String,String> experimentDataMap){
        //this functions helps us to know if we need to show the new ratePlan design based on 2 exp keys srrp(client) and ratePlanRedesign(Pokus)
        return false;
//        return MapUtils.isNotEmpty(experimentDataMap) && experimentDataMap.containsKey(ExperimentKeys.EXP_SRRP.getKey()) && EXP_TRUE_VALUE.equalsIgnoreCase(experimentDataMap.get(ExperimentKeys.EXP_SRRP.getKey()))
//                && experimentDataMap.containsKey(ExperimentKeys.EXP_RATE_PLAN_REDESIGN.getKey()) && TRUE.equalsIgnoreCase(experimentDataMap.get(ExperimentKeys.EXP_RATE_PLAN_REDESIGN.getKey()));
    }

    public boolean isReviewPageAPI(String controller) {
	    if (StringUtils.isBlank(controller))
	        return false;
	    if (ControllerConstants.REVIEW_AVAIL_ROOMS.equalsIgnoreCase(controller) ||
              ControllerConstants.REVIEW_VAILDATE_COUPON.equalsIgnoreCase(controller) ||
              ControllerConstants.REVIEW_TOTAL_PRICING.equalsIgnoreCase(controller) ||
              ControllerConstants.THANKYOU_CONTROLLER.equalsIgnoreCase(controller)) {
	        return true;
        }
	    return false;
    }

    public boolean isDetailPageAPI(String controller) {
        if (StringUtils.isBlank(controller))
            return false;
        if (ControllerConstants.DETAIL_SEARCH_ROOMS.equalsIgnoreCase(controller) ||
                ControllerConstants.DETAIL_UPDATE_PRICE.equalsIgnoreCase(controller)
                || ControllerConstants.DETAIL_SEARCH_SLOTS.equalsIgnoreCase(controller)) {
            return true;
        }
        return false;
    }

    public boolean shouldDisplayOfferDiscountBreakup(Map<String, String> expData) {
        return MapUtils.isNotEmpty(expData) && "T".equalsIgnoreCase(expData.get("LSOF"));
    }

    public static boolean gocashVariant(Map<String, String> expData, boolean isDomestic) {
        if (expData != null && MapUtils.isNotEmpty(expData) && (
                (expData.containsKey("recomFlow") && expData.get("recomFlow").equals("true")) ||
                (isDomestic && expData.containsKey("wallet_exp") && expData.get("wallet_exp").equals("1")) ||
                (!isDomestic && expData.containsKey(WalletExpIH) && expData.get(WalletExpIH).equals("1"))
        )
        ) {
            return false;
        }
        if (isDomestic && expData != null && MapUtils.isNotEmpty(expData) && expData.containsKey(ExperimentKeys.EXP_WALLET.getKey()) &&
                StringUtils.isNotBlank(expData.get(ExperimentKeys.EXP_WALLET.getKey()))
                ) {
            return Integer.valueOf(expData.get(ExperimentKeys.EXP_WALLET.getKey())) > 1;
        } else if (!isDomestic && expData != null && MapUtils.isNotEmpty(expData) && expData.containsKey(ExperimentKeys.EXP_WALLET_IH.getKey()) &&
                StringUtils.isNotBlank(expData.get(ExperimentKeys.EXP_WALLET_IH.getKey()))
        ) {
            return Integer.valueOf(expData.get(ExperimentKeys.EXP_WALLET_IH.getKey())) > 1;
        }
        return false;
    }

    public static String searchRoomRecomValue(RoomDetailsResponse roomDetailsResponse) {
        HotelRates hotelRates = roomDetailsResponse.getHotelRates() != null &&
                !roomDetailsResponse.getHotelRates().isEmpty()  &&
                roomDetailsResponse.getHotelRates().get(0) != null ? roomDetailsResponse.getHotelRates().get(0): null;
        if (hotelRates != null && hotelRates.getRecommendedRoomTypeDetails() != null){
            return "true";
        }
        if (hotelRates == null || hotelRates.getLowestRate().getTotalRoomCounts() <=1) {
            return "false";
        }

        return "true";
    }

    public static String  availRecomValue(RoomDetailsResponse roomDetailsResponse) {
        RoomTypeDetails roomTypeDetails = roomDetailsResponse.getHotelRates() != null &&
               !roomDetailsResponse.getHotelRates().isEmpty()  &&
               roomDetailsResponse.getHotelRates().get(0) != null ? roomDetailsResponse.getHotelRates().get(0).getRoomTypeDetails() : null;
        if (roomTypeDetails == null) {
            return "false";
        }
        Map<String, RoomType> roomTypes = roomTypeDetails.getRoomType() != null ? roomTypeDetails.getRoomType() : new HashMap<String, RoomType>();
        if (roomTypes.size() > 1) {
            return "true";
        }
        for(Map.Entry<String,RoomType> entry : roomTypes.entrySet()){

            Map<String, RatePlan> ratePlanList =  entry.getValue() != null ? entry.getValue().getRatePlanList(): new HashMap<String, RatePlan>();
            if (ratePlanList.size() > 1) {
                return "true";
            }
            for(Map.Entry<String,RatePlan> rateEntry : ratePlanList.entrySet()) {
                return rateEntry.getValue() != null && rateEntry.getValue().getRoomTariff() != null &&
                        rateEntry.getValue().getRoomTariff().size() > 1 ? "true" : "false";
            }
        }

        return "false";
    }



    public static boolean gocashVariant2(Map<String, String> expData, boolean isDomestic) {
        if (isDomestic && MapUtils.isNotEmpty(expData) && expData.containsKey(ExperimentKeys.EXP_WALLET.getKey())) {
            return Integer.valueOf(expData.get(ExperimentKeys.EXP_WALLET.getKey())) == 2;
        } else if (!isDomestic && MapUtils.isNotEmpty(expData) && expData.containsKey(ExperimentKeys.EXP_WALLET_IH.getKey())) {
            return Integer.valueOf(expData.get(ExperimentKeys.EXP_WALLET_IH.getKey())) == 2;
        }
        return false;
    }

    public static boolean gocashVariant3(Map<String, String> expData, boolean isDomestic) {
        if (isDomestic && MapUtils.isNotEmpty(expData) && expData.containsKey(ExperimentKeys.EXP_WALLET.getKey())) {
            return Integer.valueOf(expData.get(ExperimentKeys.EXP_WALLET.getKey())) == 3;
        } else if (!isDomestic && MapUtils.isNotEmpty(expData) && expData.containsKey(ExperimentKeys.EXP_WALLET_IH.getKey())) {
            return Integer.valueOf(expData.get(ExperimentKeys.EXP_WALLET_IH.getKey())) == 3;
        }
        return false;
    }

    public boolean buildToolTip(String funnelSource) {
	    if (Constants.FUNNEL_SOURCE_HOMESTAY.equalsIgnoreCase(funnelSource))
	        return true;
	    return false;
    }

    public boolean isExperimentOn(Map<String, String> expData, String expKey){
        if(MapUtils.isNotEmpty(expData)
                && StringUtils.isNotBlank(expData.get(expKey))
                && "t".equalsIgnoreCase(expData.get(expKey))){
            return true;
        }
        return false;
    }

    public boolean isExperimentTrue(Map<String, String> expData, String expKey) {
        if (MapUtils.isNotEmpty(expData)
                && StringUtils.isNotBlank(expData.get(expKey))
                && "true".equalsIgnoreCase(expData.get(expKey))) {
            return true;
        }
        return false;
    }

    public boolean isSPKGExperimentOn(Map<String, String> expData) {
        return MapUtils.isNotEmpty(expData) && StringUtils.isNotBlank(expData.get("SPKG"))
                && (TRUE.equalsIgnoreCase(expData.get("SPKG")) || EXP_TRUE_VALUE.equalsIgnoreCase(expData.get("SPKG")));
    }

    public boolean isShowOccassionPackagesPlanExperimentOn(Map<String, String> expData) {
        return MapUtils.isNotEmpty(expData) && StringUtils.isNotBlank(expData.get("showOccassionPackagesPlan"))
                && (TRUE.equalsIgnoreCase(expData.get("showOccassionPackagesPlan")) || EXP_TRUE_VALUE.equalsIgnoreCase(expData.get("showOccassionPackagesPlan")));
    }

    public boolean isBusinessIdentifyEnableExperimentOn(Map<String, String> expData) {
        return MapUtils.isNotEmpty(expData) && expData.getOrDefault(BUSINESS_IDENTIFY_ENABLE_EXP, "").equalsIgnoreCase(TRUE);
    }

    public boolean showBusinessIdentificationCard(Map<String, String> expData) {
        return MapUtils.isNotEmpty(expData) && expData.getOrDefault(SHOW_BUSINESS_IDENTIFICATION_CARD_EXP, "").equalsIgnoreCase(TRUE);
    }
    public static boolean isAppRequest() {
        return ANDROID.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue())) || DEVICE_IOS.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue()));
    }

    public static boolean isPWARequest() {
        return Constants.DEVICE_OS_PWA.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue()));
    }

    public static boolean isDesktopRequest() {
        return Constants.DEVICE_OS_DESKTOP.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue()));
    }

    /*
    * myPartner change log :
    *   We are using this utility to check myPartner requests at all the places where we have introduced myPartner changes
    *   in the codebase.
    *
    *   Since we did not want to use an autowired bean utility [we will be having this check at a lot of places in the codebase], we are using
    *   a static function which checks static constants.
    * */
    public  static boolean isMyPartnerRequest(String profileType, String subProfileType){
        if(StringUtils.isNotBlank(profileType) &&
                StringUtils.isNotBlank(subProfileType))
            return (profileType.equalsIgnoreCase(PROFILE_TYPE) && subProfileType.equalsIgnoreCase(SUB_PROFILE_TYPE));
        return false;
    }

    public static Tuple<String,String> getCheckinAndCheckoutForDailyUse(String roomName) {
        if (StringUtils.isNotBlank(roomName)) {
            try {
                Matcher matcher = HH_AA_TIME_REGEX.matcher(roomName);
                List<String> regexMatches = new ArrayList<String>();
                while (matcher.find()) {
                    regexMatches.add(matcher.group());
                }
                if (regexMatches.size() > 1) {
                    regexMatches = regexMatches.stream().map(item -> item.replaceAll("\\s+", "").replaceAll("\\.","")).collect(Collectors.toList());
                    String checkinTime = regexMatches.get(0);
                    int split = checkinTime.length()-2;
                    checkinTime = checkinTime.substring(0,split)+" "+checkinTime.substring(split).toUpperCase();
                    String checkoutTime = regexMatches.get(1);
                    split = checkoutTime.length()-2;
                    checkoutTime = checkoutTime.substring(0,split)+" "+checkoutTime.substring(split).toUpperCase();
                    return new Tuple<>(checkinTime,checkoutTime);
                }
            } catch (Exception e){
                LOGGER.error("Error while parsing the checkin/checkout time for Daily use room");
            }
        }
        return null;
    }

    public boolean isValidAppVersion(String appVersion,String allowedMinAppVersion) {
        String[] allowedAppVersion=allowedMinAppVersion.split("\\.");
        String[] currentAppVersion=appVersion.split("\\.");

        for(int i=0;i < allowedAppVersion.length;i++){
            if(currentAppVersion==null || currentAppVersion.length==i){
                return false;
            }else if(Integer.valueOf(currentAppVersion[i])<Integer.valueOf(allowedAppVersion[i])){
                return false;
            }else if(Integer.valueOf(currentAppVersion[i])>Integer.valueOf(allowedAppVersion[i])){
                return true;
            }
        }
        return true;
    }
    public Map<String, String[]> addFunnelSourceToParameterMap(String funnelSource, Map<String, String[]> parameterMap, LinkedHashMap<String, String> expDataMap) {
        Map<String, String[]> resultMap = new HashMap<>();
        if (MapUtils.isNotEmpty(parameterMap)) {
            resultMap.putAll(parameterMap);
            if(StringUtils.isNotBlank(funnelSource))
                resultMap.put(Constants.FILTER_COND_FUNNEL, new String[]{funnelSource});
        }
        if(expDataMap!=null && EXP_TRUE_VALUE.equalsIgnoreCase(expDataMap.get(UGCV2))){
            resultMap.put(Constants.IS_UGC_V2, new String[]{TRUE});
        }
        return resultMap;
    }

    public void buildSlot(SearchWrapperInputRequest searchWrapperInputRequest, SearchHotelsCriteria searchCriteria) {
        if (searchCriteria.getSlot() != null) {
            Slot slot = new Slot();
            slot.setDuration(searchCriteria.getSlot().getDuration());
            slot.setTimeSlot(searchCriteria.getSlot().getTimeSlot());
            searchWrapperInputRequest.setSlot(slot);
        }
    }
    public boolean isLuxeHotel(Set<String> categories) {
        if (CollectionUtils.isEmpty(categories)) {
            return false;
        }
        return categories.contains(Constants.LUXURY_HOTELS);
    }

    public CountDownLatch getCountDownLatch() {
        return new CountDownLatch(3);
    }

    public CountDownLatch getCountDownLatch(int numOfThreads) {
        return new CountDownLatch(numOfThreads);
    }

    public void logRequestResponse(Object reqResponse, String logText) {
        try {
            LOGGER.warn(logText, objectMapperUtil.getJsonFromObject(reqResponse, DependencyLayer.CLIENTGATEWAY));
        } catch (Exception e) {
            LOGGER.error("Error while printing request response logs", e);
        }

    }

    public void logRequestResponseInDebug(Object reqResponse, String logText) {
        try {
            LOGGER.debug(logText, objectMapperUtil.getJsonFromObject(reqResponse, DependencyLayer.CLIENTGATEWAY));
        } catch (Exception e) {
            LOGGER.error("Error while printing request response logs", e);
        }

    }

    public void setPaginatedToMDC(SearchHotelsCriteria searchCriteria) {
        if (StringUtils.isNotBlank(searchCriteria.getLastHotelId())) {
            MDC.put(MDCHelper.MDCKeys.PAGINATED.getStringValue(), String.valueOf(true));
        } else {
            MDC.put(MDCHelper.MDCKeys.PAGINATED.getStringValue(), String.valueOf(false));
        }
    }

    public void setLoggingParametersToMDC(List<com.mmt.hotels.clientgateway.request.RoomStayCandidate> roomStayCandidates, String checkIn, String checkOut) {
        setLengthOfStay(checkIn, checkOut);
        setAdvancePurchase(checkIn);
        setAdultCount(roomStayCandidates);
        setChildCount(roomStayCandidates);
    }

    private void setLengthOfStay(String checkIn, String checkOut) {
        try {
            if (StringUtils.isNotBlank(checkIn) && StringUtils.isNotBlank(checkOut)) {
                int lengthOfStay = dateUtil.getDaysDiff(checkIn, checkOut);
                if (lengthOfStay > 0 && lengthOfStay <= 5) {
                    MDC.put(MDCHelper.MDCKeys.LENGTH_OF_STAY.getStringValue(), String.valueOf(lengthOfStay));
                }
            }
        } catch (Exception ex) {
            LOGGER.error("Error in setLengthOfStay", ex);
        }
    }

    private void setAdvancePurchase(String checkIn) {
        try {
            if (StringUtils.isNotBlank(checkIn)) {
                int advancePurchase = dateUtil.getDaysDiff(checkIn);
                if (advancePurchase > 0 && advancePurchase <= 5) {
                    MDC.put(MDCHelper.MDCKeys.ADVANCE_PURCHASE.getStringValue(), String.valueOf(advancePurchase));
                }
            }
        } catch (Exception ex) {
            LOGGER.error("Error in setAdvancePurchase", ex);
        }
    }

    private void setAdultCount(List<com.mmt.hotels.clientgateway.request.RoomStayCandidate> roomStayCandidates) {
        try {
            int adultCount = 0;
            if (CollectionUtils.isNotEmpty(roomStayCandidates)) {
                for (com.mmt.hotels.clientgateway.request.RoomStayCandidate roomStayCandidate : roomStayCandidates) {
                    adultCount += roomStayCandidate.getAdultCount();
                }
            }
            if (adultCount > 0 && adultCount <= 5) {
                MDC.put(MDCHelper.MDCKeys.ADULT_COUNT.getStringValue(), String.valueOf(adultCount));
            }
        } catch (Exception ex) {
            LOGGER.error("Error in setAdultCount", ex);
        }
    }

    private void setChildCount(List<com.mmt.hotels.clientgateway.request.RoomStayCandidate> roomStayCandidates) {
        try {
            int childCount = 0;
            if (CollectionUtils.isNotEmpty(roomStayCandidates)) {
                for (com.mmt.hotels.clientgateway.request.RoomStayCandidate roomStayCandidate : roomStayCandidates) {
                    if (CollectionUtils.isNotEmpty(roomStayCandidate.getChildAges())) {
                        childCount += roomStayCandidate.getChildAges().size();
                    }
                }
            }
            if (childCount > 0 && childCount <= 5) {
                MDC.put(MDCHelper.MDCKeys.CHILD_COUNT.getStringValue(), String.valueOf(childCount));
            }
        } catch (Exception ex) {
            LOGGER.error("Error in setChildCount", ex);
        }
    }

    public Map<String, String[]> addSrcReqToParameterMap(Map<String, String[]> parameterMap) {
        Map<String, String[]> resultMap = new HashMap<>();
        if (MapUtils.isNotEmpty(parameterMap)) {
            resultMap.putAll(parameterMap);
        }
        resultMap.put("srcReq", new String[]{"CG_GI"});
        return resultMap;
    }

    public  Map<String, String> getExpDataMap(String expData) {
        Map<String, String> expDataMap = null;
        if (StringUtils.isNotBlank(expData)) {
            try {
                Type type = new TypeToken<Map<String, String>>() {
                }.getType();
                expData = expData.replaceAll("^\"|\"$", "");
                expDataMap = gson.fromJson(expData, type);
            } catch (Exception e) {
                LOGGER.error("error while converting expDataMap", e);
            }
        }
        return expDataMap;
    }
    public MmtExclusive buildMmtExclusiveNode(List<Inclusion> inclusionsList){
        MmtExclusive mmtExclusive = new MmtExclusive();
        mmtExclusive.setImageUrl(Constants.MMT_EXCLUSIVE_IMAGE_URL);
        List<String> mmtExclusiveInclusionList = new ArrayList<>();

        mmtExclusiveInclusionList.add(polyglotService.getTranslatedData(ConstantsTranslation.MMT_EXCLUSIVE));
        boolean checkList = CollectionUtils.isNotEmpty(inclusionsList) && inclusionsList.get(0) !=null && inclusionsList.get(0).getCode() != null
                && !Constants.NULL_STRING.equalsIgnoreCase(inclusionsList.get(0).getCode())  ? mmtExclusiveInclusionList.add(inclusionsList.get(0).getCode()) : false;
        checkList = CollectionUtils.isNotEmpty(inclusionsList) && inclusionsList.size() >1 && inclusionsList.get(1) !=null && inclusionsList.get(1).getCode() != null
                && !Constants.NULL_STRING.equalsIgnoreCase(inclusionsList.get(1).getCode())  ? mmtExclusiveInclusionList.add(inclusionsList.get(1).getCode()) : false;

        mmtExclusive.setInclusions(mmtExclusiveInclusionList);
        return mmtExclusive;
    }
    public boolean isExpPdoPrnt(String expData) {
        if (org.codehaus.plexus.util.StringUtils.isNotBlank(expData)) {
            Type type = new TypeToken<Map<String, String>>() {
            }.getType();
            expData = expData.replaceAll("^\"|\"$", "");
            Map<String, String> expDataMap = new Gson().fromJson(expData, type);
            return expDataMap != null && expDataMap.containsKey("PDO") && org.codehaus.plexus.util.StringUtils.equalsIgnoreCase(expDataMap.get("PDO"), "PRNT");
        }
        return false;
    }

    public static boolean isGroupBookingFunnel(String funnelSource) {
        return StringUtils.equalsIgnoreCase(funnelSource, FUNNEL_SOURCE_GROUP_BOOKING);
    }
    public static boolean isCorpBudgetHotelFunnel(String funnelSource) {
        return StringUtils.equalsIgnoreCase(funnelSource, FUNNEL_SOURCE_CORPBUDGET);
    }

    public String calculateTimeSlot_Meridiem(com.mmt.hotels.model.response.dayuse.Slot slot) {
        if(slot == null)
            return null;
        String startMeridien = null;
        String endMeridien = null;
        String translatedText = null;
        if(slot.getTimeSlot() != null && slot.getDuration() != null) {
            Integer checkOutTime = Integer.parseInt(slot.getTimeSlot()) + slot.getDuration();
            Integer checkinTime = Integer.parseInt(slot.getTimeSlot());
            startMeridien = checkinTime < 12 ? AM : PM;
            endMeridien = checkOutTime < 12 ? AM : (checkOutTime >= 24 && checkOutTime % 12 >= 0) ? AM : PM;

            if (startMeridien.equals(AM) && endMeridien.equals(AM)) {
                translatedText = polyglotService.getTranslatedData(TIMESLOT_AM_AM);
                translatedText = MessageFormat.format(translatedText, checkinTime % 12==0? 12:checkinTime % 12 , (checkOutTime % 12 == 0 ? 12 : checkOutTime % 12));
            } else if (startMeridien.equals(AM) && endMeridien.equals(PM)) {
                translatedText = polyglotService.getTranslatedData(TIMESLOT_AM_PM);
                translatedText = MessageFormat.format(translatedText, checkinTime % 12 == 0? 12:checkinTime % 12, (checkOutTime % 12 == 0 ? 12 : checkOutTime % 12));
            } else if (startMeridien.equals(PM) && endMeridien.equals(AM)) {
                translatedText = polyglotService.getTranslatedData(TIMESLOT_PM_AM);
                translatedText = MessageFormat.format(translatedText, checkinTime % 12 == 0? 12:checkinTime % 12, (checkOutTime % 12 == 0 ? 12 : checkOutTime % 12));
            } else {
                translatedText = polyglotService.getTranslatedData(TIMESLOT_PM_PM);
                translatedText = MessageFormat.format(translatedText, checkinTime % 12 == 0? 12:checkinTime % 12, (checkOutTime % 12 == 0 ? 12 : checkOutTime % 12));
            }
        }
        return translatedText;
    }

    public String calculateTimeSlot_Meridiem(String timeSlot, Integer duration ) {
        String startMeridien = null;
        String endMeridien = null;
        String translatedText = null;
        if(StringUtils.isNotEmpty(timeSlot) && duration != null) {
            Integer checkOutTime = Integer.parseInt(timeSlot) + duration;
            Integer checkinTime = Integer.parseInt(timeSlot);
            startMeridien = checkinTime < 12 ? AM : PM;
            endMeridien = checkOutTime < 12 ? AM : (checkOutTime >= 24 && checkOutTime % 12 >= 0) ? AM : PM;

            if (startMeridien.equals(AM) && endMeridien.equals(AM)) {
                translatedText = polyglotService.getTranslatedData(TIMESLOT_AM_AM);
                translatedText = MessageFormat.format(translatedText, checkinTime % 12==0? 12:checkinTime % 12 , (checkOutTime % 12 == 0 ? 12 : checkOutTime % 12));
            } else if (startMeridien.equals(AM) && endMeridien.equals(PM)) {
                translatedText = polyglotService.getTranslatedData(TIMESLOT_AM_PM);
                translatedText = MessageFormat.format(translatedText, checkinTime % 12 == 0? 12:checkinTime % 12, (checkOutTime % 12 == 0 ? 12 : checkOutTime % 12));
            } else if (startMeridien.equals(PM) && endMeridien.equals(AM)) {
                translatedText = polyglotService.getTranslatedData(TIMESLOT_PM_AM);
                translatedText = MessageFormat.format(translatedText, checkinTime % 12 == 0? 12:checkinTime % 12, (checkOutTime % 12 == 0 ? 12 : checkOutTime % 12));
            } else {
                translatedText = polyglotService.getTranslatedData(TIMESLOT_PM_PM);
                translatedText = MessageFormat.format(translatedText, checkinTime % 12 == 0? 12:checkinTime % 12, (checkOutTime % 12 == 0 ? 12 : checkOutTime % 12));
            }
        }
        return translatedText;
    }


    //Method overloading for Thankyou page
    public String calculateTimeSlot_Meridiem(Slot slot) {
        if(slot == null)
            return null;
        String startMeridien = null;
        String endMeridien = null;
        //String translatedText = null;
        StringBuilder translatedText = new StringBuilder();
        if(slot.getTimeSlot() != null && slot.getDuration() != null) {
            Integer checkOutTime = slot.getTimeSlot() + slot.getDuration();
            Integer checkinTime = slot.getTimeSlot();
            startMeridien = checkinTime < 12 ? AM : PM;
            endMeridien = checkOutTime < 12 ? AM : (checkOutTime >= 24 && checkOutTime % 12 >= 0) ? AM : PM;

            if (startMeridien.equals(AM) && endMeridien.equals(AM)) {
                translatedText.append(MessageFormat.format(polyglotService.getTranslatedData(TIMESLOT_AM_AM), checkinTime % 12==0 ? 12:checkinTime % 12, (checkOutTime % 12 == 0 ? 12 : checkOutTime % 12)));
            } else if (startMeridien.equals(AM) && endMeridien.equals(PM)) {
                translatedText.append(MessageFormat.format(polyglotService.getTranslatedData(TIMESLOT_AM_PM), checkinTime % 12==0 ? 12:checkinTime % 12, (checkOutTime % 12 == 0 ? 12 : checkOutTime % 12)));
            } else if (startMeridien.equals(PM) && endMeridien.equals(AM)) {
                translatedText.append(MessageFormat.format(polyglotService.getTranslatedData(TIMESLOT_PM_AM), checkinTime % 12==0 ? 12:checkinTime % 12, (checkOutTime % 12 == 0 ? 12 : checkOutTime % 12)));
            } else {
                translatedText.append(MessageFormat.format(polyglotService.getTranslatedData(TIMESLOT_PM_PM), checkinTime % 12==0 ? 12:checkinTime % 12, (checkOutTime % 12 == 0 ? 12 : checkOutTime % 12)));
            }
            translatedText.append(DAYUSE_PARENTHESES_OPEN).append(slot.getDuration()).append(DAYUSE_SPACE).append(polyglotService.getTranslatedData(STAY_TIME_HOURS)).append(DAYUSE_PARENTHESES_CLOSE);
        }
        return translatedText.toString();
    }

    public void transformLateCheckout(Map<String, DayUsePersuasion> dayUseFunnelPersuasions, List<DayUsePersuasion> dayUsePersuasionList) {
        if(MapUtils.isNotEmpty(dayUseFunnelPersuasions) && dayUseFunnelPersuasions.containsKey(CHECKOUT_MSG)){
            DayUsePersuasion dayUsePersuasion = null;
            dayUsePersuasion = dayUseFunnelPersuasions.get(CHECKOUT_MSG);
            if(StringUtils.isNotBlank(dayUsePersuasion.getText())) {
                String lateCheckoutHTML = dayUsePersuasion.getText();
                String translatedText = polyglotService.getTranslatedData(DAYUSE_LATE_CHECKOUT);
                translatedText = MessageFormat.format(lateCheckoutHTML, translatedText);
                dayUsePersuasion.setText(translatedText);
                dayUsePersuasionList.add(dayUsePersuasion);
            }
        }
    }
    public static boolean isSeoPersuasionAllowed(ListingSearchRequest searchHotelsRequest, String sectionName) {
        if (searchHotelsRequest == null || searchHotelsRequest.getRequestDetails() == null ||
                searchHotelsRequest.getRequestDetails().getTrafficSource() == null ||
                (NEARBY_HOTELS_SECTION.equalsIgnoreCase(sectionName) && Constants.TRAFFIC_SOURCE_SEO.equalsIgnoreCase(searchHotelsRequest.getRequestDetails().getTrafficSource().getSource())) ||
                !Constants.TRAFFIC_SOURCE_SEO.equalsIgnoreCase(searchHotelsRequest.getRequestDetails().getTrafficSource().getSource()) ||
                !(DOM_COUNTRY.equalsIgnoreCase(searchHotelsRequest.getRequestDetails().getSiteDomain()))|| !(("eng").equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.LANGUAGE.getStringValue())))) {
            return true;
        }
        return false;
    }

    /*
      Utility method to check if the request is from GCC funnel
    */
    public static boolean isGCC() {
        return AE.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()));
    }

    /*
      Utility method to check if the request is from Listing page
     */
    public static boolean isListingPage(ListingSearchRequest searchHotelsRequest) {
        return searchHotelsRequest!=null && searchHotelsRequest.getRequestDetails()!=null && Constants.PAGE_CONTEXT_LISTING.equalsIgnoreCase(searchHotelsRequest.getRequestDetails().getPageContext());
    }

    /* Utility method to check if showBnplCard is true/false */
    public static boolean isShowBnplCard(FeatureFlags featureFlags) {
        return null != featureFlags && featureFlags.isShowBnplCard();
    }

    // Below method will return polyglot key for groupPriceText or savingPerc based on client
    public static String getGroupPriceTextOrSavingPercKey(String key, String client) {
        StringBuilder sb = new StringBuilder(key);
        if (StringUtils.equalsIgnoreCase(client, DeviceConstant.DESKTOP.name())) {
            return sb.append(UNDERSCORE).append(DeviceConstant.DESKTOP.name()).toString();
        }
        return sb.toString();
    }

    /**
     * utility method to append pageContext request parameter to a base url
     * @param url base url
     * @param pageContext page context string to be appended
     * @return appended pageContext String to url
     */
    public static String appendPageContextToURL(String url, String pageContext) {
        if (StringUtils.isEmpty(url)) {
            LOGGER.debug("URL is empty");
            return url;
        }
        if (StringUtils.isEmpty(pageContext)) {
            LOGGER.debug("pageContext is empty");
            return url;
        }
        StringBuilder sb = new StringBuilder(url);
        sb.append("&"+Constants.PAGE_CONTEXT+"="+pageContext);
        return sb.toString();
    }

    public OTA getPreferredOtaFromExp(Map<OTA, JsonNode> reviewSummaryMap, boolean isMMTRatingExperiment) {
        OTA ota = OTA.TA; //default OTA for this is TA

        if (isMMTRatingExperiment && MapUtils.isNotEmpty(reviewSummaryMap)
                && reviewSummaryMap.get(OTA.MMT)!=null
                && reviewSummaryMap.get(OTA.MMT).get(Constants.TOTAL_RATING_COUNT)!=null
                && reviewSummaryMap.get(OTA.MMT).get(Constants.TOTAL_RATING_COUNT).intValue()>mmtRatingsCountThreshold) {
            ota = OTA.MMT;
        }
        return ota;
    }

    public boolean checkForGiBrand(String brand) {
        return StringUtils.isNotEmpty(brand) && brand.equalsIgnoreCase(Brand.GI.name());
    }

    public void updateHotelResultGI(HotelResult hotelResult, com.mmt.hotels.model.response.staticdata.HotelResult hotelResultCB,
                                    com.mmt.hotels.clientgateway.request.DeviceDetails deviceDetails) {
        if (Objects.nonNull(hotelResultCB.getGiStaticData())) {
            hotelResult.setAmenitiesGI(hotelResultCB.getGiStaticData().getAmenities());
            hotelResult.setFoodAndDining(hotelResultCB.getGiStaticData().getFoodDining());
            hotelResult.setMapImageUrl(hotelResultCB.getGiStaticData().getMapImageUrl());
            hotelResult.setCachedAt(hotelResultCB.getGiStaticData().getCachedAt());
            hotelResult.setSharingUrl(hotelResultCB.getGiStaticData().getShareUrl());
            hotelResult.setCoupleFriendlyPersuasion(hotelResultCB.getHotelDetailMetaInfo().getTopLeftPersuasion());
            hotelResult.setHotelVideos(hotelResultCB.getGiStaticData().getHotelVideos());
        }
        removeMMTFieldsFromGIResponse(hotelResult);
        alterGIAmenities(hotelResult,hotelResultCB, deviceDetails);
    }

    private void alterGIAmenities(HotelResult hotelResult, com.mmt.hotels.model.response.staticdata.HotelResult hotelResultCB, com.mmt.hotels.clientgateway.request.DeviceDetails deviceDetails) {
        prependHighlightedAmenitiesForDesktop(hotelResult, deviceDetails);
        buildPopularAmenitiesForApps(hotelResult,hotelResultCB, deviceDetails);
    }

    private void buildPopularAmenitiesForApps(HotelResult hotelResult, com.mmt.hotels.model.response.staticdata.HotelResult hotelResultCB, com.mmt.hotels.clientgateway.request.DeviceDetails deviceDetails) {
        // Popular amenities for GI is the union of highlighted and rated amenities.
        if (!DEVICE_OS_DESKTOP.equalsIgnoreCase(deviceDetails.getBookingDevice())
                && null != hotelResult.getAmenitiesGI()) {
            List<Transformed> popularAmenities = new ArrayList<>();
            Map<String, Integer> indexMap = new HashMap<>();
            List<Facility> facility = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(hotelResultCB.getHighlightedAmenities()) && hotelResultCB.getHighlightedAmenities().get(0)!=null) {
                facility = hotelResultCB.getHighlightedAmenities().get(0).getFacilities();
            }
            if (CollectionUtils.isNotEmpty(hotelResult.getHighlightedAmenities())) {
                for (String highlightedAmenity : hotelResult.getHighlightedAmenities()) {
                    Transformed transformed = new Transformed();
                    transformed.setName(highlightedAmenity);
                    if(CollectionUtils.isNotEmpty(facility)) {
                        Optional<Facility> facilityGroup = facility.stream().filter(fg -> highlightedAmenity.equalsIgnoreCase(fg.getName())).findFirst();
                        if(facilityGroup.isPresent()) {
                            transformed.setSubText(buildL2Amenities(facilityGroup.get()));
                        }
                    }
                    popularAmenities.add(transformed);
                    indexMap.put(highlightedAmenity, popularAmenities.size() - 1);
                }
            }
            if (CollectionUtils.isNotEmpty(hotelResult.getAmenitiesGI().getRated())) {
                for (Transformed ratedAmenity : hotelResult.getAmenitiesGI().getRated()) {
                    // If there is a rating present for the highlighted amenity, then copy that rating data for amenity.
                    if (null != indexMap.get(ratedAmenity.getName())) {
                        popularAmenities.set(indexMap.get(ratedAmenity.getName()), ratedAmenity);
                    } else {
                        popularAmenities.add(ratedAmenity);
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(popularAmenities)) {
                hotelResult.getAmenitiesGI().setPopular(popularAmenities);
            }
        }
    }

    private void prependHighlightedAmenitiesForDesktop(HotelResult hotelResult, com.mmt.hotels.clientgateway.request.DeviceDetails deviceDetails) {
        if (DEVICE_OS_DESKTOP.equalsIgnoreCase(deviceDetails.getBookingDevice())) {
            if (null != hotelResult.getAmenitiesGI()
                    && CollectionUtils.isNotEmpty(hotelResult.getAmenitiesGI().getCategorized())
                    && CollectionUtils.isNotEmpty(hotelResult.getHighlightedAmenities())) {
                Categorized highlightedAmenitiesCategory = new Categorized();
                highlightedAmenitiesCategory.setLabel(HIGHLIGHTED_AMENITIES_TITLE);
                highlightedAmenitiesCategory.setData(hotelResult.getHighlightedAmenities());
                hotelResult.getAmenitiesGI().getCategorized().add(0, highlightedAmenitiesCategory);
            }
        }
    }

    private void removeMMTFieldsFromGIResponse(HotelResult hotelResult) {
        hotelResult.setAmenities(null);
    }

    public void updateStaticDetailResponseGI(StaticDetailResponse staticDetailResponse, HotelDetailWrapperResponse hotelDetailWrapperResponse) {
        if (hotelDetailWrapperResponse.getHotelResult() != null && hotelDetailWrapperResponse.getHotelResult().getGiStaticData() != null) {
            staticDetailResponse.setPlacesResponse(hotelDetailWrapperResponse.getHotelResult().getGiStaticData().getPlacesResponse());
        }
    }
    public boolean checkIfGoStaysProperty(StaticDetailResponse response){
        if (response!=null && response.getHotelDetails()!=null &&
                response.getHotelDetails().getCategories()!=null &&
                response.getHotelDetails().getCategories().contains(GO_STAYS_CATEGORY)) {
                response.getHotelDetails().setGoStay(true);
                return response.getHotelDetails().getCategories().contains(GO_STAYS_CATEGORY);
        }
        LOGGER.debug("GO Stay Property :: False, Checking Streaks");
        return false;
    }

    public boolean checkIfGoStaysProperty(RoomDetailsResponse response){
        if (response!=null && CollectionUtils.isNotEmpty(response.getHotelRates())&&
                response.getHotelRates().get(0).getCategories()!=null) {
            return response.getHotelRates().get(0).getCategories().contains(GO_STAYS_CATEGORY);
        }
        LOGGER.debug("GO Stay Property :: False");
        return false;
    }


    //Suppressing fields not required by client in static detail-response
    public void suppressUnnecessaryFieldsInStreaksResponseInStaticDetails(StreaksUserInfoResponse response){
        response.getPageDetails().setSrpUnfiltered(null);
        response.getPageDetails().setReview(null);
        response.getPageDetails().setThankYou(null);
        response.getPageDetails().setGostayFilteredSrp(null);
        response.getPageDetails().setPostSales(null);
        response.getPageDetails().setHotelLanding(null);
    }

    /***
     * This function is used to add each SleepingInfoArrangement Data(bedRoom,bathRoom,guest,maxCapacity,bed,spaceType,bedInfos,subText,descriptionText,openCardText) from HES to CG Space in SleepingInfoArrangement
     * @param roomDetails
     * @param spaceIdToSleepingInfoArr
     */
    public void addSleepingInfoArrangementIntoRoomDetails(List<RoomDetails> roomDetails, LinkedHashMap<String, List<SleepingInfoArrangement>> spaceIdToSleepingInfoArr) {
        if(CollectionUtils.isEmpty(roomDetails) || MapUtils.isEmpty(spaceIdToSleepingInfoArr))
            return;
        for (RoomDetails roomDetail : roomDetails) {
            if (roomDetail.getPrivateSpaces() == null) {
                continue;
            }
            for (Space space : roomDetail.getPrivateSpaces().getSpaces()) {
                if (spaceIdToSleepingInfoArr.containsKey(space.getSpaceId())) {
                    for (SleepingInfoArrangement sleepingInfoArrangement : spaceIdToSleepingInfoArr.get(space.getSpaceId())) {
                        sleepingInfoArrangement.setDescriptionText((StringUtils.isNotBlank(sleepingInfoArrangement.getDescriptionText()) ? sleepingInfoArrangement.getDescriptionText() : EMPTY_STRING) +
                                ((StringUtils.isNotBlank(sleepingInfoArrangement.getDescriptionText()) && StringUtils.isNotBlank(space.getDescriptionText())) ? COMMA_SPACE + space.getDescriptionText() : (StringUtils.isNotBlank(space.getDescriptionText())?space.getDescriptionText():EMPTY_STRING)));
                        sleepingInfoArrangement.setOpenCardText((StringUtils.isNotBlank(sleepingInfoArrangement.getOpenCardText()) ? sleepingInfoArrangement.getOpenCardText() : EMPTY_STRING) +
                                ((StringUtils.isNotBlank(sleepingInfoArrangement.getOpenCardText()) && StringUtils.isNotBlank(space.getOpenCardText())) ? (StringUtils.isBlank(space.getDescriptionText())?SPACE:COMMA_SPACE) + space.getOpenCardText() : (StringUtils.isNotBlank(space.getOpenCardText())?space.getOpenCardText():EMPTY_STRING)));
                    }
                    if (CollectionUtils.isNotEmpty(spaceIdToSleepingInfoArr.get(space.getSpaceId()))) {
                        space.setDescriptionText(spaceIdToSleepingInfoArr.get(space.getSpaceId()).get(0).getDescriptionText());
                        space.setOpenCardText(spaceIdToSleepingInfoArr.get(space.getSpaceId()).get(0).getOpenCardText());
                    }
                    space.setSleepingInfoArrangement(spaceIdToSleepingInfoArr.get(space.getSpaceId()));
                }
            }
            roomDetail.setBedInfoText(buildBedInfoText(roomDetail.getPrivateSpaces().getSpaces()));
        }
    }

    public void addSleepingInfoArrangementIntoOccupancyRooms(List<RoomDetails> roomDetails) {
        if(CollectionUtils.isEmpty(roomDetails))
            return;
        for (RoomDetails roomDetail : roomDetails) {
            if (roomDetail.getPrivateSpaces() == null || CollectionUtils.isEmpty(roomDetail.getPrivateSpaces().getSpaces())
            || CollectionUtils.isEmpty(roomDetail.getBeds())) {
                continue;
            }
            Space privateSpace = roomDetail.getPrivateSpaces().getSpaces().get(0);
            SleepingArrangement sleepingArrangement = roomDetail.getBeds().get(0);
            privateSpace.setDescriptionText((sleepingArrangement.getType()!=null ? (sleepingArrangement.getCount() + SPACE + sleepingArrangement.getType() + (sleepingArrangement.getCount()>1 ? PLURAL_STRING : EMPTY_STRING)) : EMPTY_STRING) +
                    ((StringUtils.isNotBlank(privateSpace.getDescriptionText())) ? COMMA_SPACE + privateSpace.getDescriptionText() : EMPTY_STRING));
            roomDetail.setBedInfoText(buildBedInfoText(roomDetail.getPrivateSpaces().getSpaces()));
        }
    }

    public String buildBedInfoText(List<Space> spaces) {
        if (CollectionUtils.isEmpty(spaces))
            return null;
        LinkedHashMap<String, Integer> bedInfoMap = new LinkedHashMap<>();
        for (Space space : spaces) {
            if (BEDROOM.equalsIgnoreCase(space.getSpaceType()) || LIVING_ROOM.equalsIgnoreCase(space.getSpaceType())) {
                if (CollectionUtils.isNotEmpty(space.getSleepingInfoArrangement())) {
                    for (SleepingInfoArrangement sleepingInfoArrangement : space.getSleepingInfoArrangement()) {
                        if (MapUtils.isNotEmpty(sleepingInfoArrangement.getBedInfos())) {
                            for (String bedType : sleepingInfoArrangement.getBedInfos().keySet()) {
                                if (!bedInfoMap.containsKey(bedType))
                                    bedInfoMap.put(bedType, 0);
                                bedInfoMap.put(bedType, bedInfoMap.get(bedType) + sleepingInfoArrangement.getBedInfos().get(bedType));
                            }
                        }
                    }
                }
            }
        }

        return createBedInfoTextFromBedInfoMap(bedInfoMap);
    }

    public String createBedInfoTextFromBedInfoMap(LinkedHashMap<String, Integer> bedInfoMap) {
        if (MapUtils.isEmpty(bedInfoMap))
            return null;

        StringBuilder bedInfoText = new StringBuilder(EMPTY_STRING);
        int commaCountRequired = bedInfoMap.size() - 1;
        HashSet<String> visitedBedType = new HashSet<>();
        for (String bedType : bedTypePriorityOrder) {
            if (StringUtils.isNotBlank(bedType) && bedInfoMap.containsKey(bedType)) {
                visitedBedType.add(bedType);
                Pair<String,Integer> p = evaluateBedInfoText(bedInfoMap,bedType,commaCountRequired);
                bedInfoText.append(p.getKey());
                commaCountRequired = p.getValue();
            }
        }

        // Now Add those that are remaining Which are Not matched with the bedTypePriorityOrder List
        for (String bedType : bedInfoMap.keySet()) {
            if(visitedBedType.contains(bedType))
                continue;
            Pair<String,Integer> p = evaluateBedInfoText(bedInfoMap,bedType,commaCountRequired);
            bedInfoText.append(p.getKey());
            commaCountRequired = p.getValue();
        }

        return bedInfoText.toString();
    }

    private Pair<String, Integer> evaluateBedInfoText(LinkedHashMap<String, Integer> bedInfoMap, String bedType, int commaCountRequired){
        String bedInfoText=EMPTY_STRING;
        int bedTypeCount = (bedInfoMap.get(bedType) != null ? bedInfoMap.get(bedType) : 0);
        if (bedTypeCount == 0){
            commaCountRequired--;
        }
        else {
            if (bedTypeCount > 1) {
                if (bedType.endsWith("s"))
                    bedInfoText = bedTypeCount + SPACE + bedType + "es";
                else
                    bedInfoText = bedTypeCount + SPACE + bedType + "s";
            } else
                bedInfoText = bedTypeCount + SPACE + bedType;

            if (commaCountRequired > 0) {
                bedInfoText += COMMA_SPACE;
                commaCountRequired--;
            }
        }
        return new ImmutablePair<>(bedInfoText,commaCountRequired);

    }

    //Suppressing fields not required by client in avail-rooms
    public StreaksUserInfoResponse fetchReviewDetailsFromStreaksResp(StreaksUserInfoResponse response){
        PageDetails pageDetailsReview = new PageDetails();
        pageDetailsReview.setReview(response.getPageDetails().getReview());
        response.setPageDetails(pageDetailsReview);
        return response;

    }

    public void fetchThankYouDetailsFromStreaksResp(StreaksUserInfoResponse response){
        PageDetails pageDetailsThankyou = new PageDetails();
        pageDetailsThankyou.setThankYou(response.getPageDetails().getThankYou());
        response.setPageDetails(pageDetailsThankyou);
        response.setInProgressBookings(response.getInProgressBookings()+1);
    }

    public boolean isExpPdoPrnt(Map<String, String> expData) {
        return MapUtils.isNotEmpty(expData) && expData.containsKey("PDO") && org.codehaus.plexus.util.StringUtils.equalsIgnoreCase(expData.get("PDO"), "PRNT");
    }

    public StreaksUserInfoResponse getUserInfoResponse(String uuid, Map<String, String> expDataMap, String correlationKey, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap) throws ClientGatewayException {
        LOGGER.debug("GO Stay Property :: True, Checking Streaks");
        uuid = StringUtils.isNotEmpty(uuid) ? uuid : Constants.LOGGED_OUT_UUID;
        String streaksHermesExp = expDataMap.get(Constants.STREAK_EXP_NAME);
        LOGGER.debug("Streak Hermes Exp :: {}", streaksHermesExp);

        if (Constants.STREAK_EXP_VALUE.equalsIgnoreCase(streaksHermesExp)) {
            StreaksUserInfoResponse streaksUserInfoResponse = streaksExecutor.getStreaksUserInfo(parameterMap, httpHeaderMap, correlationKey, uuid);
            //Setting User Streak Info
            if (streaksUserInfoResponse != null && streaksUserInfoResponse.getResponseErrors() == null) {
                return streaksUserInfoResponse;
            }
        }
        return null;
    }

    public boolean checkIfGoStaysProperty(PersistedMultiRoomData persistedData) {
        if (persistedData!=null && CollectionUtils.isNotEmpty(persistedData.getHotelList()) &&
                persistedData.getHotelList().get(0) != null && persistedData.getHotelList().get(0).getHotelInfo() != null &&
                persistedData.getHotelList().get(0).getHotelInfo().getCategories()!=null) {
            return persistedData.getHotelList().get(0).getHotelInfo().getCategories().contains(GO_STAYS_CATEGORY);
        }
        LOGGER.debug("GO Stay Property :: False");
        return false;
    }

    public String getPaxStringFromRoomStay(List<com.mmt.hotels.clientgateway.request.RoomStayCandidate> roomStayCandidates) {
        int totalAdults = getTotalAdultsFromRequest(roomStayCandidates);
        int totalRooms = roomStayCandidates.size();
        int totalChild = getTotalChildrenFromRequest(roomStayCandidates);
        String paxString = String.format("%s-%s-%s", totalRooms, totalAdults, totalChild);
        if (totalChild > 0) {
            List<String> childAgeStringArray = new ArrayList<>();
            roomStayCandidates.forEach(roomStayCandidate -> {
                if (CollectionUtils.isNotEmpty(roomStayCandidate.getChildAges())) {
                    String childAge = roomStayCandidate.getChildAges().stream()
                            .map(Object::toString)
                            .collect(Collectors.joining("_"));
                    if (!childAge.trim().isEmpty()) {
                        childAgeStringArray.add(childAge);
                    }
                }
            });
            paxString = String.format("%s-%s", paxString, String.join("_", childAgeStringArray));
        }
        return paxString;
    }

    public boolean isIHFunnel(String countryCode, String siteDomain) {
        return countryCode != null && !countryCode.equalsIgnoreCase(DOM_COUNTRY) && !Constants.AE.equalsIgnoreCase(siteDomain);
    }
    public boolean isGroupBooking(String paxString, Map<String, String> expDataMap, String countryCode) {
        if (MapUtils.isNotEmpty(expDataMap) && expDataMap.containsKey(Constants.GROUP_RATES_EXP) && Integer.valueOf(expDataMap.get(Constants.GROUP_RATES_EXP)) == 1 && Constants.DOM_COUNTRY_CODE.equalsIgnoreCase(countryCode)) {
            String[] splitPax = paxString.split("-");
            int rooms = Integer.parseInt(splitPax[0]);
            if (rooms >= ROOM_COUNT_GROUPRATE) {
                return true;
            }
        }
        return false;
    }

    public static String getTextBasedUponCurrency(String priceText, String currencySymbol, Double amount) {
        if (StringUtils.isNotEmpty(priceText)) {
            priceText = priceText.replace(Constants.CURRENCY_SYMBOL, currencySymbol);
            priceText = priceText.replace(Constants.AMOUNT, amount.toString());
            return priceText;
        }
        return Constants.EMPTY_STRING;
    }

    /***
     * @param requestDetails
     * @return
     */
    public RequestIdentifier buildRequestIdentifier(RequestDetails requestDetails) {
        if(requestDetails==null){
            return null;
        }
        RequestIdentifier requestIdentifier = new RequestIdentifier();
        requestIdentifier.setJourneyId(requestDetails.getJourneyId());
        requestIdentifier.setRequestId(requestDetails.getRequestId());
        requestIdentifier.setSessionId(requestDetails.getSessionId());

        return requestIdentifier;
    }

    public String replaceText(String regex, String inputString, String replaceString) {
        if (StringUtils.isEmpty(inputString) || inputString.length() > 10000) // length guard
            return inputString;
        try {
            Pattern pattern = Pattern.compile(regex);
            Matcher matcher = pattern.matcher(inputString);
            return matcher.replaceAll(replaceString);
        } catch (StackOverflowError | IllegalArgumentException e) {
            return inputString; // fallback in case of regex issues
        }
    }

    // Check for international countryCode
    public boolean isInternationalProperty(com.mmt.hotels.model.response.staticdata.HotelResult hotelResultCB, StaticDetailCriteria staticDetailCriteria) {
        return (hotelResultCB != null && hotelResultCB.getCountryCode() != null && !StringUtils.equalsIgnoreCase(DOM_COUNTRY_CODE, hotelResultCB.getCountryCode()))
                || (staticDetailCriteria != null && staticDetailCriteria.getCountryCode() != null && !StringUtils.equalsIgnoreCase(DOM_COUNTRY_CODE, staticDetailCriteria.getCountryCode()));
    }

    public BookedInclusion prepareBookedInclusionFromInclusion(Inclusion inclusion) {
        BookedInclusion bookedInclusion = new BookedInclusion();
        bookedInclusion.setCode(inclusion.getCode());
        bookedInclusion.setText(inclusion.getValue());
        bookedInclusion.setIconType(IconType.DEFAULT);
        bookedInclusion.setIconUrl(inclusion.getIconUrl());
        return bookedInclusion;
    }

    //Review page
    public void restructureBlackInclusions(com.mmt.hotels.clientgateway.response.rooms.RatePlan ratePlan, BlackInfo blackInfo, boolean isGoTribe3ExpEnabled) {
        if (ratePlan != null && CollectionUtils.isNotEmpty(ratePlan.getInclusionsList()) && blackInfo != null) {
            List<BookedInclusion> inclusions = new LinkedList<>();
            List<BookedInclusion> goTribeInclusions = new LinkedList<>();
            ratePlan.getInclusionsList().forEach(inclusion -> {
                if (StringUtils.equalsIgnoreCase(BLACK_SEGMENT_IDENTIFIER, inclusion.getSegmentIdentifier()))
                    goTribeInclusions.add(inclusion);
                else inclusions.add(inclusion);
            });
            ratePlan.setInclusionsList(inclusions);
            if (CollectionUtils.isNotEmpty(goTribeInclusions)) {
                GoTribeInclusion goTribeInclusion = new GoTribeInclusion();
                goTribeInclusion.setBenefitsList(reorderBlackInclusions(goTribeInclusions));
                ratePlan.setGoTribeInclusion(goTribeInclusion);
                // get respective card here from black info and assign here.
                if(blackInfo.getIsNewLoyaltyProgramForGI()) { // New loyalty program.
                    if(isGoTribe3ExpEnabled) {
                        goTribeInclusion.setGoTribeIconUrlV2(blackInfo.getGoTribeMessageCard().getData().getStyle().getGoTribeIcon());
                        goTribeInclusion.setLineBgColor(blackInfo.getGoTribeMessageCard().getData().getStyle().getLineBgColour());
                        goTribeInclusion.setBgColorCodes(blackInfo.getGoTribeMessageCard().getData().getStyle().getGoTribeBannerBgColour());
                        goTribeInclusions.forEach(benefit -> benefit.setIconUrl(blackInfo.getTickIconUrl()));
                    } else { // New loyalty - Old APP = pick from existing pms. production
                        goTribeInclusion.setBenefitImageUrl(blackInfo.getBenefitImageUrl());
                        goTribeInclusions.forEach(benefit -> benefit.setIconUrl(blackInfo.getBenefitImageUrl()));
                        goTribeInclusion.setText(polyglotService.getTranslatedData(GOTRIBE_BENEFITS_TITLE_REVIEW));
                        goTribeInclusion.setSubText(polyglotService.getTranslatedData(GOTRIBE_BENEFITS_SUB_TITLE_REVIEW));
                        goTribeInclusion.setGoTribeIconUrl(blackInfo.getTierHeaderUrl());
                    }
                } else { // Old loyalty program.
                    if(isGoTribe3ExpEnabled) {
                        goTribeInclusion.setGoTribeIconUrlV2(blackInfo.getGoTribeIconUrlV2());
                        goTribeInclusion.setLineBgColor(blackInfo.getLineBgColor());
                        goTribeInclusion.setBgColorCodes(blackInfo.getReviewInclusionsBgColorCodes());
                        goTribeInclusions.forEach(benefit -> benefit.setIconUrl(blackInfo.getTickIconUrl()));
                    } else {
                        goTribeInclusion.setGoTribeIconUrl(blackInfo.getTierHeaderUrl());
                        goTribeInclusion.setBenefitImageUrl(blackInfo.getBenefitImageUrl());
                        goTribeInclusions.forEach(benefit -> benefit.setIconUrl(blackInfo.getBenefitImageUrl()));
                        goTribeInclusion.setText(polyglotService.getTranslatedData(GOTRIBE_BENEFITS_TITLE_REVIEW));
                        goTribeInclusion.setSubText(polyglotService.getTranslatedData(GOTRIBE_BENEFITS_SUB_TITLE_REVIEW));
                    }
                }
            }
        }
    }

    //Detail Page
    public void restructureBlackInclusions(SelectRoomRatePlan ratePlan, BlackInfo blackInfo, BlackBenefits blackBenefits, boolean isGoTribe3ExpEnabled) {
        if (ratePlan != null && CollectionUtils.isNotEmpty(ratePlan.getInclusionsList()) && blackInfo != null) {
            List<BookedInclusion> inclusions = new LinkedList<>();
            List<BookedInclusion> goTribeInclusions = new LinkedList<>();
            ratePlan.getInclusionsList().forEach(inclusion -> {
                if (StringUtils.equalsIgnoreCase(BLACK_SEGMENT_IDENTIFIER, inclusion.getSegmentIdentifier()))
                    goTribeInclusions.add(inclusion);
                else inclusions.add(inclusion);
            });
            ratePlan.setInclusionsList(inclusions);
            if (CollectionUtils.isNotEmpty(goTribeInclusions)) {
                String benefitImageUrl = blackInfo.getBenefitImageUrl();
                String benefitIconUrl = (isGoTribe3ExpEnabled) ? blackInfo.getTickIconUrl() : blackInfo.getBenefitImageUrl();
                GoTribeInclusion goTribeInclusion = new GoTribeInclusion();
                goTribeInclusion.setBenefitsList(reorderBlackInclusions(goTribeInclusions));
                if (CollectionUtils.isNotEmpty(goTribeInclusion.getBenefitsList())) {
                    StringBuilder allInclusionsText = new StringBuilder();
                    goTribeInclusion.getBenefitsList().forEach(benefit -> {
                        if (StringUtils.isEmpty(allInclusionsText)) allInclusionsText.append(benefit.getText());
                        else allInclusionsText.append(", ").append(benefit.getText());
                    });
                    goTribeInclusion.setText(allInclusionsText.toString());
                    goTribeInclusion.getBenefitsList().forEach(benefit -> benefit.setIconUrl(benefitImageUrl));
                }
                goTribeInclusion.setGoTribeIconUrl(blackInfo.getTierHeaderUrl());
                if(isGoTribe3ExpEnabled) { // Setting these fields only for new app.
                    goTribeInclusion.setGoTribeIconUrlV2(blackInfo.getGotribeInclusionsIconUrl());
                    goTribeInclusion.setCtaGoTribeIconUrlV2(blackInfo.getCtaGoTribeIconUrlV2());
                    if (CollectionUtils.isNotEmpty(goTribeInclusion.getBenefitsList())) {
                        goTribeInclusion.getBenefitsList().forEach(benefit -> benefit.setIUrl(benefitIconUrl));
                    }
                    goTribeInclusion.setCtaText(polyglotService.getTranslatedData(GOTRIBE_OK_GOT_IT_CTA_TEXT));
                }
                goTribeInclusion.setBenefitImageUrl(benefitImageUrl);
                if(!isGoTribe3ExpEnabled) {
                    goTribeInclusion.setIconUrl(benefitImageUrl);
                    goTribeInclusion.setCtaHeading(polyglotService.getTranslatedData(GOTRIBE_BENEFITS_TITLE_CTA_DETAIL));
                }
                goTribeInclusion.setCtaGoTribeIconUrl(blackInfo.getCtaTierHeaderUrl());

                if (blackBenefits != null && (blackBenefits.isRoomUpgrade() || blackBenefits.isMealUpgrade())) {
                    goTribeInclusion.setRoomOrMealUpgradeAvailable(true);
                    goTribeInclusion.setUpgradeType(getUpgradeType(blackBenefits));
                }

                ratePlan.setGoTribeInclusion(goTribeInclusion);
            }
        }

    }

    public String getUpgradeType(BlackBenefits blackBenefits) {
        if (blackBenefits != null) {
            if (blackBenefits.isRoomUpgrade() && blackBenefits.isMealUpgrade()) {
                return UpgradeType.ROOM_AND_MEAL.name();
            } else if (blackBenefits.isRoomUpgrade()) {
                return UpgradeType.ROOM.name();
            } else if (blackBenefits.isMealUpgrade()) {
                return UpgradeType.MEAL.name();
            }
        }
        return null;
    }

    public boolean checkIfFilterValueExistsInAppliedFilterMap(List<com.mmt.hotels.clientgateway.request.Filter> filterCriteria) {
        if (CollectionUtils.isNotEmpty(filterCriteria)) {
            for (com.mmt.hotels.clientgateway.request.Filter filterCG : filterCriteria) {
                if(filterCG.getFilterGroup() != null) {
                    if (EXACT_ROOM_RECOMMENDATION.equalsIgnoreCase(filterCG.getFilterGroup().name())) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    public static boolean isBookingDeviceDesktop(com.mmt.hotels.clientgateway.request.DeviceDetails deviceDetails) {
        return deviceDetails != null && Constants.DEVICE_OS_DESKTOP.equalsIgnoreCase(deviceDetails.getBookingDevice());
    }

    public static boolean isFilterAppliedRequest(List<com.mmt.hotels.clientgateway.request.Filter> filterCriteria, com.mmt.hotels.model.request.matchmaker.MatchMakerRequest matchMakerReq) {
        if(CollectionUtils.isEmpty(filterCriteria) && (matchMakerReq == null || (CollectionUtils.isEmpty(matchMakerReq.getSelectedTags()) && CollectionUtils.isEmpty(matchMakerReq.getLatLng())))) {
            return false;
        }
        return true;
    }

    public boolean isB2CFunnel() {
        return B2C.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.IDCONTEXT.getStringValue()));
    }

    public static String buildPagemakerRenderedTemplateUrl(String pageMakerRenderedTemplateUrl, String flavour, String version) throws UnsupportedEncodingException {
        JSONArray jsonArray = new JSONArray(new String[]{"calendar_json", "day_use_banner"});
        String templateIds = jsonArray.toString();
        return new StringBuilder(pageMakerRenderedTemplateUrl)
                .append(QUESTION).append(TEMPLATE_IDS).append(EQUI).append(URLEncoder.encode(templateIds, "UTF-8"))
                .append(AMP).append(FLAVOUR).append(EQUI).append(URLEncoder.encode(flavour, "UTF-8"))
                .append(AMP).append(VERSION_CODE).append(EQUI).append(URLEncoder.encode(version, "UTF-8"))
                .toString();
    }

    public static String buildPageMakerChundDataUrl(String url, String vertical, String flavour, String version) throws UnsupportedEncodingException {
        String chunk = PAGEMAKER_CHUNK;
        String mode = APP; //for dt/pwa app is coming
        if(!FUNNEL_SOURCE_DAYUSE.equalsIgnoreCase(vertical) && !FUNNEL_SOURCE_HOTELS.equalsIgnoreCase(vertical)){
            vertical = VERTICAL_HOSTEL;
        }
        return new StringBuilder(url)
                .append(QUESTION).append(CHUNK).append(EQUI)
                .append(URLEncoder.encode(chunk, "UTF-8"))
                .append(AMP).append(VERTICAL_KEYWORD).append(EQUI)
                .append(URLEncoder.encode(vertical.toLowerCase(), "UTF-8"))
                .append(AMP).append(FLAVOUR).append(EQUI)
                .append(URLEncoder.encode(flavour, "UTF-8"))
                .append(AMP).append(MODE).append(EQUI)
                .append(URLEncoder.encode(mode, "UTF-8"))
                .append(AMP).append(VERSION_CODE).append(EQUI)
                .append(URLEncoder.encode(version, "UTF-8"))
                .toString();
    }


    public static String buildSmartEngageUrl(String smartEngageUrl,String flavour, Map<String, Object> paramMap, String version, String deviceType, String siteDomain, String pageContext) throws UnsupportedEncodingException {
        String context = StringUtils.EMPTY;
        String[] slots = null;
        String page = StringUtils.EMPTY;
        if(PAGE_CONTEXT_LISTING.equalsIgnoreCase(pageContext)){
            slots = new String[]{"day_use_apps","app_safety_header","app_test","hotel_srp_slot0","hotels_srp_app","srp_gocash_rewards","srp_bpg"};
            page = SRP;
            context = SMARTENGAGE_CONTEXT_LISTING;
        } else if(PAGE_CONTEXT_LANDING.equalsIgnoreCase(pageContext)){
            if (DEVICE_OS_DESKTOP.equalsIgnoreCase(deviceType)) {
                slots = new String[]{"dwebHotels_info_home", "dwebHotels_banner_home"};
            } else {
                slots = new String[]{"app_safety_card", "app_prod_context"};
            }
            context = SMARTENGAGE_CONTEXT_LANDING;
        }
        String lob;
        if (DEFAULT_SITE_DOMAIN.equalsIgnoreCase(siteDomain)) {
            lob = DH;
        } else {
            lob = IH;
        }
        StringBuilder urlBuilder = new StringBuilder(smartEngageUrl);
        urlBuilder.append(QUESTION).append(FLAVOUR).append(EQUI).append(URLEncoder.encode(flavour, "UTF-8"));
        urlBuilder.append(AMP).append(CONTEXT).append(EQUI).append(URLEncoder.encode(context, "UTF-8"));
        urlBuilder.append(AMP).append(STATUS).append(EQUI).append(LIVE);
        urlBuilder.append(AMP).append(LOB_KEYWORD).append(EQUI).append(URLEncoder.encode(lob, "UTF-8"));
        urlBuilder.append(AMP).append(SLOTS).append(EQUI).append(URLEncoder.encode(new JSONArray(slots).toString(), "UTF-8"));
        urlBuilder.append(AMP).append(PARAMS).append(EQUI).append(URLEncoder.encode(new JSONObject(paramMap).toString(), "UTF-8"));
        urlBuilder.append(AMP).append(VERSION_CODE).append(EQUI).append(URLEncoder.encode(version, "UTF-8"));
        if(PAGE_CONTEXT_LISTING.equalsIgnoreCase(pageContext)){
            urlBuilder.append(AMP).append(PAGE).append(EQUI).append(URLEncoder.encode(page, "UTF-8"));
        }
        return urlBuilder.toString();
    }

    public com.mmt.hotels.clientgateway.response.FullPayment buildFullPayment(FullPayment fullPayment, double bnplConvFees, double tcsAmount) {
        com.mmt.hotels.clientgateway.response.FullPayment fullPaymentCg = new com.mmt.hotels.clientgateway.response.FullPayment();
        int finalPrice = (int) Math.round(fullPayment.getFinalPrice() + tcsAmount);
        int bnplConvFeesPrice = (int) Math.round(bnplConvFees);
        fullPaymentCg.setPayEntireBnplApplicableText(polyglotService.getTranslatedData(PAY_ENTIRE_BNPL_APPLICABLE_TEXT)
                .replace("{0}",String.valueOf(finalPrice)).replace("{1}", String.valueOf(bnplConvFeesPrice)));
        fullPaymentCg.setPayEntireBnplNotApplicableText(polyglotService.getTranslatedData(PAY_ENTIRE_BNPL_NOT_APPLICABLE_TEXT)
                .replace("{0}",String.valueOf(finalPrice)));
        fullPaymentCg.setBnplConvFees(bnplConvFeesPrice);
        return fullPaymentCg;
    }

    public String getPriceSuffix(boolean isAltAcco,int totalRoomCount, ListingSearchRequest searchHotelsRequest) {

        if (searchHotelsRequest != null && MapUtils.isNotEmpty(searchHotelsRequest.getExpDataMap()) && searchHotelsRequest.getExpDataMap().containsKey("PDO") && searchHotelsRequest.getExpDataMap().get("PDO").equalsIgnoreCase("PPPN")) {
            return PPPN_PRICE_SUFFIX;
        }

        int totalGuestCount = 0;
        if(searchHotelsRequest != null && searchHotelsRequest.getSearchCriteria() != null &&
                searchHotelsRequest.getSearchCriteria().getRoomStayCandidates() != null){
            int totalAdults = getTotalAdultsFromRequest(searchHotelsRequest.getSearchCriteria().getRoomStayCandidates());
            int totalChild = getTotalChildrenFromRequest(searchHotelsRequest.getSearchCriteria().getRoomStayCandidates());
            totalGuestCount = totalAdults+totalChild;
        }

        if(isAltAcco || (totalGuestCount <= 2 && totalRoomCount == 1)){
            return PER_NIGHT_SUFFIX;
        }

        if(totalRoomCount == 1){
            return FOR + SINGLE_ROOM_PREFIX + PER_NIGHT_SUFFIX ;
        }
        if (totalRoomCount>1)
            return FOR + totalRoomCount + MULTI_ROOM_PREFIX + PER_NIGHT_SUFFIX;

        return PER_NIGHT_SUFFIX;
    }

    public boolean isBusinessIdentificationEnableFromUserService(ExtendedUser extendedUser) {
        if (extendedUser != null && CollectionUtils.isNotEmpty(extendedUser.getAltVerifiedDetails())) {
            AltVerifiedDetails altVerifiedDetails = extendedUser.getAltVerifiedDetails().get(0);
            if (altVerifiedDetails != null) {
                return B2B_EMAIL_VERIFY_GI.equalsIgnoreCase(altVerifiedDetails.getProgramName())
                        && altVerifiedDetails.getTtl() > Instant.now().toEpochMilli();
            }
        }
        return false;
    }

    /*
     This method checks if the business identification feature is applicable or not.
     It checks if the affiliate ID present in the list of businessIdentificationAffiliates.
     If the affiliate ID is present and the business identification feature is enabled from the user service, it returns true.
     Otherwise, it returns false.
     */
    public boolean isBusinessIdentificationApplicable(String affiliateId, Set<String> allowedSegments) {
        return allowedSegments != null && StringUtils.isNotEmpty(affiliateId) && !allowedSegments.isEmpty()
                && businessIdentificationAffiliates != null && businessIdentificationSegments != null
                && !businessIdentificationAffiliates.isEmpty() && !businessIdentificationSegments.isEmpty()
                && businessIdentificationAffiliates.contains(affiliateId) && checkSegments(allowedSegments, businessIdentificationSegments);
    }

    private boolean checkSegments(Set<String> allowedSegments, Set<String> businessIdentificationSegments) {
        if(allowedSegments == null || businessIdentificationSegments == null || allowedSegments.isEmpty() || businessIdentificationSegments.isEmpty())
            return false;
        // Iterate over each string in allowedSegments
        for (String segment : allowedSegments) {
            // Check if the segment is present in businessIdentificationSegments
            if (businessIdentificationSegments.contains(segment)) {
                return true;
            }
        }
        // If no segment from allowedSegments is found in businessIdentificationSegments, return false
        return false;
    }

    public BookedInclusion mergeLOSInclusions(List<BookedInclusion> losInclusions) {
        if (CollectionUtils.isEmpty(losInclusions)) {
            return null;
        }
        BookedInclusion losInclusion = losInclusions.get(0);
        losInclusion.setInclusionsDetails(buildBenefitInclusionDetails(losInclusions));
        /*
            While merging LOS inclusions, we have to make a single text and subtext out of all the los inclusions.
            The logic is as follows:
              1. If there is only one LOS inclusion, then the text is "{inclusion text} with Long stay benefits"
              2. If there are 2 LOS inclusions, then the text is "{inclusion text 1}, {inclusion text 2} with Long stay benefits"
              3. If there are more than 2 LOS inclusions, then the text is "{inclusion text 1}, {inclusion text 2} and more with Long stay benefits".
         */
        List<String> inclusionsTextList = losInclusions.stream()
                .map(BookedInclusion::getText)
                .filter(text -> StringUtils.isNotEmpty(text) && !NULL_STRING.equalsIgnoreCase(text))
                .collect(Collectors.toList());
        List<String> inclusionsSubTextList = losInclusions.stream()
                .map(BookedInclusion::getSubText)
                .filter(text ->StringUtils.isNotEmpty(text) && !NULL_STRING.equalsIgnoreCase(text))
                .collect(Collectors.toList());
        String inclusionText = getLOSInclusionText(inclusionsTextList);
        String inclusionSubText = getLOSInclusionText(inclusionsSubTextList);
        if (null != inclusionText) {
            losInclusion.setText(inclusionText);
            losInclusion.setCode(inclusionText);
        }
        if (null != inclusionSubText) {
            losInclusion.setSubText(inclusionSubText);
        }
        losInclusion.setIconUrl(INCLUSIONS_DEFAULT_DOT_ICON_URL);
        losInclusion.setIUrl(INCLUSIONS_DEFAULT_DEFAULT_DOT_ICON_URL);
        return losInclusion;
    }

    private BenefitInclusionDetails buildBenefitInclusionDetails(List<BookedInclusion> losInclusions) {
        BenefitInclusionDetails benefitInclusionDetails = new BenefitInclusionDetails();
        benefitInclusionDetails.setTitleText(polyglotService.getTranslatedData(ConstantsTranslation.LOS_TITLE_TEXT));
        benefitInclusionDetails.setTitleTextColor(losTitleTextColor);
        List<Benefit> benefitList = losInclusions.stream()
                .map(inclusion -> {
                    Benefit benefit = new Benefit();
                    benefit.setBenefitText(inclusion.getText());
                    benefit.setIconUrl(losIconUrl);
                    return benefit;
                }).collect(Collectors.toList());
        benefitInclusionDetails.setBenefitList(benefitList);
        benefitInclusionDetails.setCtaText(polyglotService.getTranslatedData(ConstantsTranslation.OK_GOT_IT_CTA_TEXT));
        return benefitInclusionDetails;
    }

    private String getLOSInclusionText(List<String> inclusionTextList) {
        if (CollectionUtils.isNotEmpty(inclusionTextList)) {
            String inclusionText = inclusionTextList.stream()
                    .limit(2)
                    .collect(Collectors.joining(COMMA_SPACE));
            inclusionText = inclusionTextList.size() > 2 ? inclusionText + " and more" : inclusionText;
            return String.format(LOS_INCLUSION_TEXT_FORMAT, inclusionText);
        }
        return null;
    }

    public Set<SpaceData> getSpaceDataV2(Map<String, RoomInfo> altAccoRoomInfo, boolean isPrivateSpace) {
        Set<SpaceData> spaceDataList = new HashSet<>();
        for (Map.Entry<String, RoomInfo> altAccoRoom : altAccoRoomInfo.entrySet()) {
            SpaceData spaceData = getSpaceDataForRoom(altAccoRoom, isPrivateSpace);
            if (spaceData != null) {
                spaceDataList.add(spaceData);
            }
        }
        return spaceDataList;
    }

    private SpaceData getSpaceDataForRoom(Map.Entry<String, RoomInfo> altAccoRoom, boolean isPrivateSpace) {
        com.mmt.hotels.model.response.staticdata.SpaceData hesSpaceData = (altAccoRoom != null) ? (isPrivateSpace ? altAccoRoom.getValue().getPrivateSpaces() : altAccoRoom.getValue().getSharedSpaces()) : null;
        if (hesSpaceData == null)
            return null;
        SpaceData cgSpaceData = new SpaceData();
        int extraBedCount = 0, totalBaseOccupancy = 0, totalMaxOccupancy = 0;
        if (hesSpaceData != null) {
            cgSpaceData.setDescriptive(hesSpaceData.getDescriptive());
            cgSpaceData.setSharedInfo(buildSharedInfo(hesSpaceData.getSharedInfo()));
            List<Space> spaceList = new ArrayList<>();
            for(com.mmt.hotels.model.response.staticdata.Space hesSpace: hesSpaceData.getSpaces()){
                Space cgSpace = new Space();
                cgSpace.setName(hesSpace.getName());
                cgSpace.setSpaceId(hesSpace.getSpaceId());
                cgSpace.setSpaceType(hesSpace.getSpaceType());
                cgSpace.setAreaText(hesSpace.getAreaText());
                cgSpace.setSpaceInclusions(buildSpaceInclusion(hesSpace));
                if(isPrivateSpace){
//                    cgSpace.setSubText(hesSpace.getAreaText());
                    cgSpace.setAreaText(null);
                } else{
                    cgSpace.setDescriptionText(hesSpace.getDescriptionText());
                    String subText = hesSpace.getSubText();
                    if(hesSpace.getSpaceType() != null && (hesSpace.getSpaceType().equalsIgnoreCase(Constants.BEDROOM) || hesSpace.getSpaceType().equalsIgnoreCase(Constants.LIVING_ROOM))) {
                        int finalOccupancy = hesSpace.getFinalOccupancy();
                        int occupancy = max(finalOccupancy, hesSpace.getBaseOccupancy());
                        if (occupancy > 0)
                            subText = (occupancy > 1) ? polyglotService.getTranslatedData(ConstantsTranslation.SPACE_OCCUPANCY_TEXT) : polyglotService.getTranslatedData(ConstantsTranslation.SPACE_SINGLE_OCCUPANCY_TEXT);
                        else
                            subText = null;
                        if (subText != null) {
                            subText = subText.replace(OCCUPANCY_PARAMETER, String.valueOf(occupancy));
                        }
                    }
                    cgSpace.setSubText(subText);
                }
                if(CollectionUtils.isNotEmpty(hesSpace.getMedia())){
                    List<MediaData> mediaDataList = new ArrayList<>();
                    for(com.mmt.hotels.model.response.staticdata.MediaData hesMediaData: hesSpace.getMedia()){
                        MediaData cgMediaData = new MediaData();
                        cgMediaData.setMediaType(hesMediaData.getMediaType());
                        cgMediaData.setUrl(hesMediaData.getUrl());
                        mediaDataList.add(cgMediaData);
                    }
                    cgSpace.setMedia(mediaDataList);
                }
                spaceList.add(cgSpace);
            }
            cgSpaceData.setSpaces(spaceList);
        }
        cgSpaceData.setBaseGuests(totalBaseOccupancy);
        cgSpaceData.setExtraBeds(extraBedCount);
        cgSpaceData.setMaxGuests(totalMaxOccupancy);
        return cgSpaceData;
    }

    private SharedInfo buildSharedInfo(com.mmt.hotels.model.response.staticdata.SharedInfo hesSharedInfo) {
        if(hesSharedInfo == null)
            return null;
        SharedInfo sharedInfo = new SharedInfo();
        sharedInfo.setIconUrl(hesSharedInfo.getIconUrl());
        sharedInfo.setInfoText(hesSharedInfo.getInfoText());
        return  sharedInfo;
    }

    private List<String> buildSpaceInclusion(com.mmt.hotels.model.response.staticdata.Space hesSpace) {
        StringBuilder responseString = new StringBuilder();
        if (hesSpace != null) {
            if (hesSpace.getSleepingDetails() != null) {
                if (hesSpace.getSleepingDetails().getBedInfo() != null) {
                    for (com.mmt.hotels.model.response.staticdata.SleepingBedInfo bedsInfo : hesSpace.getSleepingDetails().getBedInfo()) {
                        if (StringUtils.isNotEmpty(responseString.toString())) {
                            responseString.append(SPACE).append(AMP).append(SPACE).append(bedsInfo.getBedCount()).append(SPACE).append(bedsInfo.getBedType());
                        } else {
                            responseString.append(bedsInfo.getBedCount()).append(SPACE).append(bedsInfo.getBedType());
                        }
                    }
                }
                if (hesSpace.getSleepingDetails().getExtraBedInfo() != null) {
                    for (com.mmt.hotels.model.response.staticdata.SleepingBedInfo bedsInfo : hesSpace.getSleepingDetails().getExtraBedInfo()) {
                        responseString.append(COMMA_SPACE).append(EXTRA).append(SPACE).append(bedsInfo.getBedCount()).append(SPACE).append(bedsInfo.getBedType()).append(SPACE).append(AVAILABLE.toLowerCase());
                    }
                }
            }
            if (StringUtils.isNotEmpty(responseString.toString()) && StringUtils.isNotEmpty(hesSpace.getDescriptionText())) {
                responseString.append(PIPE_SEPARATOR);
            }
            responseString.append(hesSpace.getDescriptionText());
        }
        return StringUtils.isNotEmpty(responseString.toString()) ? Arrays.asList(responseString.toString().split(PIPE_SEPARATOR_WITH_BACKSLASH)) : null;
    }

    public boolean isPreAppliedPrivateRoomApplicable(LinkedHashMap<String, String> expDataMap,
                                                     List<com.mmt.hotels.clientgateway.request.RoomStayCandidate> roomStayCandidates,
                                                     boolean isPreAppliedFilter,
                                                     List<Filter> filterCriteria,
                                                     String funnelSource) {
        return FUNNEL_SOURCE_HOSTEL.equalsIgnoreCase(funnelSource)
                && isApplicableForFilter(expDataMap, roomStayCandidates, filterCriteria, privateRoomFilter.name(), isPreAppliedFilter);
    }

    private boolean isApplicableForFilter(LinkedHashMap<String, String> expDataMap,
                                          List<com.mmt.hotels.clientgateway.request.RoomStayCandidate> roomStayCandidates,
                                          List<Filter> filterCriteria,
                                          String filterKey,
                                          boolean isPreAppliedFilter) {
        return expDataMap != null
                && MapUtils.isNotEmpty(expDataMap)
                && TRUE.equalsIgnoreCase(expDataMap.get(filterKey))
                && isValidPaxCountForPrivateRoomFilter(roomStayCandidates)
                && isPreAppliedFilter
                && !checkIfPrivateRoomExistsInAppliedFilterMap(filterCriteria);
    }

    private boolean checkIfPrivateRoomExistsInAppliedFilterMap(List<Filter> filterCriteria) {
        if (CollectionUtils.isNotEmpty(filterCriteria)) {
            for (com.mmt.hotels.clientgateway.request.Filter filterCG : filterCriteria) {
                if(filterCG.getFilterGroup() != null) {
                    if (PRIVATE_ROOM_FILTER.equalsIgnoreCase(filterCG.getFilterGroup().name())) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    private boolean isValidPaxCountForPrivateRoomFilter(List<com.mmt.hotels.clientgateway.request.RoomStayCandidate> roomStayCandidates) {
        int noOfRooms = roomStayCandidates!=null ? roomStayCandidates.size() : 0;
        int totalAdultRequested = getTotalAdultsFromRequest(roomStayCandidates);
        return totalAdultRequested == giPrivateRoomAdultCountThreshold && noOfRooms == giPrivateRoomRoomCountThreshold;
    }

    public Map<String, Filter> getPreAppliedFilters() {
        return preAppliedFilterListMap;
    }

    public String updateExpDataForPricerV2(String expData){
        try {
            if(expData!=null && !expData.isEmpty()) {
                Map<String, String> expDataMap = objectMapperUtil.getObjectFromJson(expData, Map.class, DependencyLayer.CLIENTGATEWAY);
                if(MapUtils.isNotEmpty(expDataMap) && !expDataMap.containsKey("pricerV2")) {
                    expDataMap.put("pricerV2", TRUE);
                    expData = objectMapperUtil.getJsonFromObject(expDataMap, DependencyLayer.CLIENTGATEWAY);
                }
            }
        } catch (Exception e) {
            LOGGER.error("Error while updating expData for pricer v2", e);
        }
        return expData;
    }

    public String logLuckyUserData(CommonModifierResponse commonModifierResponse, String luckyUserContext, String requestType) {
        boolean isXPercentSellOn = false;
        String uuid = null;
        Set<String> hydraSegments = null;
        if (commonModifierResponse != null) {
            isXPercentSellOn = MapUtils.isNotEmpty(commonModifierResponse.getExpDataMap()) && commonModifierResponse.getExpDataMap().containsKey(X_PERCENT_SELL_ON) && TRUE.equalsIgnoreCase(commonModifierResponse.getExpDataMap().get(X_PERCENT_SELL_ON));
            uuid = commonModifierResponse.getExtendedUser() != null ? commonModifierResponse.getExtendedUser().getUuid() : null;
            hydraSegments = commonModifierResponse.getHydraResponse() != null ? commonModifierResponse.getHydraResponse().getHydraMatchedSegment() : null;
        }
        String isXPercentSellExpText = isXPercentSellOn ? X_PERCENT_SELL_ON_TEXT : X_PERCENT_SELL_OFF_TEXT;
        String luckyUserContextWithExp = isXPercentSellExpText + (StringUtils.isNotEmpty(luckyUserContext) ? "|" + luckyUserContext : "");
        LOGGER.warn("X-Percent Tracking: xPercentSellOn={} | UUID={} | HydraSegments={} | LuckyUserContext={} | RequestType={}", isXPercentSellOn, uuid, hydraSegments, luckyUserContext, requestType);
        return luckyUserContextWithExp;
    }

    public boolean isRearchFlow(String requestId, Map<String, String> expDataMap) {
        if (StringUtils.isNotEmpty(requestId) && requestId.startsWith("CG_GI")) {
            if (requestId.contains("OrchV2")) {
                return isRearchExpEnabled(expDataMap);
            }
            return false;
        }
        return isRearchExpEnabled(expDataMap);
    }

    private boolean isRearchExpEnabled(Map<String, String> expDataMap) {
        return TRUE.equalsIgnoreCase(expDataMap.get(ExperimentKeys.EXP_ORCHESTRATOR_V2.getKey()))
                || TRUE.equalsIgnoreCase(expDataMap.get(ExperimentKeys.EXP_ORCHESTRATOR_V2_APPS.getKey())) ||
                TRUE.equalsIgnoreCase(expDataMap.get(ExperimentKeys.EXP_DAYUSE_ORCHESTRATOR_V2.getKey()))
                || "T".equalsIgnoreCase(expDataMap.get("rearch"));
    }

    public void updateAmenitiesGI(StaticDetailResponse staticDetailResponse, List<FacilityGroup> amenities) {
        Amenities amenitiesGi = staticDetailResponse.getAmenitiesGI();
        if (amenitiesGi != null && CollectionUtils.isNotEmpty(amenitiesGi.getCategorized())) {
            List<CategorizedV2> categorizedV2List = new ArrayList<>();
            for (Categorized category : amenitiesGi.getCategorized()) {
                CategorizedV2 categorizedV2 = new CategorizedV2();
                categorizedV2.setLabel(category.getLabel());
                categorizedV2.setData(buildCategorizedV2(category.getLabel(),amenities));
                categorizedV2List.add(categorizedV2);
            }
            amenitiesGi.setCategorizedV2(categorizedV2List);
            amenitiesGi.setCategorized(null);
        }
    }

    private List<Transformed> buildCategorizedV2(String label, List<FacilityGroup> amenities) {
        Optional<FacilityGroup> facilityGroup =  amenities.stream().filter(fg -> label.equalsIgnoreCase(fg.getName())).findFirst();
        if(facilityGroup.isPresent()){
            return facilityGroup.get().getFacilities().stream().map(facility -> {
                Transformed transformed = new Transformed();
                transformed.setName(facility.getName());
                transformed.setSubText(buildL2Amenities(facility));
                return transformed;
            }).collect(Collectors.toList());
        }
        return null;
    }

    private String buildL2Amenities(Facility facility) {
        String subText = null;
        if (CollectionUtils.isNotEmpty(facility.getChildAttributes()) && StringUtils.isNotEmpty(facility.getDisplayType())) {
            if ("1".equalsIgnoreCase(facility.getDisplayType())) {
                StringBuilder stringBuilder = new StringBuilder();
                for (com.mmt.model.AttributesFacility cf : facility.getChildAttributes()) {
                    stringBuilder.append(cf.getName()).append(Constants.COMMA);
                }
                subText= Constants.AMENITIES_OPEN_BRACE +
                        StringUtils.chop(stringBuilder.toString()) + Constants.AMENITIES_CLOSING_BRACE;
            }
            if ("2".equalsIgnoreCase(facility.getDisplayType())) {
                StringBuilder stringBuilder = new StringBuilder();
                com.mmt.model.AttributesFacility childAttribute = facility.getChildAttributes().get(0);
                stringBuilder.append(childAttribute.getName())
                        .append(Constants.SPACE)
                        .append(Constants.HYPEN)
                        .append(Constants.SPACE);
                if (CollectionUtils.isNotEmpty(childAttribute.getSubAttributes())) {
                    for (SubAttributeFacility subAttributeFacility : childAttribute.getSubAttributes()) {
                        stringBuilder.append(subAttributeFacility.getName()).append(Constants.COMMA);
                    }
                }
                subText = Constants.AMENITIES_OPEN_BRACE +
                        StringUtils.chop(stringBuilder.toString()) + Constants.AMENITIES_CLOSING_BRACE;
            }
        }
        return subText;
    }

    public String replaceWithFreeCancellation(String input) {
        String withFreeCancellationRegex = "(?i)with free cancellation";
        String freeCancellationRegex = "(?i)free cancellation";

        Pattern withFreeCancellationPattern = Pattern.compile(withFreeCancellationRegex);
        Pattern freeCancellationPattern = Pattern.compile(freeCancellationRegex);

        Matcher withFreeCancellationMatcher = withFreeCancellationPattern.matcher(input);
        if (withFreeCancellationMatcher.find()) {
            return withFreeCancellationMatcher.replaceAll("<s>with free cancellation</s>");
        }

        Matcher freeCancellationMatcher = freeCancellationPattern.matcher(input);
        if (freeCancellationMatcher.find()) {
            return freeCancellationMatcher.replaceAll("<s>free cancellation</s>");
        }

        return input;
    }

    public static String getExperimentValue(CommonModifierResponse commonModifierResponse, String experimentKey) {
        if (commonModifierResponse != null && commonModifierResponse.getExpDataMap() != null) {
            return commonModifierResponse.getExpDataMap().get(experimentKey);
        }
        return null;
    }
    public static String convertToUnderscoreCaps(String input) {
        // Replace spaces with underscores and convert to uppercase
        if (null == input){
            return "";
        }
        return input.trim().replace(" ", "_").toUpperCase();
    }
}
package com.mmt.hotels.clientgateway.constants;

import com.mmt.hotels.model.enums.InclusionCategory;

import java.util.*;

public class Constants {

    public static final String HORIZONTAL = "H";
    public static final String VERTICAL = "V";

    public static List<String> inclusionOrderList = Arrays.asList("ZPN", "MEAL", "GOCASH", "OTHERS", "USP");

    public static List<String> blackInclusionsOrderList = Arrays.asList(
            InclusionCategory.ROOM_UPGRADE.getLeafCategory(), InclusionCategory.MEAL_UPGRADE.getLeafCategory(),
            InclusionCategory.HOTEL_CREDIT.getLeafCategory(), InclusionCategory.FOOD_AND_BEVERAGE.getLeafCategory(),
            InclusionCategory.SPA.getLeafCategory(), InclusionCategory.MY_CASH.getLeafCategory(),
            InclusionCategory.DISCOUNT.getLeafCategory(), "OTHERS");

    public static final String SEMICOLON = ";";
    public static final String ERR_CODE_API = "2";
    public static final String CORP_ID_CONTEXT = "CORP";
    public static final String B2B_ID_CONTEXT = "B2B";
    public static final String MOB_ID_CONTEXT = "MOB";
    public static final String FILTER_COND_FUNNEL = "funnelSource";
    public static final String FILTER_HOTEL_PRICE = "PRICE";
    public static final String FILTER_HOTEL_PRICE_BUCKET = "PRICE_BUCKET";
    public static final String FILTER_ROOMS_AND_BEDS = "ROOMS_AND_BEDS";
    public static final String FILTER_PRICE_MANUAL = "HOTEL_PRICE_MANUAL";
    public static final String FILTER_SPACE = "SPACE";
    public static final String PRICE_CHECKBOX = "PRICE_CHECKBOX";
    public static final String COUNTRY = "country";
    public static final String SITE_DOMAIN = "siteDomain";
    public static final String TYPE_CITY = "city";
    public static final String FILTER_PRICE_BUCKET_HES = "HOTEL_PRICE_BUCKET";
    public static final String HOTEL_PRICE = "HOTEL_PRICE";
    public static final String FILTER_POPULAR = "POPULAR";
    public static final String FILTER_PREFERRED = "PREFERRED_RATES_OrgId_";
    public static final String PRICE_TITLE = "Price";
    public static final String PRICE_EXP = "PDO";
    public static final String PRICE_PN = "PN";
    public static final String PRICE_PNT = "PNT";
    public static final String PRICE_PRN = "PRN";
    public static final String PRICE_PRNT = "PRNT";
    public static final String PRICE_TP = "TP";
    public static final String PRICE_TPT = "TPT";
    public static final String PRICE_PPPN = "PPPN";

    public static final String N = "N";
    public static final String ANDROID = "ANDROID";
    public static final String DEVICE_OS_ANDROID = "ANDROID";

    public static final String DEVICE_IOS = "IOS";
    public static final String DEVICE_OS_IOS = "IOS";
    public static final String DEVICE_MSITE = "msite";
    public static final String DEVICE_OS_DESKTOP = "desktop";
    public static final String DEVICE_OS_PWA = "pwa";
    public static final String RECOMMENDED_HOTELS_SECTION = "RECOMMENDED_HOTELS";
    public static final String RECOMMENDED_HOTELS_HEADING = "Recommended for You";
    public static final String NEARBY_HOTELS_SECTION = "NEARBY_HOTELS";
    public static final String OTHER_ALTACCO_HOTELS_SECTION = "OTHER_ALT_ACCO_HOTELS";
    public static final String NON_ALTACCO_HOTELS_SECTION = "NON_ALT_ACCO_HOTELS";
    public static final String HEADER_AKAMAI = "X-Akamai-Edgescape";
    public static final String COMMA = ",";
    public static final String COMMA_SPACE = ", ";
    public static final String SPACE_X_SPACE = " x ";
    public static final String HEADER_COUNTRY_CODE = "country_code";
    public static final String EQUI = "=";
    public static final String EXTRA = "Extra";
    public static final String QUESTION = "?";
    public static final String AMP = "&";
    public static final String DEFAULT_DOMAIN = "DEFAULT";
    public static final String EXCLUSIVE_HOTELS = "EXCLUSIVE_HOTELS";
    public static final String REGION_SA = "SA";
    public static final String WWW_SUBDOMAIN = "https://www.";
    public static final String SA_SUBDOMAIN = "https://sa.";
    public static final String REQUESTOR_SCION = "SCION";
    public static final String TRAFFIC_SOURCE_CROSSSELL = "CROSSSELL";
    public static final String TRAFFIC_SOURCE_FLYWHEEL = "flywheel";
    public static final String FUNNEL_SOURCE_SHORTSTAYS = "SHORTSTAYS";
    public static final String DEFAULT_CUR_INR = "INR";
    public static final String DEFAULT_SITE_DOMAIN = "IN";
    public static final String DEFAULT_LANGUAGE = "eng";
    public static final String ENGLISH = "English";
    public static final String REGION_CODE = "region_code";
    public static final String LOB_DOM_FLIGHTS = "df";
    public static final String LOB_INTL_FLIGHTS = "if";
    public static final String USER_LOB_LAST_BOOKED_SC = "user_lob___last_booked_sc___NA___P1D___v1";
    public static final String DEVICE_LOB_LAST_BOOKED_SC = "device_lob___last_booked_sc___NA___P1D___v1";
    public static final String USER_LOB_LAST_BOOKED_TS = "user_lob___last_booked_ts___NA___PT30S___v1";
    public static final String DEVICE_LOB_LAST_BOOKED_TS = "device_lob___last_booked_ts___NA___PT30S___v1";
    public static final String ANDROID_MCID = "mcid";
    public static final String COMMON_MCID = "usr-mcid";
    public static final String PROFILE_TYPE_CTA = "CTA";
    public final static String AFFILIATE_ID = "AFFILIATE_ID.";
    public static final String APPLICATION_ID = "APPLICATIONID";
    public static final String DOT = ".";
    public static final Set<String> HYDRA_BLOCKED_DEVICEID_LIST = new HashSet<>(Arrays.asList("00000000-0000-0000-0000-000000000000", "Opera", "Firefox", "Safari", "Internet Explorer", "MSIE", "Chrome", "PWA", "null"));
    public static final String COSMOS = "COSMOS";
    public static final String CBINTERNAL = "CBINTERNAL";
    public static final String CHANNEL_FKPWA = "FKPWA";
    public static final String QUE_MARK = "?";
    public static final String CORRELATIONKEY = "correlationKey";
    public static final String CK_CORRELATION_KEY = "ck"; // IN this format correlation key is coming from client
    public static final String CONTEXT_TYPE = "contextType";
    public static final String ALL = "ALL";
    public static final String REQ_PARAM_TYPE = "type";
    public static final String XSS_DISALLOWED_CHARACTERS_IN_URL = "[(`;<>)'\"]"; //this is used as regex so [ and ] are allowed
    public static final String PROFILE_CORPORATE = "BUSINESS";
    public static final String LOGIN_INFO_TYPE_FK_IDNTY = "FK_IDNTY";
    //	public static final String USER_LOB_LAST_BOOKED_TS =  "user_lob::last_booked_ts::NA::PT30S::v1";
//	public static final String DEVICE_LOB_LAST_BOOKED_TS =  "device_lob::last_booked_ts::NA::PT30S::v1";
    public static final String HYDRA_SEGEMNET_SOCKET_TIME_OUT_ERROR = "HYDRA_SEGEMNET_API_SOCKET_TIME_OUT_ERROR IN {} MS";
    public static final String LOWEST_RATE_SEGMENT_KEY = "LOWEST_RATE_SEGMENT_ID";
    public static final String B2C = "B2C";
    public static final String REGION = "region";
    public static final String CURRENCY = "currency";
    public static final String LANGUAGE = "language";
    public static final String EMPTY_STRING = "";
    public static final String PLURAL_STRING = "s";
    public static final String CONTEXT_PATH = "/clientbackend-gi";
    public static final String LOGIN_TYPE_MOBILE = "MOBILE";
    public static final String CLIENT = "client";
    public static final String VERSION = "version";
    public static final String SRC_CLIENTBACKEND = "CLIENTBACKEND";
    public static final String DOM_COUNTRY = "IN";
    public static final String DOC_TYPE_PAN = "PAN";
    public static final String ID_CONTEXT = "idContext";
    public static final String WORK_FLOW_ID = "workflowId";

    public static final String TOTAL_AMOUNT_KEY = "TOTAL_AMOUNT";
    public static final String TCS_AMOUNT_KEY = "TCS_AMOUNT";
    public static final String TCS_AMOUNT_LABEL = "TCS Amount";
    public static final String AMOUNT_YOU_PAYING_NOW_KEY = "AMOUNT_YOU_PAYING_NOW_KEY";
    public static final String BASE_FARE_KEY = "BASE_FARE";
    public static final String CHARITY_KEY = "CHARITY";
    public static final String CHARITY_KEY_V2 = "CHARITY_V2";
    public static final String CHARITY_LABEL = "Donation towards charity";
    public static final String BASE_FARE_WITH_TAX_KEY = "BASE_FARE_WITH_TAX";
    public static final String TOTAL_DISCOUNT_KEY = "TOTAL_DISCOUNT";
    public static final String TOTAL_INSURANCE = "TOTAL_INSURANCE";
    public static final String MMT_DISCOUNT_KEY = "MMT_DISCOUNT";
    public static final String CDF_DISCOUNT_KEY = "CDF_DISCOUNT";

    public static final String HOTELIER_DISCOUNT_LABEL_CORP = "myBiz Special Discount";

    public static final String INSURANCE_AMOUNT = "INSURANCE_AMOUNT";
    public static final String BLACK_DISCOUNT_KEY = "BLACK_DISCOUNT";
    public static final String BLACK_DISCOUNT_LABEL = "MMTBLACK Discount";
    public static final String BLACK_SEGMENT_IDENTIFIER = "BLACK";

    public static final String WALLET_KEY = "WALLET_DISCOUNT";
    public static final String PRICE_AFTER_DISCOUNT_KEY = "PRICE_AFTER_DISCOUNT";
    public static final String PAY_NOW_KEY = "PAY_NOW";
    public static final String PAY_NOW_LABEL = "Pay Now";
    public static final String AFTER_CASHBACK_PRICE_KEY = "AFTER_CASHBACK_PRICE";
    public static final String AFTER_CASHBACK_PRICE_LABEL = "After Cashback Price";
    public static final String GOCASH_KEY = "CTW";
    public static final String TAX_CASHBACK_LABEL = "Includes taxes & fees and Cashback";
    public static final String TAXES_LABEL = "Taxes & Service fees";
    public static final String TAXES_KEY = "TAXES";
    public static final String GST_LABEL = "Hotel GST";
    public static final String HOTEL_TAX_LABEL = "Hotel Taxes";
    public static final String HOTEL_TAX_KEY = "HOTEL_TAX";
    public static final String SERVICE_FEES_LABEL = "Service Fees";
    public static final String SERVICE_FEES_KEY = "SERVICE_FEES";
    public static final String SERVICE_CHARGE_LABEL = "Service Charge";
    public static final String SERVICE_CHARGE_KEY = "SERVICE_CHARGE";
    public static final String AFFILIATE_FEES_LABEL = "Affiliate Fees";
    public static final String AFFILIATE_FEES_KEY = "AFFILIATE_FEES";
    public static final String SERVICE_FEES_REVERSAL_KEY = "SERVICE_FEES_REVERSAL";
    public static final String SERVICE_FEES_REVERSAL_LABLE = "Reversal of Service Fee";
    public static final String EFFECTIVE_COUPON_APPLIED_KEY = "EFFECTIVE_COUPON_APPLIED";
    public static final String EFFECTIVE_COUPON_APPLIED_LABLE = "Effective Coupon Applied";

    /*
    thankyou constants
     */
    public static final String AMOUNT_LABEL_MMT_WALLET = "MMT_WALLET";
    public static final String AMOUNT_LABEL_OTHER_PAYMODES = "OTHER_PAYMODES";
    public static final String AMOUNT_LABEL_TOTAL_AMOUNT = "TOTAL_AMOUNT";
    public static final String AMOUNT_LABEL_PARTIAL_AMOUNT_LEFT = "PARTIAL_AMOUNT_LEFT";
    public static final String AMOUNT_LABEL_PARTIAL_AMOUNT_PAID = "PARTIAL_AMOUNT_PAID";
    public static final String AMOUNT_LABEL_AMOUNT_CARD = "AMOUNT_CARD";
    public static final String AMOUNT_LABEL_REMAINING_AMOUNT = "REMAINING_AMOUNT";
    public static final String AMOUNT_LABEL_AMOUNT_HOTEL = "AMOUNT_HOTEL";
    public static final String AMOUNT_LABEL_APPROX_IN = "APPROX_IN";


    public static final String MEAL_PLAN_CODE_ROOM_ONLY = "EP";
    public static final String MEAL_PLAN_CODE_BED_ONLY = "BD";
    public static final String MEAL_PLAN_CODE_ACC_ONLY = "AO";
    public static final String MEAL_PLAN_CODE_BREAKFAST = "CP";
    public static final String MEAL_PLAN_CODE_BREAKFAST_LUNCH = "TMAP";
    public static final String MEAL_PLAN_CODE_BREAKFAST_DINNER = "SMAP";
    public static final String MEAL_PLAN_CODE_BREAKFAST_LUNCH_OR_DINNER = "MAP";
    public static final String MEAL_PLAN_CODE_ALL_MEALS = "AP";
    public static final String MEAL_PLAN_CODE_ALL_MEALS_AI = "AI";

    public static final String TWO_MEAL_AVAIL = "TWO_MEAL_AVAIL";
    public static final String ALL_MEAL_AVAIL = "ALL_MEAL_AVAIL";

    public static final String ALL_INCLUSIVE = "ALL_INCLUSIVE";
    public static final String SPECIALDEALS = "SPECIALDEALS";

    public static final String LOB_DONATION = "DONATION";
    public static final String INSURANCE_WIDGET = "INSURANCE_WIDGET";
    public static final String INSURANCE_AMOUNT_DISABLED_DATA = "bnplInsuranceAmountDisabled";
    public static final String RSQ_SPLITTER = "e";
    public static final String RSQ_ROOM_SPLITTER = "~";
    public static final String BOOKING_NON_INSTANT_CONFIRMATION = "NON_INSTANT";
    public static final String BOOKING_PNR_KEY = "PNR";
    public static final String BOOKING_STATUS_SUCCESS = "success";
    public static final int HOTEL_CATEGORY_SELECTION_ALGO_VERION_1 = 1;

    public static final String UNDERSCORE = "_";
    public static final String BOOKING_ID = "bookingId";
    public static final String MOBILE_PARAM = "mobile";
    public static final String PAGE_CONTEXT_DETAIL = "DETAIL";
    public static final String PAGE_CONTEXT_REVIEW = "REVIEW";
    public static final String PAGE_CONTEXT_LISTING = "LISTING";
    public static final String PAGE_CONTEXT_LANDING = "LANDING";
    public static final String auth_code = "authCode";
    public static final String SRC_CLIENT = "srcClient";
    public static final String HOTELS_ENTITY_SERVICE = "entity/api/";
    public static final String MOST_RESTRICTED_POLICY = "Y";
    public static final String SPCL_CAT_TYPE_LABEL = "LABEL";
    public static final String HEADER_REGION = "region";
    public static final String HEADER_LANGUAGE = "language";
    public static final String HEADER_CURRENCY = "currency";
    public static final String DEFAULT_CUR_USD = "USD";
    public static final String SOURCE_CON = "srCon";
    public static final String SOURCE_CLIENT = "srcClient";
    public static final String DES_CON = "countryCode";
    public static final String LOCATIONID = "locationId";
    public static final String LOCATIONTYPE = "locationType";
    public static final String CURRENCYCODE = "currencyCode";
    public static final String CONTENT_LENGTH = "content-length";
    public static final String BST_SAFETY_FILTER = "MySafety - Safe and Hygienic Stays";
    public static final String UNRATED_SR = "Unrated";
    public static final String TRAFFIC_SOURCE_SEO = "seo";
    public static final String PERSUASION_TYPE_SEO = "SEO";

    public static final String KID_FRIENDLY = "Kid Friendly";
    public static final String COUPLE_FRIENDLY = "Couple Friendly";
    public static final String STAYCATION = "Staycation Deals";
    public static final String GREATVALUE = "Great Value Deals";
    public static final String MyBiz_Assured = "MyBiz Assured";
    public static final String MMT_Assured = "MMT Assured";
    public static final String MY_BIZ_ASSURED_SECTION = "MYBIZ_ASSURED";
    public static final String NOT_MYBIZ_ASSURED_SHOWN = "NOT_MYBIZ_ASSURED_SHOWN";
    public static final String MYBIZ_SIMILAR_HOTELS = "MYBIZ_SIMILAR_HOTELS";
    public static final String MYBIZ_SIMILAR_TO_DIRECT_HOTEL = "MYBIZ_SIMILAR_TO_DIRECT_HOTEL";

    public static final String CORPBUDGET_DIRECT_HOTEL = "DIRECT_HOTEL";
    public static final String DIRECT_HOTEL = "DIRECT_HOTEL";

    public static final String ADDITIONAL_FEE_SUBTEXT_SEPARATOR = " x ";
    public static final String ADDITIONAL_FEE_SUBTEXT_SPACE = " ";
    public static final String ADDITIONAL_FEE_SUBTEXT_LINE_SEPARATOR = " \n ";
    public static final String ADDITIONAL_FEE_SUBTEXT_ROOMS = "Rooms";
    public static final String ADDITIONAL_FEE_SUBTEXT_ROOM = "Room";

    public static final String STYLE = "style";

    public static final String CTRIP_INS = "INSTANT";
    public static final String CTRIP_NONINS = "NON_INSTANT";
    public static final String EARLY_CHECKIN_CATEGORY = "Early Checkin";

    public static final String SELLABLE_ROOM_TYPE = "Room";
    public static final String SELLABLE_BED_TYPE = "Bed";
    public static final String SELLABLE_ENTIRE_TYPE = "ENTIRE";
    public static final String LISTING_TYPE_ENTIRE = "entire";
    public static final String AMENITIES_OPEN_BRACE = "(";
    public static final String AMENITIES_CLOSING_BRACE = ")";
    public static final String HYPEN = "-";
    public static final String MATTRESS = "mattress";

    public static final String SUPPLIER_INGO = "INGO";
    public static final String HEADER_LAT = "lat";
    public static final String HEADER_LONG = "long";
    public static final String PAS = "PAS";
    public static final String PAH = "PAH";
    public static final String PAH_WITHOUT_CC = "PAH_WITHOUT_CC";
    public static final String PAH_WITH_CC = "PAH_WITH_CC";
    public static final String TRANSLATION_CACHE = "translation-cache";

    public static final String TITLE_MISS = "Miss";
    public static final String TITLE_MS = "Ms";
    public static final String TITLE_MRS = "Mrs";

    public static final String HEADER_CONTENT_TYPE = "Content-Type";
    public static final String HEADER_CONTENT_APPLICATION_JSON = "application/json";

    public static final String HEADER_ACCEPT_TYPE = "Accept";
    public static final String HEADER_CONTENT_APPLICATION_JSON_UTF_8 = "application/json; charset=UTF-8";

    public static final String HEADER_ACCEPT_ENCODING = "Accept-Encoding";
    public static final String HEADER_ACCEPT_ENCODING_GZIP_ENCODING =  "gzip";

    public static final String BLACK_INCLUSION_IDENTIFIER = "BLACK";
    public static final String LOCATION_PERSUAION_PLACEHOLDER_ID_DESKTOP = "DT_CARD_SUBTITLE";
    public static final String LOCATION_PERSUAION_2_PLACEHOLDER_ID = "PC_MIDDLE_2_1";
    public static final String LOCATION_PERSUAION_PLACEHOLDER_ID_APP = "CARD_SUBTITLE";
    public static final String LOCATION_PERSUAION_PLACEHOLDER_ID_APP_CARDV2 = "CARDV2_SUBTITLE";
    public static final String LOCATION_PER_COLOUR_CODE = "#222222";


    public static final String SEO_TEXT_PERSUASION_PLACEHOLDER_ID = "PC_MIDDLE_10";
    public static final String PLACEHOLDER_CARD_SEO = "PLACEHOLDER_CARD_SEO";
    public static final String PLACEHOLDER_SELECT_TOP_R1 = "PLACEHOLDER_SELECT_TOP_R1";
    public static final String PC_SELECT_RIGHT_1 = "PC_SELECT_RIGHT_1";

    public static final String AMENITIES_PLACEHOLDER_ID_DESKTOP = "DT_CARD_LEFT_10";
    public static final String AMENITIES_PLACEHOLDER_ID_APP = "PLACEHOLDER_CARD_M2";

    public static final String TOOL_TIP_SAFETY = "SAFETY";
    public static final String TOOL_TIP_GT = "GTA"; // Ground Transport Access
    public static final String TOOL_TIP_OOP = "OOP";
    public static final String TOOL_TIP_BNPL_AVAIL = "BNPL_AVAIL";
    public static final String TOOL_TIP_FCZPN = "FCZPN";
    public static final String TOOL_TIP_FC = "FREE_CANCELLATION";
    public static final String TOOL_TIP_HOSTEL_STAY = "HOSTEL";
    public static final String TOOL_TIP_VILLA_STAY = "VILLA";
    public static final String TOOL_TIP_HOMESTAY_STAY = "HOMESTAY";
    public static final String TOOL_TIP_COTTAGE_STAY = "COTTAGE";
    public static final String TOOL_TIP_APARTMENT_STAY = "APARTMENT";
    public static final String TOOL_TIP_TYPE_PROPERTY_BENEFITS = "PROPERTY_BENEFITS";
    public static final String LAST_BOOKED_HOTELS = "LAST_BOOKED_HOTELS";
    public static final String RECENTLY_VIEWED_HOTELS = "RECENTLY_VIEWED_HOTELS";
    public static final String ONE_CLICK = "ONE_CLICK";

    public static final String WORKFLOW_PENDING = "pending";
    public static final String MOBGEN_CACHE = "mobgen-cache";

    public static final String CANCELLATION_TYPE_FC = "FC";
    public static final String FREE_CANCELLATION = "FREE_CANCELLATION";

    public static final String POST_BOOKING_CARD = "POST_BOOKING_CARD";
    public static final String CANCELLATION_TYPE_FCZPN = "FCZPN";
    public static final String CANCELLATION_TYPE_NR = "NR";
    public static final String FREE_BREAKFAST = "FREE_BREAKFAST";

    public static final String DEFAULT = "DEFAULT";

    public static final String BREAKFAST = "BREAKFAST";
    public static final String LUNCH = "LUNCH";
    public static final String DINNER = "DINNER";
    public static final String MEAL = "MEAL";
    public static final String USP = "USP";
    public static final String ZPN = "ZPN";
    public static final String WITH="with";
    public static final String OTHERS = "OTHERS";
    public static final String TOOL_TIP_MYBIZ_ASSURED = "TOOL_TIP_MYBIZ_ASSURED";
    public static final String TOOL_TIP_MMT_VALUE_STAY = "TOOL_TIP_MMT_VALUE_STAY";
    public static final String VALUE_STAY_TAG_TITLE = "VALUE_STAY_TAG_TITLE";
    public static final String MMT_VALUE_STAYS = "MMT Value Stays";
    public static final String VALUE_STAY_TAG_TITLE_REVIEW = "VALUE_STAY_TAG_TITLE_REVIEW";
    public static final String VALUE_STAY_TAG_TITLE_THANK_YOU = "VALUE_STAY_TAG_TITLE_THANK_YOU";
    public static final String FUNNEL_SOURCE_HOMESTAY = "HOMESTAY";
    public static final String FUNNEL_SOURCE_HOMESTAY_NEW = "HOMESTAY_HTL";
    public static final String FUNNEL_SOURCE_HOTELS = "HOTELS";

    public static final String FUNNEL_SOURCE_HOSTEL = "HOSTELS";
    public static final String VERTICAL_HOSTEL = "HOSTEL";
    public static final String ZONE = "zone";
    public static final String FUNNEL_DAYUSE = "DAYUSE";
    public static final String AB_EXPT_DISABLE_AMENITIES = "disableAmenities";
    public static final String AB_EXPT_DISABLE_SAVE_VALUE = "disableSaveValue";
    public static final String FUNNEL_SOURCE_DAYUSE = "DAYUSE";
    public static final String FUNNEL_SOURCE_GROUP_BOOKING = "GROUP";

    public static final String FUNNEL_SOURCE_GETAWAY = "GETAWAY";
    public static final String MORE_FILTERS = "MORE_FILTERS";
    public static final String SUGGESTED_FILTERS = "SUGGESTED_FILTERS";
    public static final String OTHER_FILTER_CATEGORY = "OTHER_FILTER_CATEGORY";
    public static final String FUNNEL_SOURCE_CORPBUDGET = "CORPBUDGET";

    // mypartner segment checks for persuasions
    public static final String MYPARTNER_SEGMENT_ID = "1180";
    public static final String MYPARTNER = "MYPARTNER";
    public static final String ROOM_CODE = "roomCode";

    public static final String DISCOUNT_THRESHOLD = "discountThreshold";
    public static final String DISCOUNT_THRESHOLD_PERCENT = "discountPercentThreshold";

    public static final String LUXE_LOCATION_ID = "SFINLUX";
    public static final String LUXE_LOCATION_TYPE = "storefront";
    public static final String IS_LUXE_ONLY_FILTER = "isLuxOnlyFilter";
    public static final String TOOL_TIP_LUXE = "TOOL_TIP_LUXE";
    public static final String IMAGE_TYPE = "IMAGE";
    public static final String VIDEO_TYPE = "VIDEO";
    public static final String BOLD_TYPE = "BOLD";

    public static final String LUXE_ICON_DESKTOP = "https://promos.makemytrip.com/Hotels_product/Luxe/DT_Listing_Luxe2x.png";
    public static final String LUXE_ICON_APPS = "https://promos.makemytrip.com/Hotels_product/Luxe/Detail_luxe2x.png";
    public static final String ALL_INCLUSIVE_PLAN_EXPERIMENT = "AIP";
    public static final String ALL_INCLUSIVE_TRANSFER_EXPERIMENT = "APT";
    public static final String HOMESTAY_PERSUASION_ALC = "ALC";

    public static final String LUXE_PACKAGE = "LUXE_PACKAGE";
    public static final String NON_LUXE_PACKAGE = "NON_LUXE_PACKAGE";
    public static final String MMT_LUXE_PACKAGE = "MMT_LUXE_PACKAGE";
    public static final String MMT_NON_LUXE_PACKAGE = "MMT_NON_LUXE_PACKAGE";
    public static final String LUXURY_HOTELS = "luxury_hotels";

    public static final String SELECT_ROOM_BANNER_TYPE = "RatePlanScroll";
    public static final String SELECT_ROOM_BANNER_BG_COLOR = "#CFEEE5";
    public static final String SELECT_ROOM_BANNER_ICON_URL = "https://promos.makemytrip.com/Hotels_product/Luxe/Tip3x.png";

    public static final String GUESTS_LOVE = "GUESTS_LOVE";
    public static final String ICON_TYPE_LIGHTNING = "LIGHTNING";
    public static final String EXP_HOTEL_ALT_ACCO_FILTER = "HAFC";
    public static final String CLIENT_DESKTOP = "DESKTOP";
    public static final String VIEW_TYPE_FLEX = "flex";
    public static final String EXP_TRUE_VALUE = "T";

    public static final String IMAGES_EXP_ENABLE = "imageExperimentEnable";
    public static final String VIEW_360_IMAGE = "VIEW_360_IMAGE";
    public static final String view360IconUrl ="view.360.icon.url=https://promos.makemytrip.com/Hotels_product/Listing/3603x.png";
    public static final String view360PersuasionIconUrl= "https://promos.makemytrip.com/Hotels_product/Contextual%20Filter/Icons/ezgif.com-gif-to-webp.webp";
    public static final String EXP_GALLERY_V2 = "GALLERYV2";
    public static final Integer listingMediaLimitExp = 20;
    public static final Integer detailGridImageLimit = 6;
    public static final String HOMESTAY_ICON = "https://promos.makemytrip.com/Hotels_product/Contextual%20Filter/Icons/Homestay_icon.png";
    public static final String PROPERTY_TYPE_HOMESTAY = "Homestay";
    public static final String DAYUSE_LATE_CHECKOUT = "DAYUSE_LATE_CHECKOUT";
    public static final String CHECKOUT_MSG = "CHECKOUT_MSG";

    public static final String TRUE = "true";
    public static final String FALSE = "false";

    public static final String PROPERTY_TYPE_HOSTEL = "Hostel";
    public static final String EXP_PL_GI = "PLGI";

    //filter processing components for time logging
    public static final String PROCESS_FILTER_REQUEST_PROCESSOR = "P_FILTER_REQUEST_PROCESSOR";
    public static final String PROCESS_DOWNSTREAM_FILTER_HES_CALL = "D_FILTER_HES_CALL";
    public static final String PROCESS_FILTER_PMS_CONFIGURATION = "P_FILTER_PMS_CONFIGURATION";
    public static final String PROCESS_FILTER_RESPONSE_PROCESS = "P_FILTER_RESPONSE_PROCESS";

    //search-hotels components for time logging
    public static final String PROCESS_SEARCH_REQUEST_PROCESS = "P_SEARCH_REQUEST_PROCESS";
    public static final String PROCESS_SEARCH_COMMON_REQUEST_PROCESS = "P_SEARCH_COMMON_REQUEST_PROCESS";
    public static final String PROCESS_SEARCH_RESPONSE_PROCESS = "P_SEARCH_RESPONSE_PROCESS";

    //search-rooms components for time logging
    public static final String PROCESS_DETAIL_REQUEST_PROCESS = "P_DETAIL_REQUEST_PROCESS";
    public static final String PROCESS_DETAIL_COMMON_REQUEST_PROCESS = "P_DETAIL_COMMON_REQUEST_PROCESS";
    public static final String PROCESS_DETAIL_RESPONSE_PROCESS = "P_DETAIL_RESPONSE_PROCESS";

    //avail-rooms components for time logging
    public static final String PROCESS_REVIEW_REQUEST_PROCESS = "P_REVIEW_REQUEST_PROCESS";
    public static final String PROCESS_REVIEW_COMMON_REQUEST_PROCESS = "P_REVIEW_COMMON_REQUEST_PROCESS";

    public static final String PROCESS_REVIEW_RESPONSE_PROCESS = "P_REVIEW_RESPONSE_PROCESS";


    public static final String EXP_CONTEXTUAL_FILTER = "CRF";

    public static final String AE = "AE";
    public static final String FITS = "Fits";
    public static final String GUEST_PROFILE = "GuestProfile";
    public static final String SAFETY_AND_HYGIENE = "SafetyandHygiene";

    public static final String HASH_SEPARATOR = "#";
    public static final String INCLUSIONS_DEFAULT_DOT_ICON_URL = "https://promos.makemytrip.com/Hotels_product/Persuasion_Icons/Dot.png";
    public static final String INCLUSIONS_DEFAULT_DEFAULT_DOT_ICON_URL = "https://promos.makemytrip.com/Hotels_product/Inclusions/Icons/Default_DefaultDot.png";
    public static final String INCLUSIONS_RIGHT_TICK_ICON_URL = "https://promos.makemytrip.com/Hotels_product/GI/RightTick.png";
    public static final String INCLUSIONS_BULLETS_ICON_URL = "https://promos.makemytrip.com/Hotels_product/GI/bullets.png";
    public static final String INCLUSIONS_PACKAGE_TEXT_COLOUR = "#714D0A";

    public static final String LOCALITY_GROUP = "LOCALITY";

    public static final String URL_PARAM_BASE_SPLITTER = "\\?";
    public static final String CHECK_AVAILBILITY_PARAM = "checkAvailability";

    public static final String REQUEST_SOURCE_KEY = "request-source";
    public static final String REQUEST_SOURCE_SCION = "SCION";
    public static final String AMP_SOURCE_CLIENT_SCION = "&srcClient=SCION";

    public static final String AMP_LENGTH_OF_STAY = "&los=";
    public static final String AMP_ADVANCE_PURCHASE = "&ap=";
    public static final String AMP_ADULT_COUNT = "&adult=";
    public static final String AMP_CHILD_COUNT = "&child=";
    public static final String AMP_PAGINATED = "&paginated=";


    // spaceType Constants
    public static final String BEDROOM = "bedroom";
    public static final String SPACE = " ";
    public static final String LIVING_ROOM = "living_room";

    // homestays filterGroup constant from frontend
    public static final String VILLA_AND_APPT = "VILLA_AND_APPT";
    // homestays filterGroup constant from hotstore
    public static final String ALT_ACCO_PROPERTY = "ALT_ACCO_PROPERTY";
    // homestays filterValue constant from hotstore
    public static final String AltAcco = "AltAcco";

    public static final String BREAK_AMENITIES_BOLD = "<br> <b>AMENITIES:</b> ";

    public static final Integer NINE = 9;

    public static final String PRICE_RATIO = "PRICE_RATIO";
    public static final String VIEW_TYPE_GRAPH = "graph";
    public static final String PRICE_CATEGORY_NAME = "PRICE";
    public static final String PRICE_PER_NIGHT = "PRICE_PER_NIGHT";
    public static final String PRICE_PER_ROOM_PER_NIGHT = "PRICE_PER_ROOM_PER_NIGHT";


    public static final String MYPARTNER_EXPEDIA_PKG_RATE = "MYPARTNER_EXPEDIA_PKG_RATE";
    public static final String PACKAGE_RATE_KEY = "packageRateMapping";
    public static final String TC_CLAUSE_KEY = "t&cMapping";
    public static final String PAY_LATER_CARD_EXP = "payLaterCard";
    public static final String PAY_LATER = "PAY_LATER";
    public static final String RUSH_DEALS = "RUSH_DEALS";
    public static final String FLEXIBLE_CHECKIN = "FLEXIBLE_CHECKIN";
    public static final String HOTEL_CATEGORY_FILTER = "HOTEL_CATEGORY";

    public static final String AM = "AM";
    public static final String PM = "PM";

    public static final String COUPLE_FRIENDLY_ID = "COUPLE_FRIENDLY";
    public static final String PIPE_SEPARATOR = " | ";
    public static final String LOCAL_ID_ACCEPTED = "LOCAL_ID_ACCEPTED";
    public static final String DAYUSE_PARENTHESES_OPEN = " (";
    public static final String DAYUSE_PARENTHESES_CLOSE = ")";
    public static final String DAYUSE_SPACE = " ";
    public static final String IMAGE_TEXT_H = "IMAGE_TEXT_H";
    public static final String DAYUSE_LOCAL_ID = "DAYUSE_LOCAL_ID";

    public static final String CANCELLED = "cancelled";
    public static final String RECALLED = "recalled";

    public static final String PROFESSIONAL = "professional";
    public static final String TRAVELLER = "traveller";
    public static final String NULL_STRING = "null";
    public static final String GCC_EXCLUSIVE = "GCC_EXCLUSIVE";
    public static final String MMT_EXCLUSIVE_IMAGE_URL = "https://promos.makemytrip.com/gcc/Badge_MMTExclusive_DT.png";
    public static final String BEST_PRICE_GUARANTEE = "BEST PRICE GUARANTEE";
    public static final String PENDING_AMOUNT = "Pending Amount";
    public static final String AMOUNT_PAID = "Amount Paid";

    public static final String GROUP_DEALS = "Group Deals";
    public static final String GROUP_BOOKING_TEXT = "groupBooking";
    public static final String NO_MEAL_INCLUSION_REMOVE = "noMealInclusionRemove";
    public static final String PEE = "PEE"; // PEE - Pricing Engine Enabled
    public static final String PEED = "PEED"; // PEED - Pricing Engine Enabled for Details
    public static final String newFilterService = "newFilterService"; // New Filter Service enabled
    public static final String OPTIMIZE_HOSTEL_SELECTION_EXP = "OHS";
    public static final String ENABLE_MERGED_PROPERTY_TYPE = "enableMergedPropertyType";
    public static final String ENABLE_SPOTLIGHT_FILTER = "gi.backend.hotel.default.default.default.giSpotlight_enableFilter";

    public static final String CITY_CODE_MALDIVES = "CTMALDI";
    public static final String TRANSFERS = "Transfers";
    public static final String TRANSFERS_FEE_TEXT_KEY = "TFT";
    public static final String PROPERTY_TYPE = "PROPERTY_TYPE";
    public static final String MERGE_PROPERTY_TYPE = "MERGE_PROPERTY_TYPE";
    public static final String SPOTLIGHT_FILTER = "SPOTLIGHT";

    public static final String ENTIRE_PROPERY = "ENTIRE_PROPERY";

    public static final String PRIVATE_ROOM = "PRIVATE_ROOM";
    public static final String ALTACCO = "AltAcco";
    public static final String BOOK_NOW_AT_0 = "BOOK_NOW_AT_0";
    public static final String BOOK_NOW_AT_1 = "BOOK_NOW_AT_1";
    public static final String ROOM = "room";
    public static final String MALE_GENDER = "male";
    public static final String FEMALE_GENDER = "female";

    public static final String EXP_BNPL_NEW_VARIANT = "bnplNewVariant";
    public static final String EXP_BNPL_ZERO_VARIANT = "bnplZeroVariant";
    public static final String BOOK_NOW_MODAL_DATA_CONFIG = "bookNowModalData";
    public static final String BOOK_NOW_MODAL_HEADING = "heading";
    public static final String BOOK_NOW_MODAL_SUB_HEADING = "subHeading";
    public static final String BOOK_NOW_MODAL_DESCRIPTION = "description";
    public static final String BOOK_NOW_MODAL_BENEFITS = "benefits";
    public static final String BOOK_NOW_MODAL_OKAY_GOT_IT = "cta";
    public static final String BOOK_NOW_TEXT_KEY = "text";
    public static final String BOOK_NOW_PERSUASION_KEY = "bookNowTag";

    // calendarAvailability constant
    public static final String AVAILABLE = "AVAILABLE";
    public static final String NOT_AVAILABLE = "NOT_AVAILABLE";
    public static final String MMT_VALUESTAYS_SUBTYPE = "VALUESTAYS";
    public static final String BOOK_AT_ZERO = "bnplZeroVariant"; // Used for Book@0/1 experiments.

    public static final String BNPLApplicable = "bnpl_ci_exp";
    public static final String enableBnplV2 = "bnpl_v2";
    public static final String newBnplKt = "new_bnpl_kt";
    public static final String flexibleCheckin = "flexible_checkin";
    public static final String rushDetailsExp = "rush_deals";

    public static final String CUMULATIVE_RATING_TEXT = "cumulativeRating";

    public static final String MP_FARE_HOLD = "MP_FARE_HOLD";
    public static final String LOG_HOVER_KEY = "logHover";
    public static final String LOG_HOVER_VALUE = "myp_hold_card_hover";
    public static final String TITLE_SUBTITLE_TOOLTIP = "TITLE_SUBTITLE_TOOLTIP";
    public static final String GET_APPROVAL_ENDPOINT = "cg/get-approvals";

    public static final String METRO = "Metro";
    public static final String BUS = "Bus";
    public static final String METRO_TAG = "METRO_STATION";
    public static final String BUS_TAG = "BUS_STATION";
    public static final String BUS_TERMINAL_TAG = "BUS_TERMINAL";
    public static final String METRO_TEXT = "Metro Access";
    public static final String BUS_TEXT = "Bus Access";

    public static final String LOCATION_PERSUASION_ID = "LOC_PERSUASION_";
    public static final String LOCATION = "LOCATION";
    public static final String STYLE_INLINE_BLOCK = "inlineBlock";
    public static final String NEW_HOTEL_LIST = "NHL";

    public static final String STYLE_DARK_TEXT = "darkText";
    public static final String STYLE_LOCATION = "pc__location";
    public static final String STYLE_BLUE_TEXT = "blueText";
    public static final String CORP_BUDGET_PROPERTIES_TEXT = "CORP_BUDGET_PROPERTIES_TEXT";
    public static final String SIMILAR_HOTELS = "SIMILAR_HOTELS";
    public static final String FILTER_PILL_EXP = "HFC"; //Experiment will be sent by client for this change HTL-38235


    public static final String HIDDEN_GEM = "Hidden Gem";
    public static final String HIDDEN_GEM_CARD = "HIDDENGEM";
    public static final String STAY_TYPE = "STAY_TYPE";
    public static final String STAY_TYPE_HOTEL = "HOTEL";
    public static final String STAY_TYPE_HOMESTAY = "HOMESTAY";
    public static final String ENTIRE_APARTMENT = "Entire Apartment";
    public static final String ENTIRE_APARTMENTS = "Entire Apartments";
    public static final String ENTIRE_SERVICE_APARTMENT = "Entire Serviced Apartment";
    public static final String ENTIRE_SERVICE_APARTMENTS = "Entire Serviced Apartments";
    public static final String ROOM_IN_APARTMENT = "Room in an Apartment";
    public static final String ROOM_IN_SERVICE_APARTMENT = "Room in a Serviced Apartment";

    public static final String TOGGLE_FILTERS_CATEGORY = "TOGGLE_FILTERS";
    public static final String MEAL_PREFERENCE_CATEGORY = "MEAL_PREFERENCES";

    public static final String EXTRA_BED_POLICY_TO_BE_REMOVED = "extrabedpolicy";
    public static final String GEC = "GEC";
    public static final String PACKAGE_RATE = "PACKAGE_RATE";

    /*Persuasions constants for Mypartner- Cashback offer & Hero Offer*/
    public static final String HERO_OFFER_PERSUASION_ICON_TYPE = "icHeroIcon";
    public static final String CASHBACK_OFFER_PERSUASION_ICON_TYPE = "icRewardBonus";
    public static final String CASHBACK_HERO_OFFER_PERSUASION_NODE = "cashbackHeroPersuasion";

    public static final String FUNNEL_SOURCE = "funnel";
    public static final String SHORTSTAYS_FUNNEL="SHORTSTAYS";

    public static final String SEARCH_ROOMS_END_POINT = "cg/search-rooms/";

    public static final String CASHBACK_TO_WALLET = "Cashback to Wallet";

    public static final String PAGE_CONTEXT = "pageContext";

    public static final String SHOW_MMT_RATING_EXP = "showMMTRating";
    public static final String TOTAL_RATING_COUNT = "totalRatingCount";

    public static final String ADDITIONAL_INFORMATION = "Additional Information";

    public static final String VOYAGER_ID = "voyagerid";
    public static final String MMT_HOTEL_ID = "mhotelid";
    public static final String STREET_VIEW_REQ = "streetViewRequired";


    public static final String FLAVOUR = "flavour";
    public static final String VERSION_CODES = "versioncode";

    public static final String ID = "id";

    public static final String TAG_IDS = "tagids";
    public static final String OFFSET = "offset";
    public static final String LIMIT = "limit";

    public static final String STREAK_USER_BALANCE = "User Streak Balance";
    public static final String STREAK_USER_INFO = "User Streak Info";
    public static final String STREAK_EXP_VALUE = "1";
    public static final String STREAK_EXP_NAME = "streaks_hermes";

    //Gostays is hardcoded in HES categories response if HSC giving "key": "GO_STAY","value": "Y" in response.
    public static final String GO_STAYS_CATEGORY = "Gostays";

    public static final String ROOM_SEPARATOR = "-";
    public static final String OCCUPANCY_SEPARATOR = "_";

    public static final String HOTEL_IDS_PARAM = "hotelIds";
    public static final String COUNTRY_CODE_PARAM = "countryCode";
    public static final String VCID_PARAM = "vcId";
    public static final String NETWORK_TYPE_PARAM = "networkType";
    public static final String IMAGE_CATEGORY_PARAM = "imageCategory";
    public static final String BRAND_PARAM = "brand";
    public static final String THUMBNAIL_REQD_PARAM = "isThumbnailRequired";
    public static final String IMAGE_TYPE_PARAM = "imageType";

    public static final String FLYER_TEXT = "flyer";

    // Static uuid used in case of loggedout state for showing production state gostay banners
    public static final String LOGGED_OUT_UUID = "logged_out";

    public static final String OCCUPANCY_PARAMETER = "{occupancy}";

    public static final String OPEN_BOLD_TAG = "<b>";

    public static final String CLOSE_BOLD_TAG = "</b>";

    public static final String SPACE_SINGLE_OCCUPANCY_EXTRA_GUESTS = "SPACE_SINGLE_OCCUPANCY_EXTRA_GUESTS";

    public static final String SPACE_OCCUPANCY_EXTRA_GUESTS = "SPACE_OCCUPANCY_EXTRA_GUESTS";

    public static final String EXTRA_PARAMETER = "{extra}";

    public static final String BULLET_HTML = "\u2022";
    public static final String CHARITY_ID = "CHARITY_ID";
    public static final String CHARITY_ID_V2 = "CHARITY_ID_V2";
    public static final String REDIRECT_THANKYOU = "THANKYOU";
    public static final String REDIRECT_PAYMENT = "PAYMENT";
    public static final String APPVERSIONGOIBIBO = "APPVERSION-GOIBIBO";
    public static final String ALERT_TYPE_VARIANT = "alert_type_variant"; //True for new App Versions configured by Pokus
    public static final String CURRENCY_SYMBOL = "{currency_symbol}";
    public static final String AMOUNT = "{amount}";
    public static final String GOCASH_ICON = "https://gos3.ibcdn.com/waller_icon-1690377440.png";
    public static final String OFFERS_CASHBACK_APPLIED_TEXT = "Offers & Cashback Applied";
    public static final String OFFERS_APPLIED_TEXT = " Offers Applied";

    public static final String PAY_MODE = "PAY_MODE";

    public static final String ICON_URL_PAH_INCLUSION = "https://promos.makemytrip.com/Hotels_product/Persuasion_Icons/Dot.png";
    public static final Integer ROOM_COUNT_GROUPRATE = 5;

    public static final String GROUP_RATES_EXP = "enable_gr";
    public static final String DOM_COUNTRY_CODE = "in";
    public static final String GOTRIBE_KEY = "gotribe";
    public static final String COIN_KEY = "coin";
    public static final String COIN_URL = "https://gos3.ibcdn.com/waller_icon-1690377440.png";
    public static final String FOOTER_BG_COLOR = "#fffde9";
    public static final String FOOTER_GOTRIBE_BG_COLOR = "#fdddd3";

    public static final String FOOTER_GOCASH_TEXT = "Yay! You get ₹%d goCash after completing this booking";

    public static final String FOOTER_GOCASH_NEWUSER_TEXT = "Yay! Since you’re a new user, you'll get %d extra goCash as well as %d goCash after completing this booking";

    public static final String FOOTER_GOCASH_NEWUSER_IH_TEXT = "Yay! First international hotel booking rewards: get %d goCash after booking completion";
    public static final String FOOTER_GOCASH_NEWUSER_DH_TEXT = "Yay! First domestic hotel booking rewards: get %d goCash after booking completion";

    public static final String FOOTER_GOCASH_NEWUSER = "Yay! Since you’re a new user, you'll get %d extra goCash";

    public static final String EARN_GOCASH_INCLUSION = "Earn ₹%d goCash";

    public static final String GOCASH_CAHSBACK_INCLUSION = "₹%d goCash Cashback Included";

    public static final String GOCASH = "GOCASH";

    public static final String GOTRIBE_USER_GOCASH_TEXT = "Book & Earn ₹%d goCash Instantly";
    public static final String GOTRIBE_USER_GOCASH_INSTANT_DISC_TEXT = "₹%d exclusive discount + earn ₹%d goCash";
    public static final String LOG_IN_PERSUASION_KEY = "loginPersuasion";
    public static final String TAJ_CAMPAIGN_PERSUASION_KEY = "TAJ_CAMPAIGN";
    public static final String GIFT_CARD_BENEFIT_TYPE = "Gift_Card";
    public static final String HOTEL_CREDIT_BENEFIT_TYPE = "hotel_credit";

    public static final String HEADER_CITY = "city";
    public static final String BIG_URL = "1920:1080";
    public static final String GALLERY_URL = "634:357";
    public static final String THUMB_URL = "200:113";
    public static final String MOBILE_THUMB_URL = "86:48";

    public static final String FlyerRatesLoggedOutExp = "flyer_web_loggout";
    public static final String WalletExpIH = "gi.backend.hotel.default.default.default.wallet_exp_ih";
    public static final String AMOUNT_PAID_KEY = "AMOUNT_PAID";
    public static final String DUE_AMOUNT_KEY = "DUE_AMOUNT";
    public static final String TOTAL_PRICE_KEY = "TOTAL_PRICE";
    public static final String PAY_NOW_LINK = "PAY_NOW_LINK";

    public static final String TOTAL_PAID_KEY = "TOTAL_PAID";
    public static final String REMAINING_AMOUNT_TO_PAY_KEY = "REMAINING_AMOUNT_TO_PAY";


    public static final String ERROR_GENERIC_STRING_TAGGED_MEDIA = "{\"success\":false,\"data\":{},\"info\":[\"Something went wrong\"]}";

    public static final String DOMESTIC = "DOM";
    public static final String INTERNATIONAL = "INTL";
    public static final String BookAtZero = "Book @ ₹0";
    public static final String BookAtOne = "Book @ ₹1";

    public static final String BLACKDEALS = "BLACKDEALS";
    public static final String CURRENT_USERTIER = "BLACKDEALS_TIER%d";
    public static final String BLACKDEALS_TIER1 = "BLACKDEALS_TIER1";
    public static final String BLACKDEALS_TIER2 = "BLACKDEALS_TIER2";
    public static final String BLACKDEALS_TIER3 = "BLACKDEALS_TIER3";
    public static final String ALT_ACCO_SELECT_ROOM_EXP = "aa_skip_select_room_exp";
    public static final String DEALS = "deals";

    // APP versions for which we are supporting soldOutCallOut.
    public static final int ANDROID_APP_VER_FOR_SOLDOUT_CALLOUT = 2042;
    public static final int IOS_APP_VER_FOR_SOLDOUT_CALLOUT = 1420;


    public static final boolean isRnplEnabled = true;
    public static final int RNPL_BOOKING_CUTOFF_DAYS = 2;
    public static final String CMP = "CMP";
    public static final String TRAFFIC_SOURCE_HPA = "googlehoteldfinder";
    public static final String TRAFFIC_SOURCE_TA = "tripadv";
    public static final String TRAFFIC_SOURCE_TRV = "trivago";
    public static final String TRAFFIC_SOURCE_SKYSCN = "skyscanner";
    public static final String TRAFFIC_SOURCE_SEM_PREFIX = "sem|";
    public static final String TRAFFIC_SOURCE_SEM = "sem";
    public static final String BATHROOMS = "Bathrooms";
    public static final String IMAGE_URL_BATHROOM_TYPE = "https://promos.makemytrip.com/hotelfacilities/bathroom.png";
    public static final String IMAGE_URL_ROOM_TYPE = "https://promos.makemytrip.com/Hotels_product/Hotel_SR/Android/drawable-hdpi/bed.png";
    public static final int SKIP_SELECT_ROOM_THRESHOLD = 3;
    public static final String GOTRIBE_REVAMP_POKUS_EXP_KEY = "blackRevamp";
    public static final String LOS_BENEFITS_EXP_KEY = "losbenefitsbackend";
    public static final String TAJ_GIFT_CARD_EXP_KEY = "gi.backend.hotel.default.review.default.giftcard";
    public static final String GOTRIBE3_POKUS_EXP_KEY = "goTribe3";
    public static final String PLUS = "+";
    public static final String BEDROOM_COUNT = "BEDROOM_COUNT";
    public static final String EXACT_ROOM_RECOMMENDATION = "EXACT_ROOM_RECOMMENDATION";
    public static final String LOGGEDOUT_BNPL_WEB_EXP = "loggedout_web_bnpl";
    public static final String AMENITIES = "amenities";
    public static final String AUGUR_CONFIG_EXP = "exp";
    public static final String AUGUR_CONFIG_CONFIG = "config";
    public static final String AUGUR_CONFIG_OPTION = "option";
    public static final String PIPE_SYMBOL = "|";
    public static final String YET_TO_BE_GENERATED = "Yet To Be Generated";
    public static final String PN_PRICE_SUFFIX = "per night";
    public static final String PPPN_PRICE_SUFFIX = "per person per night";
    public static final String TAXES_AND_FEES_TEXT = "taxes & fees";
    public static final String PIPE_SEPARATOR_WITH_BACKSLASH = " \\| ";
    public static final String SELLABLE_UNIT_ENTIRE = "entire";
    public static final String SELLABLE_UNIT_ROOM = "room";
    public static final String SELLABLE_UNIT_ENTIRE_PMS_KEY = "ENTIRE_PROPERTY";
    public static final String SELLABLE_UNIT_ROOM_PMS_KEY = "PRIVATE_ROOMS";
    public static final String SALE_CAMPAIGN = "SALE_CAMPAIGN";
    public static final String LONG_STAY_BENEFIT = "LONG_STAY_BENEFIT";
    public static final String LOS = "LOS";
    public static final String LOS_BENEFIT_ICON_URL = "https://promos.goibibo.com/Hotels_product/GI/blueoval.png";
    public static final String PRICER_V2_EXP = "pricerV2";
    public static final String PRICER_V2_INTL_EXP = "pricerIntlV2";

    public static final String BEDS_SELLABLE_TYPE_FILTER_CODE = "DORMITORY_BEDS";
    public static final String ROOMS_SELLABLE_TYPE_FILTER_CODE = "PRIVATE_ROOMS";
    public static final String CHUNK = "chunk";
    public static final String VERTICAL_KEYWORD = "vertical";
    public static final String MODE = "mode";
    public static final String VERSION_CODE = "versionCode";
    public static final String PAGEMAKER_CHUNK = "home_carousel";
    public static final String APP = "app";
    public static final String SMARTENGAGE_CONTEXT_LANDING = "hotels_home";
    public static final String SMARTENGAGE_CONTEXT_LISTING = "hotels_srp";
    public static final String DH = "dh";
    public static final String IH = "ih";
    public static final String CONTEXT = "context";
    public static final String STATUS = "status";
    public static final String LIVE = "LIVE";
    public static final String LOB_KEYWORD = "lob";
    public static final String SLOTS = "slots";
    public static final String PARAMS = "params";
    public static final String TEMPLATE_IDS = "template_ids";
    public static final String PAGE = "page";
    public static final String SRP = "srp";

    public static final String NEW_DETAIL_PAGE = "newDetailPage";

    public static final String KIDS = "KIDS";

    // loyalty Card Ids
    public static String ALL_DISPLAY_META_CARD = "ALL_DISPLAY_META_CARD";
    public static String REVIEW_GO_CASH_GOTRIBE1 = "REVIEW_GO_CASH_GOTRIBE1";
    public static String REVIEW_PERSUASION_GOTRIBE1 = "REVIEW_PERSUASION_GOTRIBE1";
    public static String COMMONS_CARD_TEMPLATE_ID = "gi_review_benefits_card";

    public static final String LOCATION_PERSUASION_ICON_TYPE_DAYUSE = "g_location";
    public static final String PRICES_TEXT_DAYUSE = "Prices are Inclusive of taxes and fees";
    public static final String DAY_USE_ONE_NIGHT_PRICE = "Full Night Price";

    public static final String FLEXI_NEW = "flexi_new";
    // HES_CATEGORY_FILTERS are the filter keys present inside filterCategoryMap in HES response.
    public static final Set<String> HES_CATEGORY_FILTERS = new HashSet<>(Collections.singletonList("SUGGESTED_FOR_YOU"));
    public static final String HIGHLIGHTED_AMENITIES_TITLE = "Popular Amenities";

    public static final String PER_NIGHT_SUFFIX = "per night";
    public static final String SINGLE_ROOM_PREFIX = "1 room ";
    public static final String MULTI_ROOM_PREFIX = " rooms ";
    public static final String FOR = "for ";

    public static final String SIMILAR_PROPERTIES_HEADING_DEFAULT = "Properties similar to this property";
    public static final String SIMILAR_PROPERTIES_HEADING_DYNAMIC_TEXT = "Properties similar to ";

    // business identitication constants
    public static final String B2B_EMAIL_VERIFY_GI = "B2B_EMAIL_VERIFY_GI";
    public static final String SAVING_PERC = "{PERCENTAGE}";
    public static final String BUSINESSIDENTIFICATION_CARD_TITLE_TEXT_COLOR = "#FF6D38";
    public static final String BUSINESSIDENTIFICATION_CARD_ICON_URL = "https://go-assets.ibcdn.com/u/GI/images/1719307771017-workBenefitsCard.png";
    public static final String BUSINESSIDENTIFICATION_BG_GRADIENT_START = "#FFFFFF";
    public static final String BUSINESSIDENTIFICATION_BG_GRADIENT_END = "#FFECE5";
    public static final String BUSINESS_IDENTIFY_ENABLE_EXP = "business_identify_enable";
    public static final String SHOW_BUSINESS_IDENTIFICATION_CARD_EXP = "show_business_identification_card";
    public static final String SUGGESTED_FILTER_THRESHOLD = "suggestedFiltersThreshold";

    public static final String INCLUSION_TYPE_LOS = "los";
    public static final String LOS_INCLUSION_TEXT_FORMAT = "%s with <font color='#007E7D'> Long Stay Benefits</font>";

    public static final int DAILY_STEAL_DEAL_TEMPLATE_ID = 9;
    public static final String DAILY_STEAL_DEAL_PERSUASION_ID = "nowOrNeverDealPer";
    public static final String LONG_STAY_GENERIC_CARD_ICON_URL = "https://promos.goibibo.com/Hotels_product/GI/blueoval2.png";

    public static final String REQUEST_IDENTIFIER = "requestId";

    public static final String UGCV2 = "UGCV2";
    public static final String amenitiesGiV2 = "amenitiesGiV2";

    public static final String MEDIAV2 = "MEDIAV2";
    public static final String UGCV2_TRUE_VALUE = "T";

    public static final String REVIEW_RATING_TITLE = "Review & Rating";

    public static final String CG_GI = "CG-GI";

    // Loved by Indians constants
    public static final String ICV2 = "ICV2";
    public static final String TOOL_TIP_INDIANNESS = "TOOL_TIP_INDIANNESS";
    public static final String IMAGE_URL_ROOM_SIZE = "https://gos3.ibcdn.com/roomSizeBlack-1678093548.png";
    public static final String IMAGE_URL_DORM_TYPE = "https://go-assets.ibcdn.com/u/GI/images/1740394393355-door.png";
    public static final String IMAGE_URL_DORM_BATHROOM = "https://go-assets.ibcdn.com/u/GI/images/1740394445905-shower_gi.png";
    public static final String IMAGE_URL_DORM_BED_COUNT = "https://go-assets.ibcdn.com/u/GI/images/1742808540401-bed.png";
    public static final String SQUARE_FEET_V2 = "sq.ft";
    public static final double SQUARE_FEET_TO_SQUARE_METER_CONVERSION_FACTOR = 0.092903;
    public static final String SQUARE_METER = "sq.mt";

    public static final String PROFILE_TYPE = "0";

    public static final String BOOKER_DATE_PATTERN = "yyyy-MM-dd'T'HH:mm:ss";

    public static final int BOOKER_PLUS_DAYS = 1;

    public static final String TITLE_TEXT = "TITLE";

    public static final String HEADER_CONTENT_MULTIPART_FORM_DATA="multipart/form-data";
    public static final String PRIVATE_ROOM_FILTER = "PRIVATE_ROOM_V1";

    public static final String GI_DH = "GI_DH";
    public static final String GI_IH = "GI_IH";
    public static final String POI = "poi";
    public static final String PLACE = "Place";
    public static final String HOTEL_TAJ_GIFT_NAME = "Taj Gift Card";

    public static final String DEFAULT_QUESTION_URL = "DEFAULT_QUESTION_URL";

    public static final String UNSUPPORTED_TITLE = "UNSUPPORTED_TITLE";

    public static final String UNSUPPORTED_SUBTITLE = "UNSUPPORTED_SUBTITLE";

    public static final String UNSUPPORTED_ICON_URL = "UNSUPPORTED_ICON_URL";

    public static final String NO_DATA_FOUND_FOR_UUID = "No data fetched from mojo api for uuid : ";

    // Select Room Revamp constants.
    public static final String SELECT_ROOM_REVAMP_EXP_KEY = "SRV2";
    public static final String SELECT_ROOM_V2_SKIP_EXP_KEY = "SRSKIP";
    public static final String EXP_KEY_EXACT_ROOM_SEARCH = "exactRoomSearch";
    public static final int SELECT_ROOM_REVAMP_INCLUSIONS_MAX_COUNT = 4;
    public static final int SIGNATURE_AMENITIES_SELECT_ROOM_COUNT = 2;
    public static final String SIGNATURE_AMENITIES = "SIGNATURE_AMENITIES";
    public static final String BACK_SLASH_N = "\n";
    public static final String GO_TRIBE_INCLUSION_CODE = "GO_TRIBE";
    public static final String LOS_INCLUSION_CODE = "LOS";
    public static final List<String> GO_TRIBE_UPGRADE_CATEGORIES = Arrays.asList(
            InclusionCategory.ROOM_UPGRADE.getName(), InclusionCategory.MEAL_UPGRADE.getName());
    public static final List<String> NO_MEAL_PLAN_CODES = Arrays.asList(
            MEAL_PLAN_CODE_ROOM_ONLY, MEAL_PLAN_CODE_BED_ONLY, MEAL_PLAN_CODE_ACC_ONLY
    );
    public static final String CANCELLATION_DATE_PLACEHOLDER = "{cancellationDate}";
    public static final String ROOMS_COMBO ="ROOMS_COMBO";
    public static final String SINGLE_ = "SINGLE_";
    public static final String PLURAL_ = "PLURAL_";
    public static final String _FC = "_FC";
    public static final String _NR = "_NR";
    public static final String COMBO_TITLE= "COMBO_TITLE_";
    public static final String EXACT_ROOM_VALUE = "exactRoom";

    public static final String X_PERCENT_SELL_ON = "xPercentSellOn";
    public static final String X_PERCENT_SELL_ON_TEXT = "def_selling_true";
    public static final String X_PERCENT_SELL_OFF_TEXT = "def_selling_false";


    public static final String DEEPLINK_FILTER_DATA = "filterData";
    public static final String DEEPLINK_FILTER_GROUP_SPLITTER = "^";
    public static final String AND_SEPARATOR = "&";
    public static final String RSC = "rsc";

    public static final String LOCUSTYPE_URL_PARAM = "locusType";
    public static final String LOCUSID_URL_PARAM = "locusId";
    public static final String SEARCHTEXT_URL_PARAM = "searchText";
    public static final String STAYCATION_FILTER_URL_PARAM = "staycation";

    public static final String PR_SEPARATOR = "=";
    public static final String PIPE_SPLITTER = "|";
    public static final String FUNNEL_NAME = "funnelName";
    public static final String MPN = "mpn";

    public static final String PARENT_LOCATION_ID = "parentLocId";
    public static final String PARENT_LOCATION_type = "parentLocType";
    public static final String REGION_URL_PARAM = "region";
    public static final String HYPHEN = "-";
    public static final String MM_AREA_TAG = "mmAreaTag";
    public static final String MM_POI_TAG = "mmPoiTag";

    public static final String PIPE_UNICODE = "%7C";
    public static final String SPACE_UNICODE = "%20";


    public static final String ROOM_STAY_QUAL = "roomStayQualifier";
    public static final String AP = "AP";
    public static final String HOTEL_CATEGORY = "hotelCategory";


    public static final String CHECK_IN = "checkin";
    public static final String CHECK_OUT = "checkout";

    public static final String COMPONENT = "cmp";
    public static final String APP_SHARE_HOT_DETAILS = "hotelAppShareNew";

    public static final String LOCUS_ID = "locusId";
    public static final String LOCUS_LOCATION_TYPE = "locusType";
    public static final String CITY = "city";
    public static final String WALLET_REGION_IND = "IN";
    public static final String VIEW_TYPE = "viewType";
    public static final String LINKED_RATE_EXPERIMENT_NR = "showNRPlan";
    public static final String LINKED_RATE_EXP_ZERO_VARIANT = "0";
    public static final String LINKED_RATE_EXP_ONE_VARIANT = "1";
    public static final String LINKED_RATE_EXP_TWO_VARIANT = "2";
    public static final String LUCKY = "LUCKY";

    public static final String UPSELL = "UPSELL";
    public static final String DOWNSELL = "DOWNSELL";
    public static final String TICKTOCKDEALS = "tickTockDeals";
    public static final String IS_UGC_V2 = "isUGCV2";

    public static final String LOCATION_CARD_V2 = "locationCardV2";

    public static final String COMBINED_OTA_FLOW = "combineOTAFlow";
    public static final String MMT_RATINGS_ON_GI = "showmmtratings";

    public static final String LINKEDRATE_FCNR = "CANCELLATION_POLICY_NR";

    public static final String BRAND_FILTER = "BRAND_FILTER";

    public static final String TRAVELLER_IMPRESSIONS_EXP = "TravellerImpressions";

    public static final String HEADER_DEVICE_ID = "device-id";
    public static final String HEADER_OAUTH_GOIBIBO = "oauth-goibibo";
    public static final String HEADER_DEVICE_ID_LOAD_PROGRAM_API = "deviceId";
    public static final String HEADER_OAUTH_GOIBIBO_LOAD_PROGRAM_API = "giAuth";

    public static final String MULTIFILTER_COROUSAL = "multifilterCorousal";

    /**
     * overriding click url and view url null for GI SL tracking for specific android and iOS versions handling, to push them towards old tracking system for those eparticular builds.
     */
    public static final String GI_SL_TRACKING_PARITY = "giSLTrackingOverwrite";
}

package com.mmt.hotels.clientgateway.enums;

public enum ExperimentKeys {
    GOTRIBE_REVAMP("blackRevamp"),
    EXP_SRRP("SRRP"), //client sends us this flag to identify if they need to show the new rate plan design at their end (to be in sync with backend) HTL-37514
    EXP_RATE_PLAN_REDESIGN("ratePlanRedesign"), //this is a backend key to know if we need to show new rateplan design for HTL-37514
    EXP_WALLET("wallet_exp"),
    goTribe3("goTribe3"),
    EXP_WALLET_IH("gi.backend.hotel.default.default.default.wallet_exp_ih"),
    CHARITY_NEW("charity_new"),
    HCARDV2("HCARDV2"),
    EXP_ORCHESTRATOR_V2("orchestrator_v2"),
    EXP_ORCHESTRATOR_V2_APPS("orchestratorV2"),
    privateRoomFilter("privateRoomFilter"),
    room<PERSON>psell("roomUpsellCG"),
    EXP_DAYUSE_ORCHESTRATOR_V2("dayuse_orchestratorV2"),
    PILGR_IMAGE_BED_INFO("pilgrimageBedInfo"),
    MMT_RATINGS_ON_GI("showmmtratings");

    private String key;

    ExperimentKeys(String key){
        this.key = key;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }
}
